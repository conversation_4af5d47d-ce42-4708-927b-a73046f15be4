项目代码提取结果
提取时间: 2025-07-11 16:10:20.528379
总行数: 7002
处理文件数: 55
================================================================================

处理的文件列表:
  .eslintrc.js (6 行)
  .prettierrc.js (20 行)
  docker-compose.yml (39 行)
  extract_code.py (177 行)
  markdown_converter.py (71 行)
  .vscode\launch.json (23 行)
  agent_server\wiseAgent_server\langgraph.json (7 行)
  agent_server\wiseAgent_server\.github\workflows\integration-tests.yml (34 行)
  agent_server\wiseAgent_server\.github\workflows\unit-tests.yml (49 行)
  agent_server\wiseAgent_server\src\agent\client_example.py (36 行)
  agent_server\wiseAgent_server\src\agent\graph.py (98 行)
  agent_server\wiseAgent_server\src\agent\__init__.py (6 行)
  agent_server\wiseAgent_server\tests\conftest.py (4 行)
  agent_server\wiseAgent_server\tests\integration_tests\test_graph.py (8 行)
  agent_server\wiseAgent_server\tests\integration_tests\__init__.py (1 行)
  agent_server\wiseAgent_server\tests\unit_tests\test_configuration.py (4 行)
  agent_server\wiseAgent_server\tests\unit_tests\__init__.py (1 行)
  api\ServerAPI_v1\xiamenInternationalBank\docker-compose.yml (39 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\chat.py (95 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\database.py (27 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\embedTest.py (16 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\initsql.py (141 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\knowledge.py (119 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\knowledge_base_config.py (53 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\langgraphchat.py (67 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\llmapiClient.py (252 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\logging_config.py (18 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\main.py (35 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\models.py (108 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\pgdb.py (60 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\pg_retriever.py (52 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\utils.py (22 行)
  api\ServerAPI_v1\xiamenInternationalBank\app\xmconfig.py (71 行)
  api\ServerAPI_v1\xiamenInternationalBank\tests\test_api.py (265 行)
  backend\docker-compose.yml (60 行)
  backend\old-docker-compose.yml (46 行)
  backend\app\conftest.py (14 行)
  backend\app\main.py (144 行)
  backend\app\role_tree.py (1 行)
  backend\app\agent\agents.py (28 行)
  backend\app\agent\agent_router.py (151 行)
  backend\app\agent\chatbot.py (39 行)
  backend\app\agent\daliy_his_weixin.py (69 行)
  backend\app\agent\daliy_schedule_weixin.py (75 行)
  backend\app\agent\financialPublicOpinionReport_weixin.py (732 行)
  backend\app\agent\financialPublicOpinionReport_weixin_V2.py (977 行)
  backend\app\agent\financialPublicOpinionReport_weixin_V3.py (1061 行)
  backend\app\agent\financialPublicOpinionReport_weixin_V4.py (1201 行)
  backend\app\agent\financialPublicOpinionReport_weixin_V5.1.py (182 行)
  backend\app\agent\core\llm.py (34 行)
  backend\app\agent\core\vectorstore.py (144 行)
  backend\app\agent\schema\schema.py (87 行)
  backend\app\agent\schema\task_data.py (44 行)
  backend\app\agent\schema\__init__.py (2 行)
  funTest\rag_01\backend\app\config.py (1 行)

================================================================================

============================================================
文件: .eslintrc.js
============================================================

module.exports = {
  extends: [require.resolve('@umijs/lint/dist/config/eslint')],
  globals: {
    page: true,
    REACT_APP_ENV: true,
  },
};

============================================================
文件: .prettierrc.js
============================================================

module.exports = {
  singleQuote: true,
  trailingComma: 'all',
  printWidth: 100,
  proseWrap: 'never',
  endOfLine: 'lf',
  overrides: [
    {
      files: '.prettierrc',
      options: {
        parser: 'json',
      },
    },
    {
      files: 'document.ejs',
      options: {
        parser: 'html',
      },
    },
  ],
};

============================================================
文件: docker-compose.yml
============================================================

version: '1.0'
services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
    image: preloan-assistant-api
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: preloan-assistant-api
    restart: always
    environment:
      - ENV_MODE=prod
    command: uvicorn app.main:app --host 0.0.0.0 --port 8800
    ports:
      - "8800:8800"
    env_file:
      - .env.prod
    volumes:
      - ./static:/app/static
    depends_on:
      - mongodb
    networks:
      - app-network
  mongodb:
    image: mongo:4.4
    container_name: preloan-assistant-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
volumes:
  mongodb_data:
networks:
  app-network:
    driver: bridge 

============================================================
文件: extract_code.py
============================================================

"""
代码提取工具 - 从项目中提取代码并去掉注释
"""
importos
importre
importast
importtokenize
fromioimportStringIO
frompathlibimportPath
classCodeExtractor:
    def__init__(self):
        self.code_extensions={'.py':self.remove_python_comments,'.js':self.remove_js_comments,'.ts':self.remove_js_comments,'.jsx':self.remove_js_comments,'.tsx':self.remove_js_comments,'.java':self.remove_java_comments,'.cpp':self.remove_cpp_comments,'.c':self.remove_cpp_comments,'.h':self.remove_cpp_comments,'.hpp':self.remove_cpp_comments,'.go':self.remove_go_comments,'.rs':self.remove_rust_comments,'.php':self.remove_php_comments,'.rb':self.remove_ruby_comments,'.sql':self.remove_sql_comments,'.sh':self.remove_shell_comments,'.yaml':self.remove_yaml_comments,'.yml':self.remove_yaml_comments,'.json':None,}
self.exclude_dirs={'__pycache__','.git','.svn','node_modules','venv','env','.venv','dist','build','.pytest_cache','.mypy_cache','static','uploads','temp_downloads','data_images'}
self.exclude_files={'.pyc','.pyo','.pyd','.so','.dll','.exe','.bin','.log','.tmp','.cache','.DS_Store','Thumbs.db'}
defremove_python_comments(self,code):
        """去掉Python代码中的注释"""
try:
            result=[]
tokens=tokenize.generate_tokens(StringIO(code).readline)
fortokenintokens:
                iftoken.typenotin(tokenize.COMMENT,tokenize.NL):
                    iftoken.type==tokenize.NEWLINE:
                        result.append('\n')
eliftoken.type!=tokenize.ENCODING:
                        result.append(token.string)
return''.join(result)
except:
            lines=code.split('\n')
result=[]
in_multiline_string=False
string_delimiter=None
forlineinlines:
                ifnotline.strip():
                    continue
ifline.strip().startswith('#'):
                    continue
if'#'inlineandnotin_multiline_string:
                    quote_count=line.count('"')+line.count("'")
ifquote_count%2==0:
                        comment_pos=line.find('#')
ifcomment_pos>0:
                            line=line[:comment_pos].rstrip()
ifline.strip():
                    result.append(line)
return'\n'.join(result)
defremove_js_comments(self,code):
        """去掉JavaScript/TypeScript代码中的注释"""
code=re.sub(r'//.*$','',code,flags=re.MULTILINE)
code=re.sub(r'/\*.*?\*/','',code,flags=re.DOTALL)
lines=[lineforlineincode.split('\n')ifline.strip()]
return'\n'.join(lines)
defremove_java_comments(self,code):
        """去掉Java代码中的注释"""
returnself.remove_js_comments(code)
defremove_cpp_comments(self,code):
        """去掉C/C++代码中的注释"""
returnself.remove_js_comments(code)
defremove_go_comments(self,code):
        """去掉Go代码中的注释"""
returnself.remove_js_comments(code)
defremove_rust_comments(self,code):
        """去掉Rust代码中的注释"""
returnself.remove_js_comments(code)
defremove_php_comments(self,code):
        """去掉PHP代码中的注释"""
code=re.sub(r'//.*$','',code,flags=re.MULTILINE)
code=re.sub(r'#.*$','',code,flags=re.MULTILINE)
code=re.sub(r'/\*.*?\*/','',code,flags=re.DOTALL)
lines=[lineforlineincode.split('\n')ifline.strip()]
return'\n'.join(lines)
defremove_ruby_comments(self,code):
        """去掉Ruby代码中的注释"""
code=re.sub(r'#.*$','',code,flags=re.MULTILINE)
code=re.sub(r'=begin.*?=end','',code,flags=re.DOTALL)
lines=[lineforlineincode.split('\n')ifline.strip()]
return'\n'.join(lines)
defremove_sql_comments(self,code):
        """去掉SQL代码中的注释"""
code=re.sub(r'--.*$','',code,flags=re.MULTILINE)
code=re.sub(r'/\*.*?\*/','',code,flags=re.DOTALL)
lines=[lineforlineincode.split('\n')ifline.strip()]
return'\n'.join(lines)
defremove_shell_comments(self,code):
        """去掉Shell脚本中的注释"""
code=re.sub(r'#.*$','',code,flags=re.MULTILINE)
lines=[lineforlineincode.split('\n')ifline.strip()]
return'\n'.join(lines)
defremove_yaml_comments(self,code):
        """去掉YAML文件中的注释"""
returnself.remove_shell_comments(code)
defshould_exclude_dir(self,dir_path):
        """检查是否应该排除目录"""
dir_name=os.path.basename(dir_path)
returndir_nameinself.exclude_dirs
defshould_exclude_file(self,file_path):
        """检查是否应该排除文件"""
file_ext=os.path.splitext(file_path)[1].lower()
returnfile_extinself.exclude_files
defextract_code_from_file(self,file_path):
        """从单个文件中提取代码"""
try:
            withopen(file_path,'r',encoding='utf-8',errors='ignore')asf:
                content=f.read()
file_ext=os.path.splitext(file_path)[1].lower()
iffile_extinself.code_extensions:
                processor=self.code_extensions[file_ext]
ifprocessor:
                    content=processor(content)
returncontent
returnNone
exceptExceptionase:
            print(f"Error reading file {file_path}: {e}")
returnNone
defextract_code_from_directory(self,root_dir,target_lines=7000):
        """从目录中提取代码"""
all_code=[]
total_lines=0
processed_files=[]
forroot,dirs,filesinos.walk(root_dir):
            dirs[:]=[dfordindirsifnotself.should_exclude_dir(os.path.join(root,d))]
forfileinfiles:
                iftotal_lines>=target_lines:
                    break
file_path=os.path.join(root,file)
ifself.should_exclude_file(file_path):
                    continue
file_ext=os.path.splitext(file)[1].lower()
iffile_extnotinself.code_extensions:
                    continue
code=self.extract_code_from_file(file_path)
ifcodeandcode.strip():
                    relative_path=os.path.relpath(file_path,root_dir)
file_header=f"\n{'='*60}\n文件: {relative_path}\n{'='*60}\n"
code_lines=code.count('\n')
iftotal_lines+code_lines<=target_lines:
                        all_code.append(file_header)
all_code.append(code)
total_lines+=code_lines+4
processed_files.append((relative_path,code_lines))
else:
                        remaining_lines=target_lines-total_lines-4
ifremaining_lines>10:
                            truncated_code='\n'.join(code.split('\n')[:remaining_lines])
all_code.append(file_header)
all_code.append(truncated_code)
all_code.append(f"\n... (文件被截断，原文件共{code_lines}行) ...\n")
processed_files.append((relative_path,remaining_lines))
break
iftotal_lines>=target_lines:
                break
return'\n'.join(all_code),processed_files,total_lines
defmain():
    extractor=CodeExtractor()
root_dir='.'
print("开始提取代码...")
code_content,processed_files,total_lines=extractor.extract_code_from_directory(root_dir,7000)
output_file='extracted_code.txt'
withopen(output_file,'w',encoding='utf-8')asf:
        f.write(f"项目代码提取结果\n")
f.write(f"提取时间: {__import__('datetime').datetime.now()}\n")
f.write(f"总行数: {total_lines}\n")
f.write(f"处理文件数: {len(processed_files)}\n")
f.write("="*80+"\n\n")
f.write("处理的文件列表:\n")
forfile_path,linesinprocessed_files:
            f.write(f"  {file_path} ({lines} 行)\n")
f.write("\n"+"="*80+"\n")
f.write(code_content)
print(f"代码提取完成!")
print(f"输出文件: {output_file}")
print(f"总行数: {total_lines}")
print(f"处理文件数: {len(processed_files)}")
print("\n处理的文件:")
forfile_path,linesinprocessed_files:
        print(f"  {file_path} ({lines} 行)")
if__name__=="__main__":
    main()


============================================================
文件: markdown_converter.py
============================================================

importsubprocess
importjson
importos
fromtypingimportOptional,Dict,Any
importasyncio
frommodelcontextprotocolimportMultiServerMCPClient
classMarkdownConverter:
    def__init__(self,use_docker:bool=False,mount_dir:Optional[str]=None):
        """
        初始化 MarkdownConverter
        
        Args:
            use_docker (bool): 是否使用 Docker 运行
            mount_dir (Optional[str]): 如果需要挂载本地目录，指定目录路径
        """
self.use_docker=use_docker
self.mount_dir=mount_dir
self._process=None
asyncdefstart_server(self,port:int=3001)->None:
        """
        启动 markitdown-mcp 服务器
        
        Args:
            port (int): SSE 服务器端口
        """
ifself.use_docker:
            cmd=["docker","run","--rm","-i"]
ifself.mount_dir:
                cmd.extend(["-v",f"{self.mount_dir}:/workdir"])
cmd.extend(["-p",f"{port}:{port}"])
cmd.extend(["markitdown-mcp:latest","--sse","--host","0.0.0.0","--port",str(port)])
else:
            cmd=["markitdown-mcp","--sse","--host","0.0.0.0","--port",str(port)]
self._process=awaitasyncio.create_subprocess_exec(*cmd,stdout=asyncio.subprocess.PIPE,stderr=asyncio.subprocess.PIPE)
awaitasyncio.sleep(2)
asyncdefstop_server(self)->None:
        """停止 markitdown-mcp 服务器"""
ifself._process:
            self._process.terminate()
awaitself._process.wait()
asyncdefconvert_to_markdown(self,uri:str)->str:
        """
        将指定 URI 的内容转换为 Markdown 格式
        
        Args:
            uri (str): 要转换的 URI，可以是 http、https、file 或 data URI
            
        Returns:
            str: 转换后的 Markdown 内容
        """
try:
            input_data={"tool":"convert_to_markdown","parameters":{"uri":uri}}
asyncwithMultiServerMCPClient({"markitdown":{"url":"http://localhost:3001/sse","transport":"sse"}})asclient:
                tools=client.get_tools()
result=awaittools["markitdown.convert_to_markdown"].invoke(uri)
returnresult
exceptExceptionase:
            raiseException(f"执行转换时出错: {str(e)}")
asyncdefmain():
    converter=MarkdownConverter(use_docker=True,mount_dir="/path/to/your/data")
try:
        awaitconverter.start_server()
result=awaitconverter.convert_to_markdown("file:///workdir/example.txt")
print("转换结果:")
print(result)
exceptExceptionase:
        print(f"错误: {str(e)}")
finally:
        awaitconverter.stop_server()
if__name__=="__main__":
    asyncio.run(main())


============================================================
文件: .vscode\launch.json
============================================================

{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Debug backend.app.main",
            "type": "debugpy",
            "request": "launch",
            // "program": "${workspaceFolder}/backend/app/main.py", // 确保路径指向你的main.py
            "cwd": "${workspaceFolder}/backend", // 设置当前工作目录到backend
            "module": "uvicorn",
            "args": [
                "app.main:app",
                "--host", "0.0.0.0",
                "--port", "8800",
                "--reload"
            ],
            "envFile": "${workspaceFolder}/backend/.env.testing", // 如果有环境变量文件的话，请相应调整路径
            "justMyCode": true
        }
    ]
}

============================================================
文件: agent_server\wiseAgent_server\langgraph.json
============================================================

{
  "dependencies": ["."],
  "graphs": {
    "agent": "./src/agent/graph.py:graph"
  },
  "env": ".env"
}


============================================================
文件: agent_server\wiseAgent_server\.github\workflows\integration-tests.yml
============================================================

name: Integration Tests
on:
  schedule:
    - cron: "37 14 * * *" 
  workflow_dispatch: 
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  integration-tests:
    name: Integration Tests
    strategy:
      matrix:
        os: [ubuntu-latest]
        python-version: ["3.11", "3.12"]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          uv venv
          uv pip install -r pyproject.toml
          uv pip install -U pytest-asyncio
      - name: Run integration tests
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
          LANGSMITH_TRACING: true
        run: |
          uv run pytest tests/integration_tests

============================================================
文件: agent_server\wiseAgent_server\.github\workflows\unit-tests.yml
============================================================

name: CI
on:
  push:
    branches: ["main"]
  pull_request:
  workflow_dispatch: 
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  unit-tests:
    name: Unit Tests
    strategy:
      matrix:
        os: [ubuntu-latest]
        python-version: ["3.11", "3.12"]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          uv venv
          uv pip install -r pyproject.toml
      - name: Lint with ruff
        run: |
          uv pip install ruff
          uv run ruff check .
      - name: Lint with mypy
        run: |
          uv pip install mypy
          uv run mypy --strict src/
      - name: Check README spelling
        uses: codespell-project/actions-codespell@v2
        with:
          ignore_words_file: .codespellignore
          path: README.md
      - name: Check code spelling
        uses: codespell-project/actions-codespell@v2
        with:
          ignore_words_file: .codespellignore
          path: src/
      - name: Run tests with pytest
        run: |
          uv pip install pytest
          uv run pytest tests/unit_tests

============================================================
文件: agent_server\wiseAgent_server\src\agent\client_example.py
============================================================

"""金融风险分析代理的客户端示例。

展示如何通过API调用传入参数，如数据库连接信息和文件路径。
"""
importrequests
importjson
importos
fromdotenvimportload_dotenv
load_dotenv()
BASE_URL="http://localhost:8000"
definvoke_agent(query:str,config:dict=None)->dict:
    """调用风险分析代理"""
endpoint=f"{BASE_URL}/agent/invoke"
payload={"inputs":{"query":query,"messages":[]},"config":{"configurable":configor{}}}
try:
        response=requests.post(endpoint,headers={"Content-Type":"application/json"},data=json.dumps(payload))
response.raise_for_status()
returnresponse.json()
exceptrequests.exceptions.RequestExceptionase:
        print(f"请求错误: {e}")
return{"error":str(e)}
defmain():
    """主函数"""
openai_api_key=os.getenv("OPENAI_API_KEY")
ifnotopenai_api_key:
        print("警告: 未找到OPENAI_API_KEY环境变量")
print("\n=== 示例1: 带数据库和文件路径的请求 ===")
config1={"db_url":"postgres://localhost:5432/finance_db","db_username":"financial_analyst","db_password":"secure_password","risk_policy_path":"/path/to/risk_policy.txt","model_name":"gpt-3.5-turbo","temperature":0.3,"api_key":openai_api_key}
result1=invoke_agent("分析我们数据库中客户的信用风险，特别关注贷款额度超过10万的客户",config1)
print(f"结果:\n{json.dumps(result1,ensure_ascii=False,indent=2)}")
print("\n=== 示例2: 仅提供API密钥的请求 ===")
config2={"api_key":openai_api_key}
result2=invoke_agent("评估张三、李四和王五的信用风险",config2)
print(f"结果:\n{json.dumps(result2,ensure_ascii=False,indent=2)}")
if__name__=="__main__":
    main()


============================================================
文件: agent_server\wiseAgent_server\src\agent\graph.py
============================================================

"""LangGraph 金融风险分析代理。

该图支持通过API参数传递数据库连接信息和文件路径，实现客户信用风险分析。
"""
from__future__importannotations
fromtypingimportDict,List,Optional,Any,TypedDict
importos
fromlangchain_core.messagesimportHumanMessage,AIMessage,SystemMessage
fromlangchain_openaiimportChatOpenAI
fromlanggraph.graphimportStateGraph,END
classConfiguration(TypedDict):
    """代理的可配置参数。
    
    可以在创建助手或调用图时设置这些参数。
    """
db_url:Optional[str]
db_username:Optional[str]
db_password:Optional[str]
risk_policy_path:Optional[str]
model_name:Optional[str]
temperature:Optional[float]
api_key:Optional[str]
classState(TypedDict):
    """代理的输入状态。
    
    定义传入数据的初始结构。
    """
query:str
db_results:Optional[Dict[str,Any]]
policy_data:Optional[Dict[str,Any]]
analysis:Optional[str]
messages:List[Dict[str,str]]
defquery_database(config:Configuration,query:str)->Dict[str,Any]:
    """从数据库获取客户信息（模拟）"""
print(f"连接到数据库: {config.get('db_url','未提供URL')}")
print(f"使用用户名: {config.get('db_username','未提供用户名')}")
if"客户"inqueryor"信用"inqueryor"风险"inquery:
        return{"customers":[{"id":1,"name":"张三","credit_score":780,"loan_amount":50000,"risk_level":"低"},{"id":2,"name":"李四","credit_score":650,"loan_amount":100000,"risk_level":"中"},{"id":3,"name":"王五","credit_score":520,"loan_amount":200000,"risk_level":"高"}]}
return{"result":"未找到相关数据"}
defread_policy_file(file_path:str)->Dict[str,Any]:
    """读取风险政策文件（模拟）"""
print(f"读取文件: {file_path}")
iffile_pathand("policy"infile_path.lower()or"风险"infile_path):
        return{"policies":["信用分数低于600为高风险客户，需要特别审查","信用分数600-700为中等风险客户，贷款额度不超过10万","信用分数高于700为低风险客户，可以提供更优惠的利率"]}
return{"warning":"未找到风险政策文件或文件路径无效"}
asyncdefretrieve_customer_data(state:State,config:Dict[str,Any])->State:
    """从数据库获取客户数据的节点"""
query=state["query"]
try:
        db_results=query_database(config["configurable"],query)
return{**state,"db_results":db_results}
exceptExceptionase:
        return{**state,"db_results":{"error":f"数据库查询失败: {str(e)}"}}
asyncdefretrieve_risk_policy(state:State,config:Dict[str,Any])->State:
    """获取风险政策的节点"""
try:
        file_path=config["configurable"].get("risk_policy_path","")
iffile_path:
            policy_data=read_policy_file(file_path)
else:
            policy_data={"warning":"未提供风险政策文件路径"}
return{**state,"policy_data":policy_data}
exceptExceptionase:
        return{**state,"policy_data":{"error":f"读取风险政策失败: {str(e)}"}}
asyncdefanalyze_risk(state:State,config:Dict[str,Any])->State:
    """分析客户风险的节点"""
query=state["query"]
db_results=state.get("db_results",{})
policy_data=state.get("policy_data",{})
model_name=config["configurable"].get("model_name","gpt-3.5-turbo")
temperature=config["configurable"].get("temperature",0.7)
api_key=config["configurable"].get("api_key")oros.getenv("OPENAI_API_KEY")
messages=state.get("messages",[])
try:
        ifnotapi_key:
            return{**state,"analysis":"错误: 未提供OpenAI API密钥","messages":messages}
llm=ChatOpenAI(model=model_name,temperature=temperature,openai_api_key=api_key)
system_message=SystemMessage(content="你是一位资深的金融风险分析师，专门评估客户的信用风险。请基于提供的客户数据和风险政策，提供专业的风险评估和建议。")
user_content=f"""
        查询: {query}
        
        客户数据: {db_results}
        
        风险政策: {policy_data}
        
        请分析这些客户的信用风险，并根据风险政策提供评估和建议。
        """
human_message=HumanMessage(content=user_content)
response=awaitllm.ainvoke([system_message,human_message])
analysis=response.content
new_messages=messages+[{"role":"system","content":system_message.content},{"role":"user","content":user_content},{"role":"assistant","content":analysis}]
return{**state,"analysis":analysis,"messages":new_messages}
exceptExceptionase:
        return{**state,"analysis":f"分析风险时出错: {str(e)}","messages":messages}
asyncdefdecide_next_step(state:State,config:Dict[str,Any])->str:
    """决定下一步操作"""
returnEND
graph=(StateGraph(State,config_schema=Configuration).add_node("retrieve_customer_data",retrieve_customer_data).add_node("retrieve_risk_policy",retrieve_risk_policy).add_node("analyze_risk",analyze_risk).set_entry_point("retrieve_customer_data").add_edge("retrieve_customer_data","retrieve_risk_policy").add_edge("retrieve_risk_policy","analyze_risk").add_edge("analyze_risk",decide_next_step).compile(name="金融风险分析代理"))


============================================================
文件: agent_server\wiseAgent_server\src\agent\__init__.py
============================================================

"""New LangGraph Agent.

This module defines a custom graph.
"""
fromagent.graphimportgraph
__all__=["graph"]


============================================================
文件: agent_server\wiseAgent_server\tests\conftest.py
============================================================

importpytest
@pytest.fixture(scope="session")
defanyio_backend():
    return"asyncio"


============================================================
文件: agent_server\wiseAgent_server\tests\integration_tests\test_graph.py
============================================================

importpytest
fromagentimportgraph
pytestmark=pytest.mark.anyio
@pytest.mark.langsmith
asyncdeftest_agent_simple_passthrough()->None:
    inputs={"changeme":"some_val"}
res=awaitgraph.ainvoke(inputs)
assertresisnotNone


============================================================
文件: agent_server\wiseAgent_server\tests\integration_tests\__init__.py
============================================================

"""Define any integration tests you want in this directory."""


============================================================
文件: agent_server\wiseAgent_server\tests\unit_tests\test_configuration.py
============================================================

fromlanggraph.pregelimportPregel
fromagent.graphimportgraph
deftest_placeholder()->None:
    assertisinstance(graph,Pregel)


============================================================
文件: agent_server\wiseAgent_server\tests\unit_tests\__init__.py
============================================================

"""Define any unit tests you may want in this directory."""


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\docker-compose.yml
============================================================

version: '3.8'
services:
  db:
    image: registry.cn-hangzhou.aliyuncs.com/pgvector:v0.5.0
    container_name: xiamen_bank_db_pgvector
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - xiamen_bank_network
  api:
    image: xiamen_bank_api:v1.2_202502240937
    container_name: xiamen_bank_api
    ports:
      - "8009:8000"
    env_file:
      - .env    
    networks:
      - xiamen_bank_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
networks:
  xiamen_bank_network:
    driver: bridge
volumes:
  postgres_data:    

============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\chat.py
============================================================

fromfastapiimportAPIRouter,HTTPException,Depends,Query
fromfastapi.responsesimportStreamingResponse
importasyncio
from.utilsimportChatResponse
from.databaseimportget_db
from.langgraphchatimportFlowRAG
fromdatetimeimportdatetime
fromtypingimportDict,Any,List,Optional
from.logging_configimportget_logger
from.modelsimportChatDialogueRequest,Chat,AppInfo
from.llmapiClientimportchat_model_api
importjson
fromsqlalchemy.ext.asyncioimportAsyncSession
fromsqlalchemyimportselect
frompydanticimportBaseModel
fromsqlalchemyimporttext
fromuuidimportUUID
logger=get_logger(__name__)
router=APIRouter(prefix="/api/v1/chat",tags=["chat"])
classCreateChatRequest(BaseModel):
    appId:str
@router.post("/createChat",response_model=Dict[str,Any])
asyncdefcreate_chat(request:CreateChatRequest,db:AsyncSession=Depends(get_db)):
    try:
        ifnotrequest.appId:
            raiseValueError("应用ID不能为空")
logger.info(f"正在创建新的聊天记录，appId: {request.appId}")
new_chat=Chat(app_id=request.appId,created_at=datetime.now(),stream=False,detail=False,variables={},messages=[])
db.add(new_chat)
try:
            awaitdb.commit()
awaitdb.refresh(new_chat)
logger.info(f"聊天创建成功: {str(new_chat.id)}")
return{"chatId":str(new_chat.id),"message":"聊天创建成功"}
exceptExceptionase:
            logger.error(f"创建聊天失败: {str(e)}")
raiseHTTPException(status_code=500,detail=f"创建聊天失败: {str(e)}")
exceptExceptionase:
        logger.error(f"创建聊天失败，详细错误: {str(e)}")
raiseHTTPException(status_code=500,detail=f"创建聊天失败: {str(e)}")
@router.post("/completions")
asyncdefchat_dialogue(conversation:ChatDialogueRequest,db:AsyncSession=Depends(get_db)):
    try:
        ifnotconversation.chatId:
            raiseValueError("聊天ID不能为空")
ifnotconversation.messages:
            raiseValueError("消息内容不能为空")
ifnotconversation.datasetId:
            raiseValueError("知识库ID不能为空")
stmt=select(Chat).where(Chat.id==conversation.chatId)
result=awaitdb.execute(stmt)
chat=result.scalar_one_or_none()
ifnotchat:
            logger.error(f"聊天记录未找到: {conversation.chatId}")
raiseHTTPException(status_code=404,detail="聊天记录未找到")
last_message=conversation.messages[-1]
logger.info(f'最新消息: {last_message}')
chat.messages=conversation.messages
chat.variables=conversation.variables
chat.stream=conversation.stream
chat.detail=conversation.detail
awaitdb.commit()
asyncdefstream_response():
            try:
                ai_message_content=""
asyncforchunkinchat_model_api(stream=conversation.stream,detail=conversation.detail,chat_id=conversation.chatId,knowledgebase_ids=conversation.datasetId,messages=conversation.messages,):
                    ifnotchunk:
                        continue
ai_message_content+=chunk
logger.debug(f"Stream chunk: {chunk}")
yieldchunk
awaitasyncio.sleep(0)
yield"event: end\ndata: Stream has ended\n\n"
ifai_message_content:
                    try:
                        conversation.messages.append({"role":"assistant","content":ai_message_content})
chat.messages=json.dumps(conversation.messages)
awaitdb.commit()
exceptExceptionase:
                        logger.error(f"保存AI回复失败: {str(e)}")
exceptasyncio.CancelledError:
                logger.warning(f"Stream was cancelled by client: {conversation.chatId}")
raise
exceptExceptionase:
                logger.error(f"Stream处理错误: {str(e)}")
yieldf"event: error\ndata: {str(e)}\n\n"
returnStreamingResponse(stream_response(),media_type='text/event-stream')
exceptValueErrorase:
        logger.error(f"参数验证错误: {str(e)}")
raiseHTTPException(status_code=400,detail=str(e))
exceptHTTPException:
        raise
exceptExceptionase:
        logger.error(f"处理聊天请求失败: {str(e)}")
raiseHTTPException(status_code=500,detail="处理聊天请求失败")


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\database.py
============================================================

fromsqlalchemy.ext.asyncioimportcreate_async_engine,AsyncSession
fromsqlalchemy.ormimportsessionmaker
fromsqlalchemy.poolimportNullPool
fromdotenvimportload_dotenv
importos
fromcontextlibimportasynccontextmanager
fromtypingimportAsyncGenerator
importlogging
importasyncpg
from.logging_configimportget_logger
load_dotenv('../.env')
DB_USER=os.getenv('POSTGRES_USER','username')
DB_PASSWORD=os.getenv('POSTGRES_PASSWORD','password')
DB_HOST=os.getenv('POSTGRES_HOST','db')
DB_PORT=os.getenv('POSTGRES_PORT','5432')
DB_NAME=os.getenv('POSTGRES_DB','postgres')
DATABASE_URL=f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
logger=get_logger(__name__)
logger.info(f"Connecting to database at {DB_HOST}:{DB_PORT}")
engine=create_async_engine(DATABASE_URL,echo=True,future=True,poolclass=NullPool)
async_session=sessionmaker(engine,class_=AsyncSession,expire_on_commit=False)
asyncdefget_db()->AsyncGenerator[AsyncSession,None]:
    asyncwithasync_session()assession:
        try:
            yieldsession
finally:
            awaitsession.close()


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\embedTest.py
============================================================

fromdotenvimportload_dotenv
importos
importrequests
frompathlibimportPath
current_dir=Path(__file__).parent
env_path=(current_dir.parent/'.env').resolve()
load_dotenv(env_path)
access_token=os.getenv('EMBEDDING_API_KEY')
url=os.getenv('EMBEDDING_API_URL')
ifnoturlornotaccess_token:
    raiseValueError(f"Missing configuration - URL: {url}, Token: {'exists'ifaccess_tokenelse'missing'}")
headers={'Content-Type':'application/json','Authorization':f'Bearer {access_token}'}
defembedding(data):
    json={"input":[data],"model":"bge-large"}
res=requests.post(url,json=json,headers=headers)
returnres.json()


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\initsql.py
============================================================

importasyncio
importasyncpg
from.logging_configimportget_logger
from.xmconfigimportsettings
logger=get_logger(__name__)
conn=None
asyncdefinit_db():
    globalconn
conn=awaitasyncpg.connect(user=settings.POSTGRES_USER,password=settings.POSTGRES_PASSWORD,host=settings.POSTGRES_HOST,port=settings.POSTGRES_PORT,database=settings.POSTGRES_DB)
logger.info("数据库连接成功")
try:
        awaitconn.execute("""
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        """)
awaitconn.execute("""
        CREATE TABLE IF NOT EXISTS app_info (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            app_name VARCHAR(50),
            created_at TIMESTAMP  DEFAULT NOW(),
            updated_at TIMESTAMP  DEFAULT NOW()
        );
        COMMENT ON TABLE app_info IS '应用程序配置信息表';
        COMMENT ON COLUMN app_info.id IS '应用ID';
        COMMENT ON COLUMN app_info.app_name IS '应用程序名称';
        COMMENT ON COLUMN app_info.created_at IS '创建时间';
        COMMENT ON COLUMN app_info.updated_at IS '更新时间';
        """)
logger.info("应用设置表 'app_info' 创建成功或已存在。")
print("应用设置表 'app_info' 创建成功或已存在。")
awaitconn.execute("""
        CREATE TABLE IF NOT EXISTS chats (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            app_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT NOW(),
            stream BOOLEAN DEFAULT FALSE, 
            detail BOOLEAN DEFAULT FALSE,
            variables JSONB DEFAULT '{}',
            messages JSONB DEFAULT '[]'
        );
        COMMENT ON TABLE chats IS '聊天记录表';
        COMMENT ON COLUMN chats.id IS '主键ID';
        COMMENT ON COLUMN chats.app_id IS '应用程序ID';
        COMMENT ON COLUMN chats.created_at IS '创建时间';
        COMMENT ON COLUMN chats.stream IS '是否为流式响应';
        COMMENT ON COLUMN chats.detail IS '是否包含详细信息';
        COMMENT ON COLUMN chats.variables IS '变量信息';
        COMMENT ON COLUMN chats.messages IS '聊天消息内容';
        """)
logger.info("聊天表 'chats' 创建成功或已存在。")
print("聊天表 'chats' 创建成功或已存在。")
awaitconn.execute("""
        CREATE TABLE IF NOT EXISTS knowledge_bases (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            parent_id VARCHAR(255),
            type VARCHAR(255),
            name VARCHAR(255) NOT NULL,
            intro TEXT,
            avatar VARCHAR(255),
            vector_model VARCHAR(255),
            agent_model VARCHAR(255),
            created_at TIMESTAMP NOT NULL,
            last_updated TIMESTAMP NOT NULL,
            user_id INTEGER,
            user_name VARCHAR(255),
            is_active BOOLEAN NOT NULL DEFAULT TRUE
        );
        COMMENT ON TABLE knowledge_bases IS '知识库主表';
        COMMENT ON COLUMN knowledge_bases.id IS '主键ID';
        COMMENT ON COLUMN knowledge_bases.parent_id IS '父级ID';
        COMMENT ON COLUMN knowledge_bases.type IS '类型';
        COMMENT ON COLUMN knowledge_bases.name IS '知识库名称';
        COMMENT ON COLUMN knowledge_bases.intro IS '知识库描述';
        COMMENT ON COLUMN knowledge_bases.avatar IS '知识库头像地址';
        COMMENT ON COLUMN knowledge_bases.vector_model IS '向量模型';
        COMMENT ON COLUMN knowledge_bases.agent_model IS '文本处理模型';
        COMMENT ON COLUMN knowledge_bases.created_at IS '创建时间';
        COMMENT ON COLUMN knowledge_bases.last_updated IS '最后更新时间';
        COMMENT ON COLUMN knowledge_bases.user_id IS '用户ID';
        COMMENT ON COLUMN knowledge_bases.user_name IS '用户名称';
        COMMENT ON COLUMN knowledge_bases.is_active IS '是否激活';
        """)
logger.info("知识库表 'knowledge_bases' 创建成功或已存在。")
print("知识库表 'knowledge_bases' 创建成功或已存在。")
awaitconn.execute("""
        CREATE TABLE IF NOT EXISTS collections (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            knowledge_base_id UUID NOT NULL REFERENCES knowledge_bases(id),
            parent_id VARCHAR(255),
            created_at TIMESTAMP 
        );
        COMMENT ON TABLE collections IS '知识库集合表';
        COMMENT ON COLUMN collections.id IS '主键ID';
        COMMENT ON COLUMN collections.knowledge_base_id IS '关联的知识库ID';
        COMMENT ON COLUMN collections.parent_id IS '父级ID';
        COMMENT ON COLUMN collections.created_at IS '创建时间';
        """)
logger.info("集合表 'collections' 创建成功或已存在。")
print("集合表 'collections' 创建成功或已存在。")
awaitconn.execute("""
        CREATE TABLE IF NOT EXISTS knowledge_data (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            collection_id UUID REFERENCES collections(id),
            knowledge_base_id UUID REFERENCES knowledge_bases(id),
            mode VARCHAR(50),
            data VARCHAR(2000),
            doc_name VARCHAR(255),
            prompt VARCHAR(50),
            created_at TIMESTAMP,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            deleted_at TIMESTAMP,
            embedding_vector "public"."vector"
        );
        COMMENT ON TABLE knowledge_data IS '知识库数据表';
        COMMENT ON COLUMN knowledge_data.id IS '主键ID';
        COMMENT ON COLUMN knowledge_data.collection_id IS '关联的集合ID';
        COMMENT ON COLUMN knowledge_data.knowledge_base_id IS '关联的知识库ID';
        COMMENT ON COLUMN knowledge_data.mode IS '模式';
        COMMENT ON COLUMN knowledge_data.data IS '知识内容';
        COMMENT ON COLUMN knowledge_data.prompt IS '提示词';
        COMMENT ON COLUMN knowledge_data.doc_name IS '文档名称';
        COMMENT ON COLUMN knowledge_data.created_at IS '创建时间';
        COMMENT ON COLUMN knowledge_data.is_active IS '是否激活';
        COMMENT ON COLUMN knowledge_data.deleted_at IS '删除时间';
        COMMENT ON COLUMN knowledge_data.embedding_vector IS '向量嵌入';
        """)
logger.info("知识数据表 'knowledge_data' 创建成功或已存在。")
print("知识数据表 'knowledge_data' 创建成功或已存在。")
exceptExceptionase:
        logger.error(f"初始化数据库时发生错误: {e}")
print(f"初始化数据库时发生错误: {e}")
finally:
        awaitconn.close()
asyncdefclose_db():
    """关闭数据库连接"""
globalconn
ifconn:
        awaitconn.close()
conn=None
logger.info("数据库连接已关闭")
if__name__=="__main__":
    asyncio.run(init_db())


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\knowledge.py
============================================================

fromfastapiimportAPIRouter,HTTPException,Depends
fromsqlalchemy.ext.asyncioimportAsyncSession
fromsqlalchemyimportselect,update
fromdatetimeimportdatetime
importjson
fromtypingimportList,Dict,Any
from.databaseimportget_db
from.modelsimportKnowledgeBase,Collection,KnowledgeData,AppInfo
from.embedTestimportembedding
frompydanticimportBaseModel,Field
from.logging_configimportget_logger
logger=get_logger(__name__)
router=APIRouter(prefix="/api/core/dataset",tags=["knowledge"])
classKnowledgeBaseCreate(BaseModel):
    name:str
parent_id:str|None=Field(default=None,alias="parentId")
type:str
intro:str|None=None
avatar:str|None=None
vector_model:str=Field(alias="vectorModel")
agent_model:str=Field(alias="agentModel")
classConfig:
        populate_by_alias=True
validate_by_name=True
classAppCreate(BaseModel):
    app_name:str
@router.post("/create/app",response_model=Dict[str,Any])
asyncdefcreate_app(app:AppCreate,db:AsyncSession=Depends(get_db)):
    try:
        new_app=AppInfo(**{"app_name":app.app_name,"created_at":datetime.now(),"updated_at":datetime.now()})
db.add(new_app)
awaitdb.commit()
awaitdb.refresh(new_app)
return{"data":str(new_app.id),"code":200,"statusText":"success","message":"创建应用成功"}
exceptExceptionase:
        awaitdb.rollback()
logger.error(f"Failed to create app: {str(e)}")
raiseHTTPException(status_code=500,detail=f"创建应用失败: {str(e)}")
@router.post("/create",response_model=Dict[str,Any])
asyncdefcreate_knowledge_base(knowledge_base:KnowledgeBaseCreate,db:AsyncSession=Depends(get_db)):
    try:
        new_kb=KnowledgeBase(name=knowledge_base.name,parent_id=knowledge_base.parent_id,type=knowledge_base.type,intro=knowledge_base.intro,avatar=knowledge_base.avatar,vector_model=knowledge_base.vector_model,agent_model=knowledge_base.agent_model,created_at=datetime.now(),last_updated=datetime.now(),is_active=True)
db.add(new_kb)
awaitdb.commit()
awaitdb.refresh(new_kb)
return{"code":200,"statusText":"success","message":"创建知识库成功","datasetId":str(new_kb.id)}
exceptExceptionase:
        return{"code":500,"statusText":"error","message":f"创建知识库失败: {str(e)}","datasetId":None}
@router.post("/collection/create",response_model=Dict[str,Any])
asyncdefcreate_collection(request:Dict[str,Any],db:AsyncSession=Depends(get_db)):
    try:
        stmt=select(KnowledgeBase).where(KnowledgeBase.id==request["datasetId"],KnowledgeBase.is_active==True)
result=awaitdb.execute(stmt)
kb=result.scalar_one_or_none()
ifnotkb:
            raiseHTTPException(status_code=404,detail="Knowledge base not found")
new_collection=Collection(knowledge_base_id=request["datasetId"],created_at=datetime.now())
db.add(new_collection)
awaitdb.commit()
awaitdb.refresh(new_collection)
return{"collectionId":str(new_collection.id),"code":200}
exceptExceptionase:
        awaitdb.rollback()
raiseHTTPException(status_code=500,detail=str(e))
@router.post("/data/pushData",response_model=Dict[str,Any])
asyncdefadd_knowledge_data(request:Dict[str,Any],db:AsyncSession=Depends(get_db)):
    try:
        result={"insertLen":0,"overToken":[],"repeat":[],"error":[],"insertIds":[]}
asyncwithdb.begin():
            fordatainrequest["data"]:
                try:
                    embedding_result=embedding(data)
ifnotembedding_resultor"data"notinembedding_result:
                        result["error"].append(f"获取 embedding 失败: {data[:100]}...")
continue
embedding_vector=embedding_result.get("data")[0].get("embedding")
ifnotembedding_vector:
                        result["error"].append(f"embedding 向量为空: {data[:100]}...")
continue
embedding_vector=[float(x)forxinembedding_vector]
new_data=KnowledgeData(collection_id=request["collectionId"],knowledge_base_id=request["datasetId"],mode=request["mode"],data=data,prompt=request.get("prompt"),doc_name=request.get("docName"),created_at=datetime.now(),embedding_vector=embedding_vector)
db.add(new_data)
awaitdb.flush()
result["insertIds"].append(str(new_data.id))
result["insertLen"]+=1
exceptExceptionase:
                    logger.error(f"Error processing data item: {str(e)}")
result["error"].append(str(e))
continue
return{"code":200,"statusText":"success","message":"","data":{"insertIds":result["insertIds"],"insertLen":result["insertLen"],"overToken":result["overToken"],"repeat":result["repeat"],"error":result["error"]}}
exceptExceptionase:
        logger.error(f"Failed to add knowledge data: {str(e)}")
raiseHTTPException(status_code=500,detail=str(e))
@router.delete("/data/delete",response_model=Dict[str,Any])
asyncdefdelete_knowledge_data(request:Dict[str,Any],db:AsyncSession=Depends(get_db)):
    try:
        stmt=update(KnowledgeData).where(KnowledgeData.id.in_(request["dbId"])).values(is_active=False,deleted_at=datetime.now())
result=awaitdb.execute(stmt)
awaitdb.commit()
return{"data":"success","code":200}
exceptExceptionase:
        awaitdb.rollback()
raiseHTTPException(status_code=500,detail=str(e))
@router.put("/data/update",response_model=Dict[str,Any])
asyncdefupdate_knowledge_data(request:Dict[str,Any],db:AsyncSession=Depends(get_db)):
    try:
        embedding_result=embedding(request["data"])
ifnotembedding_resultor"data"notinembedding_result:
            raiseHTTPException(status_code=500,detail="获取 embedding 失败")
embedding_vector=embedding_result.get("data")[0].get("embedding")
ifnotembedding_vector:
            raiseHTTPException(status_code=500,detail="embedding 向量为空")
stmt=update(KnowledgeData).where(KnowledgeData.id==request["dbId"]).values(data=request["data"],embedding_vector=embedding_vector)
result=awaitdb.execute(stmt)
awaitdb.commit()
return{"data":{"insertLen":result.rowcount,"overToken":[],"repeat":[],"error":[]},"code":200}
exceptExceptionase:
        awaitdb.rollback()
raiseHTTPException(status_code=500,detail=str(e))


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\knowledge_base_config.py
============================================================

fromdatetimeimportdatetime
frompydanticimportBaseModel,Field
fromtypingimportOptional,List
from.utilsimportgenerate_random_id
classKnowledgeBaseBase(BaseModel):
    name:str=Field(...,description="知识库名称，必填")
description:Optional[str]=Field(None,description="知识库描述，选填")
file_count:int=Field(0,description="文件数量，默认为0")
chunk_count:int=Field(0,description="文本块数量，默认为0")
is_active:bool=Field(True,description="是否激活，默认为True")
classKnowledgeBaseCreate(BaseModel):
    name:str=Field(...,description="知识库名称，必填")
description:Optional[str]=Field(None,description="知识库描述，选填")
user_id:Optional[int]=Field(None,description="用户 ID，选填")
user_name:Optional[str]=Field(None,description="用户名称，选填")
classKnowledgeBaseUpdate(BaseModel):
    name:Optional[str]=Field(None,description="知识库名称，选填")
description:Optional[str]=Field(None,description="知识库描述，选填")
is_active:Optional[bool]=Field(None,description="是否激活，选填")
classKnowledgeBaseResponse(KnowledgeBaseBase):
    id:str=Field(...,description="知识库 ID，必填")
created_at:datetime=Field(...,description="创建时间，必填")
last_updated:datetime=Field(...,description="最后更新时间，必填")
classConfig:
        orm_mode=True
classKnowledgeBaseCollectionCreate(BaseModel):
    klbId:str=Field(...,description="知识库 ID，必填")
classKnowledgeBaseCollectionResponse(BaseModel):
    collectionId:str=Field(...,description="集合 ID，必填")
classKnowledgeDataAdd(BaseModel):
    collectionId:str=Field(...,description="集合 ID，必填")
mode:str=Field(...,description="模式，必填，值为 'chunk' 或 'qa'")
prompt:Optional[str]=Field(None,description="QA 拆分提示词，选填")
data:List[dict]=Field(...,description="数据列表，必填")
classKnowledgeDataAddResponse(BaseModel):
    insertLen:int=Field(...,description="最终插入成功的数量，必填")
overToken:List[str]=Field(...,description="超出 token 的，必填")
repeat:List[str]=Field(...,description="重复的数量，必填")
error:List[str]=Field(...,description="其他错误，必填")
classKnowledgeDataDeleteRequest(BaseModel):
    dbIds:List[str]=Field(...,description="文档切块后，每一个数据块 ID，必填")
classKnowledgeDataUpdateRequest(BaseModel):
    dbId:str=Field(...,description="数据块 ID，必填")
data:List[dict]=Field(...,description="更新数据，必填")
classKnowledgeDataUpdateResponse(BaseModel):
    insertLen:int=Field(...,description="最终插入成功数量，必填")
overToken:List[str]=Field(...,description="超出 token 的，必填")
repeat:List[str]=Field(...,description="重复的数量，必填")
error:List[str]=Field(...,description="其他错误，必填")
classKnowledgeFileResponse(BaseModel):
    id:str=Field(...,description="文件 ID，必填")
name:str=Field(...,description="文件名称，必填")
url:str=Field(...,description="文件 URL，必填")


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\langgraphchat.py
============================================================

fromtypingimportTypedDict,List,Dict,Any,Optional
fromlanggraph.graphimportStateGraph,END
fromlanggraph.graph.messageimportadd_messages
importasyncio
importtraceback
fromdatetimeimportdatetime
from.modelsimportSearchResult,KnowledgeQAParams
from.pg_retrieverimportparallel_knowledge_search
from.logging_configimportsetup_logging,get_logger
setup_logging()
logger=get_logger(__name__)
classRAGState(TypedDict):
    query:str
Knowledgebase_ids:List[str]
search_results:List[SearchResult]
error:Optional[str]
output:Optional[Dict]
classFlowRAG:
    def__init__(self,knowledgebase_ids:List[str]):
        logger.info(f"knowledgebase_ids==》{knowledgebase_ids}")
self.knowledgebase_ids=knowledgebase_ids
self.workflow=StateGraph(RAGState)
self._setup_workflow()
def_setup_workflow(self):
        self.workflow.add_node("search",self.search_node)
self.workflow.add_node("build_answer",self.build_prompt)
self.workflow.set_entry_point("search")
self.workflow.add_edge("search","build_answer")
self.workflow.add_edge("build_answer",END)
asyncdefsearch_node(self,state:RAGState)->RAGState:
        """执行搜索任务"""
logger.info(f"开始执行搜索任务，查询词：{state['query']}")
try:
            results=awaitparallel_knowledge_search(state["query"],self.knowledgebase_ids,)
logger.info(f"搜索完成，找到 {len(results)} 条结果")
return{**state,"search_results":results}
exceptExceptionase:
            logger.error(f"搜索失败: {str(e)}")
traceback.print_exc()
return{**state,"error":str(e)}
asyncdefbuild_prompt(self,state:RAGState)->RAGState:
        """构建提示"""
ifstate.get("error"):
            logger.error(f"构建提示失败: {state['error']}")
returnstate
logger.info("开始构建提示")
retrieval_template="【参考资料{{index}}】{{content}}"
quote_strs=[]
foridx,resinenumerate(state["search_results"],1):
            quote_strs.append(retrieval_template.replace("{{content}}",res["content"]).replace("{{index}}",str(idx)))
quote_block="\n\n".join(quote_strs)
final_prompt=f"{quote_block}\n\n请根据以上参考内容回答：{state['query']}"
logger.info(f"构建提示词完成，提示词：{final_prompt}")
return{**state,"output":{"role":"user","content":final_prompt}}
asyncdefrun(self,query:str)->Dict[str,Any]:
        """执行流程"""
logger.info(f"开始执行流程，查询词：{query}")
try:
            initial_state=RAGState(query=query,Knowledgebase_ids=self.knowledgebase_ids,search_results=[],error=None,output=None)
app=self.workflow.compile()
result=awaitapp.ainvoke(initial_state)
logger.info(f"流程执行完成，结果：{result}")
returnresult
exceptExceptionase:
            logger.error(f"流程执行失败: {str(e)}")
traceback.print_exc()
return{"error":str(e)}


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\llmapiClient.py
============================================================

fromtypingimportAsyncGenerator,List,Dict,Any,Optional
from.langgraphchatimportFlowRAG
importhttpx
importtraceback
importjson
from.modelsimportKnowledgeQAParams,DeepSeekConfig
fromdotenvimportload_dotenv
importos
from.modelsimportProvider
load_dotenv('../.env')
DEFAULT_MODEL=os.getenv('LLM_MODEL_NAME','DeepSeek-R1-Distill-Qwen-32B')
DEFAULT_PROVIDER=os.getenv('LLM_PROVIDER',Provider.DEEPSEEK)
from.logging_configimportsetup_logging,get_logger
setup_logging()
logger=get_logger(__name__)
defprepare_llm_request(messages:List[Dict[str,Any]],params:Dict[str,Any],provider:str=Provider.OPENAI)->tuple[List[Dict[str,Any]],Dict[str,Any]]:
    """
    统一处理 LLM 请求的消息和参数
    
    Args:
        messages: 原始消息列表
        params: 请求参数
        provider: 模型提供商
    
    Returns:
        tuple[处理后的消息列表, 处理后的参数]
    """
logger.info("=========================处理信息===============================")
processed_messages=messages
validated_params=params.copy()
ifprocessed_messagesandprocessed_messages[0]['role']=='system'andnotprocessed_messages[0]['content'].strip():
        processed_messages=processed_messages[1:]
logger.info(f"[{provider}] 移除了空系统消息")
ifprovider==Provider.DEEPSEEK:
        ifmessages:
            processed_messages=[messages[0]]
foriinrange(1,len(messages)):
                current_msg=messages[i]
prev_msg=processed_messages[-1]
ifcurrent_msg['role']==prev_msg['role']:
                    prev_msg['content']=f"{prev_msg['content']}\n{current_msg['content']}"
else:
                    processed_messages.append(current_msg)
if'top_p'invalidated_params:
            top_p=float(validated_params['top_p'])
iftop_p<=0ortop_p>1.0:
                validated_params['top_p']=0.9
logger.warning(f"[DeepSeek] Invalid top_p value: {top_p}, using default value: 0.9")
if'temperature'invalidated_params:
            temp=float(validated_params['temperature'])
iftemp<0ortemp>1.0:
                validated_params['temperature']=0.7
logger.warning(f"[DeepSeek] Invalid temperature value: {temp}, using default value: 0.7")
if'max_tokens'invalidated_params:
            max_tokens=int(validated_params['max_tokens'])
ifmax_tokens<1:
                validated_params['max_tokens']=2000
logger.warning(f"[DeepSeek] Invalid max_tokens value: {max_tokens}, using default value: 2000")
elifmax_tokens>8192:
                validated_params['max_tokens']=8192
logger.warning(f"[DeepSeek] max_tokens value {max_tokens} exceeds limit, using max value: 8192")
elifprovider==Provider.DOUBAO:
        if'top_p'invalidated_params:
            top_p=float(validated_params['top_p'])
iftop_p<=0ortop_p>1.0:
                validated_params['top_p']=0.9
logger.warning(f"[Doubao] Invalid top_p value: {top_p}, using default value: 0.9")
if'temperature'invalidated_params:
            temp=float(validated_params['temperature'])
iftemp<0ortemp>1.0:
                validated_params['temperature']=0.7
logger.warning(f"[Doubao] Invalid temperature value: {temp}, using default value: 0.7")
if'max_tokens'invalidated_params:
            max_tokens=int(validated_params['max_tokens'])
ifmax_tokens<1:
                validated_params['max_tokens']=2000
logger.warning(f"[Doubao] Invalid max_tokens value: {max_tokens}, using default value: 2000")
elifmax_tokens>4096:
                validated_params['max_tokens']=4096
logger.warning(f"[Doubao] max_tokens value {max_tokens} exceeds limit, using max value: 4096")
else:
        if'max_tokens'invalidated_params:
            max_tokens=int(validated_params['max_tokens'])
ifmax_tokens<=0:
                validated_params['max_tokens']=2000
logger.warning(f"Invalid max_tokens value: {max_tokens}, using default value: 2000")
if'top_p'invalidated_params:
            top_p=float(validated_params['top_p'])
iftop_p<=0ortop_p>1.0:
                validated_params['top_p']=0.9
logger.warning(f"Invalid top_p value: {top_p}, using default value: 0.9")
validated_params={k:vfork,vinvalidated_params.items()ifvisnotNone}
returnprocessed_messages,validated_params
asyncdefstream_openai_api(api_key:str,model:str,messages:List[Dict[str,Any]],url:str,extra:Optional[Dict[str,Any]]=None,system_prompt:Optional[str]=None,provider:str=Provider.OPENAI)->AsyncGenerator[str,None]:
    logger.info('==>stream_openai_api')
logger.info(f"URL: {url}")
logger.info(f"Model: {model}")
logger.info(f"Provider: {provider}")
ifnotapi_keyornotmodelornotmessages:
        raiseValueError("Missing required parameters: api_key, model, or messages")
headers={"Authorization":f"Bearer {api_key}","Content-Type":"application/json"}
question_url=urlifurl.endswith('/chat/completions')elsef'{url}/chat/completions'
data={"model":model,"messages":messages,"stream":True}
timeout=300
ifsystem_prompt:
        ifnotmessagesorlen(messages)==0:
            messages=[{"role":"system","content":system_prompt}]
else:
            messages[0]['content']=system_prompt
ifextra:
        valid_params=['temperature','top_p','timeout']
forparaminvalid_params:
            ifparaminextraandextra[param]isnotNone:
                ifparam=='timeout':
                    timeout=extra[param]
else:
                    data[param]=extra[param]
processed_messages,validated_params=prepare_llm_request(messages,data,provider)
validated_params['messages']=processed_messages
logger.info("=========================请求信息===============================")
logger.info(f"Request URL: {question_url}")
logger.info(f"Request Headers: {headers}")
logger.info(f"Request Data: {validated_params}")
logger.info("========================================================")
try:
        asyncwithhttpx.AsyncClient()asclient:
            asyncwithclient.stream('POST',question_url,headers=headers,json=validated_params,timeout=timeout)asresponse:
                logger.info(f"Response Status Code: {response.status_code}")
ifresponse.status_code!=200:
                    error_body=awaitresponse.aread()
logger.error(f"API Error - Status Code: {response.status_code}")
logger.error(f"Response Headers: {response.headers}")
logger.error(f"Error Body: {error_body.decode('utf-8')}")
yieldf"event: error\ndata: API request failed with status code: {error_body.decode('utf-8')}\n\n"
return
asyncforchunkinresponse.aiter_bytes():
                    try:
                        decoded=chunk.decode('utf-8')
forlineindecoded.split('\n\n'):
                            line=line.strip()
ifnotline:
                                continue
ifline.startswith('data: '):
                                yieldf"{line}\n\n"
else:
                                if'{'inline:
                                    json_data=line[line.find('{'):]
yieldf"data: {json_data}\n\n"
exceptUnicodeDecodeErrorase:
                        traceback.print_exc()
logger.error(f"Decode Error: {str(e)}")
continue
excepthttpx.RequestErrorase:
        traceback.print_exc()
error_message=f"请求错误: {str(e)}"
logger.error(error_message)
yieldf"event: error\ndata: {error_message}\n\n"
exceptExceptionase:
        error_message=f"未知错误: {str(e)}"
logger.error(error_message)
traceback.print_exc()
yieldf"event: error\ndata: {error_message}\n\n"
asyncdefopenai_api(api_key:str,model:str,messages:List[Dict[str,Any]],url:str,extra:Optional[Dict[str,Any]]=None,system_prompt:Optional[str]=None,provider:str=Provider.OPENAI)->Dict[str,Any]:
    logger.info('==>openai_api')
logger.info(f"URL: {url}")
logger.info(f"Model: {model}")
logger.info(f"Provider: {provider}")
headers={"Authorization":f"Bearer {api_key}","Content-Type":"application/json"}
data={"model":model,"messages":messages,"stream":False}
ifsystem_prompt:
        data['messages'][0]['content']=system_prompt
ifextra:
        forkeyin['temperature','top_p','max_tokens']:
            ifkeyinextra:
                data[key]=extra[key]
processed_messages,validated_params=prepare_llm_request(messages,data,provider)
validated_params['messages']=processed_messages
logger.info("Request Headers:")
logger.info(headers)
logger.info("Request Data:")
logger.info(validated_params)
asyncwithhttpx.AsyncClient(timeout=httpx.Timeout(60.0))asclient:
        try:
            response=awaitclient.post(f'{url}/chat/completions',headers=headers,json=validated_params)
logger.info(f"API响应: {response.json()}")
ifresponse.status_code==200:
                returnresponse.json()
else:
                error_body=awaitresponse.aread()
logger.error(f"API Error - Status Code: {response.status_code}")
logger.error(f"Response Headers: {response.headers}")
logger.error(f"Error Body: {error_body.decode('utf-8')}")
raiseException(f"API request failed with status code: {response.status_code}")
excepthttpx.ReadTimeout:
            error_message="请求超时，请稍后重试"
logger.error(error_message)
return{"error":error_message}
exceptExceptionase:
            traceback.print_exc()
error_message=f"API请求失败: {str(e)}"
logger.error(error_message)
return{"error":error_message}
asyncdefchat_model_api(chat_id:str,messages:List[Dict[str,Any]],knowledgebase_ids:List[str],stream:bool,detail:bool)->AsyncGenerator[str,None]:
    try:
        messages_list=convert_messages(messages)
logger.info(f'调用 KNOWLEDGE_QA - stream: {stream}, detail: {detail}')
kg_qa_flow_rag=FlowRAG(knowledgebase_ids=knowledgebase_ids)
user_query=messages_list[-1]['content']
logger.info(f"用户查询: {user_query}")
kg_qa_results=awaitkg_qa_flow_rag.run(user_query)
logger.info(f"知识库检索结果: {json.dumps(kg_qa_results,ensure_ascii=False)}")
last_message=kg_qa_results['output']
messages_list[-1]=last_message
service_config=DeepSeekConfig.get_config()
ifdetail:
            yieldjson.dumps({"event":"moduleStatus","data":{"status":"running","name":"知识库搜索"}})+"\n\n"
quoteList={"moduleName":"AI 对话","moduleType":"chatNode","model":service_config["model"],"query":user_query,"quoteList":[{"content":res["content"],"score":res["score"]}forresinkg_qa_results.get("search_results",[])]}
yieldf'event: appStreamResponse\ndata: [{json.dumps(quoteList,ensure_ascii=False)}]\n\n'
yield'event: moduleStatus\ndata: {"status":"running","name":"AI 对话"}\n\n'
logger.info(f"发送给模型的消息: {json.dumps(messages_list,ensure_ascii=False)}")
ifstream:
            full_response=[]
asyncforchunkinstream_openai_api(service_config["api_key"],service_config["model"],messages_list,service_config["service_url"],provider=service_config["provider"]):
                if'data: 'inchunk:
                    try:
                        json_str=chunk.replace('data: ','').strip()
ifjson_str:
                            chunk_data=json.loads(json_str)
content=chunk_data.get('choices',[{}])[0].get('delta',{}).get('content','')
ifcontent:
                                full_response.append(content)
exceptjson.JSONDecodeError:
                        pass
yieldjson.dumps(chunk)+"\n\n"
logger.info(f"模型完整响应: {''.join(full_response)}")
else:
            response=awaitopenai_api(service_config["api_key"],service_config["model"],messages_list,service_config["service_url"],provider=service_config["provider"])
content=response.get('choices',[{}])[0].get('message',{}).get('content','')
logger.info(f"取模型响应的content内容: {content}")
yieldjson.dumps(response)+"\n\n"
exceptExceptionase:
        error_message=f"发生错误：{str(e)}"
traceback.print_exc()
logger.error(error_message)
yieldf"event: error\ndata: {error_message}\n\n"
defconvert_messages(original_messages):
    converted_messages=[]
converted_messages.append({"role":"system","content":""})
formsginoriginal_messages:
        ifmsg.get('role')in['user','assistant']:
            converted_messages.append({"role":msg['role'],"content":msg['content']})
returnconverted_messages


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\logging_config.py
============================================================

importlogging
importos
defsetup_logging():
    logging.basicConfig(level=os.getenv("LOG_LEVEL",logging.INFO),format='%(levelname)s - %(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(message)s',datefmt='%Y-%m-%d %H:%M:%S')
defget_logger(name):
    logger=logging.getLogger(name)
returnlogger
defget_logger_debug(name):
    logger=logging.getLogger(name)
logger.setLevel(logging.DEBUG)
returnlogger
defget_logger_info(name):
    logger=logging.getLogger(name)
logger.setLevel(logging.INFO)
returnlogger
defget_logger_warning(name):
    logger=logging.getLogger(name)
logger.setLevel(logging.WARNING)


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\main.py
============================================================

importos
fromfastapiimportFastAPI
fromfastapi.middleware.corsimportCORSMiddleware
from.chatimportrouteraschat_router
from.knowledgeimportrouterasknowledge_router
from.initsqlimportinit_db,close_db
from.logging_configimportsetup_logging,get_logger
importasyncio
setup_logging()
logger=get_logger(__name__)
app=FastAPI(title="厦门国际银行 API",description="厦门国际银行知识库问答系统 API",version="1.0.0")
app.add_middleware(CORSMiddleware,allow_origins=["*"],allow_credentials=True,allow_methods=["*"],allow_headers=["*"],)
app.include_router(chat_router)
app.include_router(knowledge_router)
@app.on_event("startup")
asyncdefstartup_event():
    """应用启动时执行"""
logger.info("正在初始化数据库...")
awaitinit_db()
logger.info("数据库初始化完成")
@app.on_event("shutdown")
asyncdefshutdown_event():
    """应用关闭时执行"""
logger.info("正在关闭数据库连接...")
awaitclose_db()
logger.info("数据库连接已关闭")
@app.get("/")
asyncdefroot():
    return{"message":"厦门国际银行知识库问答系统 API"}
@app.get("/health")
asyncdefhealth_check():
    return{"status":"healthy"}
if__name__=="__main__":
    importuvicorn
uvicorn.run(app,host="127.0.0.1",port=8000)


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\models.py
============================================================

fromtypingimportTypedDict,List,Dict,Any,Optional
fromdatetimeimportdatetime
frompydanticimportBaseModel
importos
fromsqlalchemyimportColumn,String,DateTime,Boolean,JSON,UUID,Integer,ForeignKey,Text
fromsqlalchemy.ext.declarativeimportdeclarative_base
importuuid
fromdotenvimportload_dotenv
fromsqlalchemy.typesimportTypeDecorator
fromsqlalchemy.dialects.postgresqlimportARRAY
fromsqlalchemy.sql.sqltypesimportFloat
load_dotenv('../.env')
classSearchResult(TypedDict):
    role:str
content:str
score:float
id:str
file_id:str
knowledge_base_id:str
chunk_index:int
answer:str
question:str
created_at:datetime
classKnowledgeQAParams(TypedDict):
    knowledge_base_ids:List[str]
prompt:Optional[str]
prompt_retrieval:Optional[str]
config:Dict[str,Any]
classChatDialogueRequest(BaseModel):
    chatId:str
datasetId:List[str]
stream:Optional[bool]=False
detail:Optional[bool]=False
variables:Optional[Dict[str,Any]]=None
messages:List[Dict[str,Any]]=[]
classProvider:
    OPENAI="OPENAI"
DEEPSEEK="DEEPSEEK"
DOUBAO="DOUBAO"
LOCAL="LOCAL"
OLLAMA="OLLAMA"
JIUTIAN="JIUTIAN"
classDeepSeekConfig:
    """DeepSeek 模型配置"""
@classmethod
defget_config(cls)->Dict[str,str]:
        return{"service_url":os.getenv('DEEPSEEK_SERVICE_URL'),"api_key":os.getenv('DEEPSEEK_API_KEY'),"model":os.getenv('DEEPSEEK_MODEL_NAME'),"provider":os.getenv('DEEPSEEK_PROVIDER')}
Base=declarative_base()
classChat(Base):
    __tablename__="chats"
id=Column(UUID(as_uuid=True),primary_key=True,default=uuid.uuid4)
app_id=Column(String(255))
created_at=Column(DateTime,default=datetime.now)
stream=Column(Boolean,default=False)
detail=Column(Boolean,default=False)
variables=Column(JSON)
messages=Column(JSON)
classKnowledgeBase(Base):
    __tablename__="knowledge_bases"
id=Column(UUID,primary_key=True,server_default="uuid_generate_v4()")
parent_id=Column(String(255))
type=Column(String(255))
name=Column(String(255),nullable=False)
intro=Column(Text)
avatar=Column(String(255))
vector_model=Column(String(255))
agent_model=Column(String(255))
created_at=Column(DateTime,nullable=False)
last_updated=Column(DateTime,nullable=False)
user_id=Column(Integer)
user_name=Column(String(255))
is_active=Column(Boolean,nullable=False,default=True)
classCollection(Base):
    __tablename__="collections"
id=Column(UUID(as_uuid=True),primary_key=True,default=uuid.uuid4)
knowledge_base_id=Column(UUID(as_uuid=True),ForeignKey('knowledge_bases.id'),nullable=False)
created_at=Column(DateTime,nullable=False,default=datetime.now)
classVector(TypeDecorator):
    impl=ARRAY(Float(precision=53))
cache_ok=True
def__init__(self,dimensions=1536):
        super(Vector,self).__init__()
self.dimensions=dimensions
defprocess_bind_param(self,value,dialect):
        ifvalueisnotNone:
            ifnotisinstance(value,list):
                value=list(value)
return[float(v)forvinvalue]
returnvalue
classKnowledgeData(Base):
    __tablename__="knowledge_data"
id=Column(UUID(as_uuid=True),primary_key=True,default=uuid.uuid4)
collection_id=Column(UUID(as_uuid=True),ForeignKey('collections.id'),nullable=False)
knowledge_base_id=Column(UUID(as_uuid=True),ForeignKey('knowledge_bases.id'),nullable=False)
mode=Column(String(50),nullable=False)
data=Column(String(2000),nullable=False)
doc_name=Column(String(255))
prompt=Column(String(50))
created_at=Column(DateTime,nullable=False,default=datetime.now)
is_active=Column(Boolean,nullable=False,default=True)
deleted_at=Column(DateTime)
embedding_vector=Column(ARRAY(Float(precision=53)))
classAppInfo(Base):
    __tablename__="app_info"
id=Column(UUID(as_uuid=True),primary_key=True,server_default="uuid_generate_v4()")
app_name=Column(String(50))
created_at=Column(DateTime,default=datetime.now)
updated_at=Column(DateTime,default=datetime.now)


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\pgdb.py
============================================================

importasyncio
fromasyncpgimportcreate_pool,Pool
fromapp.xmconfigimportsettings
fromapp.logging_configimportget_logger
fromtypingimportOptional
logger=get_logger(__name__)
classPostgreSQLManager:
    _instance=None
_initialized=False
_pool:Optional[Pool]=None
def__new__(cls):
        """单例模式"""
ifcls._instanceisNone:
            cls._instance=super().__new__(cls)
returncls._instance
def__init__(self):
        """初始化方法"""
pass
asyncdef_init_pool(self):
        """初始化连接池"""
try:
            logger.info(f"正在初始化 PostgreSQL 连接池，地址: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}")
self._pool=awaitcreate_pool(user=settings.POSTGRES_USER,password=settings.POSTGRES_PASSWORD,host=settings.POSTGRES_HOST,port=settings.POSTGRES_PORT,database=settings.POSTGRES_DB,min_size=settings.POSTGRES_POOL_MIN_SIZE,max_size=settings.POSTGRES_POOL_MAX_SIZE,command_timeout=settings.POSTGRES_COMMAND_TIMEOUT,timeout=settings.POSTGRES_TIMEOUT)
self._initialized=True
logger.info("PostgreSQL 连接池初始化成功")
print("PostgreSQL 连接池初始化成功")
exceptExceptionase:
            logger.error(f"PostgreSQL 连接池初始化失败: {str(e)}")
raise
asyncdefconnect(self):
        """连接数据库"""
ifnotself._initialized:
            awaitself._init_pool()
try:
            asyncwithself._pool.acquire()asconn:
                awaitconn.execute('SELECT 1')
logger.info("PostgreSQL 连接验证成功")
exceptExceptionase:
            logger.error(f"PostgreSQL 连接验证失败: {str(e)}")
raise
asyncdefdisconnect(self):
        """断开连接"""
ifself._pool:
            awaitself._pool.close()
self._pool=None
self._initialized=False
logger.info("PostgreSQL 连接已断开")
@property
defpool(self)->Pool:
        """获取连接池"""
ifnotself._initialized:
            raiseRuntimeError("PostgreSQL 连接池未初始化")
returnself._pool
postgresql=PostgreSQLManager()
asyncdefinitialize_db():
    awaitpostgresql.connect()
returnpostgresql.pool
asyncdefget_db():
    awaitinitialize_db()
returnpostgresql.pool


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\pg_retriever.py
============================================================

importasyncpg
fromtypingimportList,Dict,Any
importjson
from.embedTestimportembedding
fromdotenvimportload_dotenv
frompathlibimportPath
importos
current_dir=Path(__file__).parent
env_path=(current_dir.parent/'.env').resolve()
load_dotenv(env_path)
MAX_CONTENT_LENGTH=500
SIMILARITY_THRESHOLD=0.8
deftruncate_content(content:str,max_length:int=MAX_CONTENT_LENGTH)->str:
    """
    截断内容到指定长度，保持完整句子
    """
iflen(content)<=max_length:
        returncontent
end_markers=['.','。','!','！','?','？']
truncated=content[:max_length]
last_marker_pos=max(truncated.rfind(marker)formarkerinend_markers)
iflast_marker_pos>0:
        returncontent[:last_marker_pos+1]
returntruncated+"..."
asyncdefget_pg_pool():
    returnawaitasyncpg.create_pool(user=os.getenv('POSTGRES_USER'),password=os.getenv('POSTGRES_PASSWORD'),host=os.getenv('POSTGRES_HOST'),port=os.getenv('POSTGRES_PORT','5433'),database=os.getenv('POSTGRES_DB'))
asyncdefparallel_knowledge_search(query:str,knowledge_base_ids:List[str])->List[Dict[str,Any]]:
    """
    使用 pgvector 进行并行知识检索
    """
pool=awaitget_pg_pool()
asyncwithpool.acquire()asconn:
        awaitconn.execute('CREATE EXTENSION IF NOT EXISTS vector;')
res=embedding(query)
vector_data=res.get("data")[0].get("embedding")
vector_str=f"[{','.join(map(str,vector_data))}]"
results=awaitconn.fetch("""
            SELECT 
                id,
                data,
                mode,
                knowledge_base_id, 
                collection_id,
                (embedding_vector <=> $1::vector) as score
            FROM knowledge_data 
            WHERE knowledge_base_id = ANY($2)
            AND is_active = TRUE
            AND (embedding_vector <=> $1::vector) < $4
            ORDER BY embedding_vector <=> $1::vector
            LIMIT $3
        """,vector_str,knowledge_base_ids,5,SIMILARITY_THRESHOLD)
return[{'id':str(r['id']),'content':truncate_content(r['data']),'answer':truncate_content(r['data']),'question':query,'knowledge_base_id':str(r['knowledge_base_id']),'collection_id':str(r['collection_id']),'score':float(r['score']),'mode':r['mode']}forrinresults]


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\utils.py
============================================================

importuuid
frompydanticimportBaseModel,Field
fromdatetimeimportdatetime
frompydanticimportBaseModel
fromtypingimportList,Dict,Any
defgenerate_random_id():
    returnstr(uuid.uuid4())
classMessage(BaseModel):
    content:str
createAt:int
id:str
updateAt:int
message:str
role:str
meta:Dict[str,Any]
classChatResponse(BaseModel):
    app_info:str
conversation_id:str
extra:Dict[str,Any]
user_id:int
user_name:str
messages:List[Any]


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\app\xmconfig.py
============================================================

importos
frompathlibimportPath
fromurllib.parseimportquote_plus
fromdotenvimportload_dotenv
from.logging_configimportget_logger
logger=get_logger(__name__)
classSettings:
    _instance=None
_initialized=False
def__new__(cls):
        ifcls._instanceisNone:
            cls._instance=super().__new__(cls)
returncls._instance
def__init__(self):
        ifnotself._initialized:
            self._initialize()
def_find_env_file(self):
        """查找环境配置文件"""
env_mode=os.getenv('ENV_MODE','production')
current_path=Path(__file__).resolve()
root_dir=current_path.parent.parent
env_files=[root_dir/f'.env.{env_mode}',root_dir/'.env',current_path.parent/f'.env.{env_mode}',current_path.parent/'.env',]
forenv_fileinenv_files:
            ifenv_file.exists():
                logger.info(f"找到配置文件: {env_file}")
returnstr(env_file)
logger.warning("未找到配置文件，使用默认配置")
returnNone
def_initialize(self):
        """初始化配置"""
logger.info("开始加载环境配置")
try:
            env_file=self._find_env_file()
ifenv_file:
                load_dotenv(env_file)
self.POSTGRES_USER=os.getenv('POSTGRES_USER','username')
self.POSTGRES_PASSWORD=os.getenv('POSTGRES_PASSWORD','password')
self.POSTGRES_HOST=os.getenv('POSTGRES_HOST','localhost')
self.POSTGRES_PORT=int(os.getenv('POSTGRES_PORT','5432'))
self.POSTGRES_DB=os.getenv('POSTGRES_DB','postgres')
self.POSTGRES_SCHEMA=os.getenv('POSTGRES_SCHEMA','public')
self.POSTGRES_POOL_MIN_SIZE=int(os.getenv('POSTGRES_POOL_MIN_SIZE','10'))
self.POSTGRES_POOL_MAX_SIZE=int(os.getenv('POSTGRES_POOL_MAX_SIZE','100'))
self.POSTGRES_COMMAND_TIMEOUT=int(os.getenv('POSTGRES_COMMAND_TIMEOUT','60'))
self.POSTGRES_TIMEOUT=int(os.getenv('POSTGRES_TIMEOUT','60'))
self.API_HOST=os.getenv('API_HOST','0.0.0.0')
self.API_PORT=int(os.getenv('API_PORT','8000'))
self.API_DEBUG=os.getenv('API_DEBUG','true').lower()=='true'
self.VECTOR_DIMENSION=int(os.getenv('VECTOR_DIMENSION','1536'))
self.VECTOR_SIMILARITY_THRESHOLD=float(os.getenv('VECTOR_SIMILARITY_THRESHOLD','0.8'))
self._initialized=True
logger.info("环境配置加载完成")
ifself.API_DEBUG:
                self._log_config()
exceptExceptionase:
            logger.error(f"加载配置时发生错误: {e}")
raise
def_log_config(self):
        """打印配置信息（敏感信息会被遮掩）"""
logger.debug("当前配置:")
forkey,valueinself.__dict__.items():
            ifnotkey.startswith('_'):
                if'PASSWORD'inkey:
                    logger.debug(f"{key}: ****")
else:
                    logger.debug(f"{key}: {value}")
@property
defDATABASE_URL(self)->str:
        """构建 PostgreSQL 连接 URL"""
returnf"postgresql://{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
settings=Settings()


============================================================
文件: api\ServerAPI_v1\xiamenInternationalBank\tests\test_api.py
============================================================

importunittest
importasyncio
importjson
importpytest
fromhttpximportAsyncClient
fromdatetimeimportdatetime
fromtypingimportList,Dict
importlogging
logging.basicConfig(level=logging.INFO)
logger=logging.getLogger(__name__)
BASE_URL="http://localhost:8000"
classTestAPIEndpoints(unittest.TestCase):
    defsetUp(self):
        self.app_id=None
self.chat_id=None
self.dataset_id=None
self.collection_id=None
self.data_block_ids=[]
self.loop=asyncio.get_event_loop()
self.loop.run_until_complete(self.async_setUp())
logger.info("初始化测试实例...")
logger.info(f"初始状态: app_id={self.app_id}, dataset_id={self.dataset_id}, collection_id={self.collection_id}")
asyncdefasync_setUp(self):
        self.client=AsyncClient(base_url=BASE_URL,timeout=30.0)
asyncdefasync_tearDown(self):
        awaitself.client.aclose()
deftearDown(self):
        self.loop.run_until_complete(self.async_tearDown())
asyncdeftest_1_create_app(self):
        """测试创建应用"""
logger.info("开始测试创建应用...")
test_app={"app_name":"cc测试厦门知识库应用"}
response=awaitself.client.post("/api/core/dataset/create/app",json=test_app)
data=response.json()
logger.info(f"创建应用响应: {json.dumps(data,ensure_ascii=False,indent=2)}")
self.app_id=data.get("data")
self.assertEqual(response.status_code,200)
self.assertIsNotNone(self.app_id,"App ID should not be None")
logger.info(f"创建应用完成，app_id: {self.app_id}")
asyncdeftest_2_create_chat(self):
        """测试创建聊天"""
logger.info("开始测试创建聊天...")
logger.info(f"使用 app_id: {self.app_id}")
test_chat={"appId":str(self.app_id)}
response=awaitself.client.post("/api/v1/chat/createChat",json=test_chat)
data=response.json()
logger.info(f"创建聊天响应: {json.dumps(data,ensure_ascii=False,indent=2)}")
self.chat_id=data.get("chatId")
self.assertEqual(response.status_code,200)
self.assertIsNotNone(self.chat_id,"Chat ID should not be None")
logger.info(f"创建聊天完成，chat_id: {self.chat_id}")
asyncdeftest_3_create_knowledge_base(self):
        """测试创建知识库"""
logger.info("开始测试创建知识库...")
test_kb={"name":"test知识库","type":"dataset","intro":"test知识库介绍","avatar":"","vectorModel":"bge-large-en","agentModel":"DeepSeek-R1-Distill-Qwen-32B"}
response=awaitself.client.post("/api/core/dataset/create",json=test_kb)
data=response.json()
logger.info(f"创建知识库响应: {json.dumps(data,ensure_ascii=False,indent=2)}")
ifdata.get("code")==200:
            self.dataset_id=data.get("datasetId")
logger.info(f"成功保存 dataset_id: {self.dataset_id}")
else:
            self.fail(f"创建知识库失败: {data}")
self.assertIsNotNone(self.dataset_id,"Dataset ID should not be None")
asyncdeftest_4_create_collection(self):
        """测试创建集合"""
logger.info("开始测试创建集合...")
ifnotself.dataset_id:
            self.fail("Dataset ID is required for creating collection")
test_collection={"datasetId":str(self.dataset_id),"parentId":None}
response=awaitself.client.post("/api/core/dataset/collection/create",json=test_collection)
data=response.json()
logger.info(f"创建集合响应: {json.dumps(data,ensure_ascii=False,indent=2)}")
ifdata.get("code")==200:
            self.collection_id=data.get("collectionId")
logger.info(f"成功保存 collection_id: {self.collection_id}")
else:
            self.fail(f"创建集合失败: {data}")
self.assertIsNotNone(self.collection_id,"Collection ID should not be None")
asyncdeftest_5_push_knowledge_data(self):
        """测试添加知识库数据"""
logger.info("开始测试添加知识库数据...")
ifnotself.collection_idornotself.dataset_id:
            self.fail("Collection ID or Dataset ID is missing")
logger.info(f"Using collection_id: {self.collection_id}")
logger.info(f"Using dataset_id: {self.dataset_id}")
test_data={"collectionId":str(self.collection_id),"datasetId":str(self.dataset_id),"mode":"chunk","prompt":"","docName":"test.txt","data":["《哪吒之魔童闹海》是饺子编剧并执导的奇幻动画电影...","影片上映后成绩斐然，截至 2 月 15 日，票房突破 157.38 亿元..."]}
response=awaitself.client.post("/api/core/dataset/data/pushData",json=test_data)
data=response.json()
logger.info(f"添加知识库数据响应: {json.dumps(data,ensure_ascii=False,indent=2)}")
self.assertEqual(response.status_code,200)
self.assertEqual(data.get("code"),200)
asyncdeftest_6_chat_dialogue(self):
        """测试聊天对话"""
logger.info("开始测试聊天对话...")
awaitself._test_chat_simple()
awaitself._test_chat_detailed()
awaitself._test_chat_stream_simple()
awaitself._test_chat_stream_detailed()
asyncdef_test_chat_simple(self):
        """测试简单聊天场景 (非流式，无详情)"""
logger.info("\n测试场景 1: 非流式，无详情...")
simple_case={"chatId":str(self.chat_id),"datasetId":[str(self.dataset_id)],"stream":False,"detail":False,"messages":[{"role":"user","content":"电影哪吒之魔童闹海谁导演的？"}]}
awaitself._process_chat_response(simple_case)
asyncdef_test_chat_detailed(self):
        """测试详细聊天场景 (非流式，有详情)"""
logger.info("\n测试场景 2: 非流式，有详情...")
detailed_case={"chatId":str(self.chat_id),"datasetId":[str(self.dataset_id)],"stream":False,"detail":True,"messages":[{"role":"user","content":"电影哪吒之魔童闹海谁导演的？"}]}
awaitself._process_chat_response(detailed_case)
asyncdef_process_chat_response(self,test_case):
        """处理聊天响应的通用方法"""
logger.info(f"发送请求: {json.dumps(test_case,ensure_ascii=False,indent=2)}")
response=awaitself.client.post("/api/v1/chat/completions",json=test_case,timeout=60.0)
self.assertEqual(response.status_code,200)
raw_content=response.content.decode('utf-8')
logger.info(f"原始响应内容:\n{raw_content}")
json_responses=[]
forlineinraw_content.split('\n\n'):
            line=line.strip()
ifnotline:
                continue
logger.info(f"处理响应行: {line}")
try:
                ifline.startswith('event:'):
                    event_line=line.split('\n')
iflen(event_line)>=2:
                        event_type=event_line[0].replace('event:','').strip()
event_data=event_line[1].replace('data:','').strip()
logger.info(f"事件类型: {event_type}")
logger.info(f"事件数据: {event_data}")
ifevent_data:
                            try:
                                json_responses.append(json.loads(event_data))
exceptjson.JSONDecodeError:
                                pass
else:
                    json_responses.append(json.loads(line))
exceptjson.JSONDecodeErrorase:
                logger.warning(f"JSON 解析错误: {str(e)}")
logger.warning(f"尝试解析的行: {line}")
continue
llm_response=None
forrespinjson_responses:
            if"choices"inresp:
                llm_response=resp
break
self.assertIsNotNone(llm_response,"No LLM response found in the response")
self.assertIn("choices",llm_response)
self.assertTrue(len(llm_response["choices"])>0)
content=llm_response["choices"][0].get("message",{}).get("content","")
self.assertTrue(len(content)>0,"Response content should not be empty")
logger.info(f"LLM 响应内容: {content}")
asyncdef_test_chat_stream_simple(self):
        """测试流式聊天场景 (流式，无详情)"""
logger.info("\n测试场景 3: 流式，无详情...")
stream_case={"chatId":str(self.chat_id),"datasetId":[str(self.dataset_id)],"stream":True,"detail":False,"messages":[{"role":"user","content":"电影哪吒之魔童闹海谁导演的？"}]}
logger.info(f"发送流式请求: {json.dumps(stream_case,ensure_ascii=False,indent=2)}")
asyncwithself.client.stream("POST","/api/v1/chat/completions",json=stream_case,timeout=60.0)asresponse:
            self.assertEqual(response.status_code,200)
full_response=""
asyncforchunkinresponse.aiter_bytes():
                try:
                    decoded=chunk.decode('utf-8')
forlineindecoded.split('\n\n'):
                        line=line.strip()
ifnotline:
                            continue
data_str=(line.replace('data: ','').strip().strip('"').replace('\\"','"').rstrip('\\n').rstrip())
ifdata_strin["[DONE]","Stream has ended"]:
                            logger.info(f"收到结束标记: {data_str}")
continue
try:
                            data=json.loads(data_str)
logger.info(f"解析的数据: {json.dumps(data,ensure_ascii=False,indent=2)}")
if"choices"indataanddata["choices"]:
                                delta=data["choices"][0].get("delta",{})
content=delta.get("content","")
ifcontent:
                                    full_response+=content
logger.info(f"当前内容: {content}")
logger.info(f"累积响应 (长度={len(full_response)}): {full_response}")
exceptjson.JSONDecodeErrorase:
                            logger.warning(f"JSON解析错误: {e}, 数据: {data_str}")
logger.warning(f"数据字符串长度: {len(data_str)}")
logger.warning(f"数据字符串最后10个字符: {repr(data_str[-10:])}")
continue
ifline.startswith('event:'):
                            event_type=line.replace('event:','').strip()
logger.info(f"事件类型: {event_type}")
exceptUnicodeDecodeErrorase:
                    logger.error(f"解码错误: {str(e)}")
continue
exceptExceptionase:
                    logger.error(f"处理数据块时出错: {str(e)}")
continue
logger.info(f"最终完整响应: {full_response}")
self.assertTrue(len(full_response)>0,"Stream response should not be empty")
asyncdef_test_chat_stream_detailed(self):
        """测试流式聊天场景 (流式，有详情)"""
logger.info("\n测试场景 4: 流式，有详情...")
stream_case={"chatId":str(self.chat_id),"datasetId":[str(self.dataset_id)],"stream":True,"detail":True,"messages":[{"role":"user","content":"电影哪吒之魔童闹海谁导演的？"}]}
logger.info(f"发送流式请求: {json.dumps(stream_case,ensure_ascii=False,indent=2)}")
asyncwithself.client.stream("POST","/api/v1/chat/completions",json=stream_case,timeout=60.0)asresponse:
            self.assertEqual(response.status_code,200)
full_response=""
asyncforchunkinresponse.aiter_bytes():
                try:
                    decoded=chunk.decode('utf-8')
forlineindecoded.split('\n\n'):
                        line=line.strip()
ifnotline:
                            continue
data_str=(line.replace('data: ','').strip().strip('"').replace('\\"','"').rstrip('\\n').rstrip())
ifdata_strin["[DONE]","Stream has ended"]:
                            logger.info(f"收到结束标记: {data_str}")
continue
try:
                            data=json.loads(data_str)
logger.info(f"解析的数据: {json.dumps(data,ensure_ascii=False,indent=2)}")
if"choices"indataanddata["choices"]:
                                delta=data["choices"][0].get("delta",{})
content=delta.get("content","")
ifcontent:
                                    full_response+=content
logger.info(f"当前内容: {content}")
logger.info(f"累积响应 (长度={len(full_response)}): {full_response}")
exceptjson.JSONDecodeErrorase:
                            logger.warning(f"JSON解析错误: {e}, 数据: {data_str}")
logger.warning(f"数据字符串长度: {len(data_str)}")
logger.warning(f"数据字符串最后10个字符: {repr(data_str[-10:])}")
continue
ifline.startswith('event:'):
                            event_type=line.replace('event:','').strip()
logger.info(f"事件类型: {event_type}")
exceptUnicodeDecodeErrorase:
                    logger.error(f"解码错误: {str(e)}")
continue
exceptExceptionase:
                    logger.error(f"处理数据块时出错: {str(e)}")
continue
logger.info(f"最终完整响应: {full_response}")
self.assertTrue(len(full_response)>0,"Stream response should not be empty")
deftest_all(self):
        """运行所有测试"""
logger.info("开始运行所有测试...")
asyncdefrun_tests():
            awaitself.test_1_create_app()
awaitself.test_2_create_chat()
awaitself.test_3_create_knowledge_base()
awaitself.test_4_create_collection()
awaitself.test_5_push_knowledge_data()
awaitself.test_6_chat_dialogue()
try:
            self.loop.run_until_complete(run_tests())
exceptExceptionase:
            logger.error(f"测试过程中发生错误: {str(e)}")
raise
defgenerate_test_report():
    """生成测试报告"""
importpytest
importsys
pytest_args=[__file__,"--capture=sys","--html=test_report.html","--self-contained-html"]
pytest.main(pytest_args)
if__name__=="__main__":
    unittest.main(argv=['first-arg-is-ignored'])


============================================================
文件: backend\docker-compose.yml
============================================================

version: '3.8'
services:
  langflow:
    image: langflow:1.2.0
    container_name: langflow
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - LANGFLOW_DATABASE_URL=********************************************/langflow
      - LANGFLOW_SUPERUSER=admin
      - LANGFLOW_SUPERUSER_PASSWORD=securepassword
      - LANGFLOW_SECRET_KEY=dBuuuB_FHLvU8T9eUNlxQF9ppqRxwWpXXQ42kM2_fbg
      - LANGFLOW_AUTO_LOGIN=False
      - LANGFLOW_NEW_USER_IS_ACTIVE=False
    volumes:
      - langflow-data:/app/langflow
    networks:
      - app-network
  postgres:
    image: postgres:16
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: langflow
      POSTGRES_PASSWORD: langflow
      POSTGRES_DB: langflow
    volumes:
      - langflow-postgres:/var/lib/postgresql/data
    networks:
      - app-network
  wiseagent:
    image: wiseagent:1.0.0
    container_name: wiseagent
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - ENV_MODE=production
    networks:
      - app-network
  nginx:
    image: nginx-proxy:latest
    container_name: nginx
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "8888:80"
    depends_on:
      - langflow
      - wiseagent
    networks:
      - app-network
volumes:
  langflow-postgres:
  langflow-data:
networks:
  app-network:
    driver: bridge

============================================================
文件: backend\old-docker-compose.yml
============================================================

version: '3.10'
services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
    image: wiseagent-app
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: wiseagent-app
    restart: always
    environment:
      - ENV_MODE=prod
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_USER=wiseagent
      - MONGODB_PASSWORD= 1qaz@WSX
      - MONGODB_DATABASE=wiseagent
      - MONGODB_AUTH_SOURCE=${MONGODB_AUTH_SOURCE:-admin}
    command: uvicorn app.main:app --host 0.0.0.0 --port 8800
    ports:
      - "9700:8800"
    env_file:
      - .env.prod
    volumes:
      - /data/wiseagent/static:/app/static
    depends_on:
      - mongodb
    networks:
      - app-network
  mongodb:
    image: mongo:4.4
    container_name: wiseagent-app-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: wiseagent
      MONGO_INITDB_ROOT_PASSWORD: 1qaz@WSX
      MONGO_INITDB_DATABASE: wiseagent
    volumes:
      - /data/wiseagent/mongodb_data:/data/db
    networks:
      - app-network
volumes:
  mongodb_data:
networks:
  app-network:
    driver: bridge 

============================================================
文件: backend\app\conftest.py
============================================================

importasyncio
fromengines.embedding.embedding_utilsimportget_embedding_vector
fromengines.rag.flow_rag_newimportFlowRAG
fromengines.retrieval.base_retrieverimportESKeywordRetriever
fromengines.rerank.rerank_utilsimportrerank_texts
config={"api_key":"sk-BOstXojjXywbG7QlA76eA282A4724e91915d045512239d9e","model":"bge-m3","service_url":"http://122.14.231.165:3001/v1"}
text="这是一个测试文本"
embedding=asyncio.run(get_embedding_vector(text,config))
ifembedding:
    print(f"向量维度: {len(embedding)}")
print(embedding[:10])
test=FlowRAG(config={'vector_search':True,'graph_search':False,'rerank_config':{'api_key':'OTEzZWQ2OTc4YTBlZWY2MDBjYjc5ZGNjMDViYzg1NmIyNThkODg5MQ=='}})
result=test.run('实际控制')
print(result)


============================================================
文件: backend\app\main.py
============================================================

importos
fromfastapiimportFastAPI,Depends
fromfastapi.staticfilesimportStaticFiles
fromfastapi.middleware.corsimportCORSMiddleware
fromapp.routersimportauth,users,rule,groups,role,llm,embedding,authorization,rerank
fromapp.routersimportchat,conversation,system_app_setting,complianceQA,message,marketingQA,feedback,chat2kb,wisegraph_api
fromapp.routersimportstructured_dataset,unstructured_dataset,dataset_file,structured_data_record
fromapp.routersimportconsumerProtectionRule,consumerProtection,wjb_webSeach_agent,financial_webSeach_agent,policy_webSeach_agent,mediaInsightsReport,auditTask
fromapp.routersimportauditTask
fromapp.routersimportbase_system_app_api
fromapp.routersimportknowledge_base,source_files
fromapp.routersimportsystem_settings
fromapp.routersimportuseCases
fromapp.routersimportaiContentRecognition
fromapp.routersimportcustomerAssistant
fromapp.routersimportreportTemplate
fromapp.routersimportprompt
fromapp.routersimportgetContextFromLangflow
fromapp.routersimportcomplaintAnalysis_agent
fromapp.routersimportwise_retrieval
fromapp.routersimportaccess_log
fromapp.apiimportpreLoanAssistant_api,fina_api
fromapp.utils.init_dataimportinit_all
fromfastapi.responsesimportResponse,FileResponse
fromfastapiimportRequest
from.db.mongodbimportmongodb
fromapp.db.miniIOimportminio
fromapp.engines.wisegraph.graph_retrieverimportneo4j
fromapp.utils.configimportsettings
fromapp.utils.logging_configimportsetup_logging,get_logger
setup_logging()
logger=get_logger(__name__)
fromapp.engines.coe.coe_serviceimportCOEService
fromapp.middlewareimportAccessLogMiddleware,PerformanceMonitorMiddleware
api_prefix="/api"
asyncdefinit_data():
    awaitinit_all()
logger.info("--==初始化数据完成==--")
app=FastAPI(title=os.getenv('APP_NAME','RoarData AI App'),debug=os.getenv('DEBUG',True))
app.add_middleware(CORSMiddleware,allow_origins=["*"],allow_credentials=True,allow_methods=["*"],allow_headers=["*"],)
app.add_middleware(AccessLogMiddleware)
app.include_router(auth.router,tags=["auth"])
app.include_router(users.router,tags=["users"])
app.include_router(rule.router,tags=["rule"])
app.include_router(groups.router,tags=["groups"])
app.include_router(role.router,tags=["roles"])
app.include_router(llm.router,tags=["llm"])
app.include_router(embedding.router,tags=["embedding"])
app.include_router(rerank.router,tags=["rerank"])
app.include_router(authorization.router,tags=["authorization"])
app.include_router(conversation.router,tags=["conversation"])
app.include_router(message.router,tags=["message"])
app.include_router(feedback.router,tags=["feedback"])
app.include_router(system_app_setting.router,tags=["system_app_setting"])
app.include_router(structured_dataset.router,tags=["structured_dataset"])
app.include_router(unstructured_dataset.router,tags=["unstructured_dataset"])
app.include_router(dataset_file.router,tags=["dataset_file"])
app.include_router(structured_data_record.router,tags=["structured_data_record"])
app.include_router(knowledge_base.router,tags=["knowledge_base"])
app.include_router(source_files.router,tags=["source_files"])
app.include_router(useCases.router,tags=["useCases"])
app.include_router(consumerProtectionRule.router,tags=["consumerProtectionRule"])
app.include_router(consumerProtection.router,tags=["consumerProtection"])
app.include_router(wjb_webSeach_agent.router,tags=["wjb_webSeach_agent"])
app.include_router(financial_webSeach_agent.router,tags=["financial_webSeach_agent"])
app.include_router(policy_webSeach_agent.router,tags=["policyTrackingTool"])
app.include_router(auditTask.router,tags=["auditTask"])
app.include_router(customerAssistant.router,tags=["customerAssistant"])
app.include_router(reportTemplate.router,tags=["reportTemplate"])
app.include_router(prompt.router,tags=["prompt"])
app.include_router(complaintAnalysis_agent.router,tags=["complaintAnalysis"])
app.include_router(mediaInsightsReport.router,tags=["mediaInsightsReport"])
app.include_router(access_log.router,tags=["access_logs"])
app.include_router(preLoanAssistant_api.router,tags=["preLoanAssistant"])
app.include_router(fina_api.router,tags=["fina"])
app.include_router(base_system_app_api.router,tags=["base_system_app_api"])
app.include_router(chat.router,tags=["chat"])
app.include_router(complianceQA.router,tags=["complianceQA"])
app.include_router(chat2kb.router,tags=["chat2kb"])
app.include_router(wisegraph_api.router,tags=["wisegraph"])
app.include_router(getContextFromLangflow.router,tags=["getContextFromLangflow"])
app.include_router(marketingQA.router,tags=["marketingQA"])
app.include_router(aiContentRecognition.router,tags=["aiContentRecognition"])
app.include_router(system_settings.router)
app.include_router(wise_retrieval.router,tags=["wise_retrieval"])
current_dir=os.path.dirname(os.path.abspath(__file__))
static_dir=os.path.join(current_dir,"static")
app.mount("/static",StaticFiles(directory=static_dir,html=True),name="static")
@app.get("/{full_path:path}",include_in_schema=False)
asyncdefserve_spa_index(request:Request):
    path=request.url.path
ifpath.startswith(api_prefix):
        returnResponse(status_code=404)
index_path=os.path.join(static_dir,"index.html")
ifos.path.exists(index_path):
        returnFileResponse(index_path)
logger.error(f"index.html not found at {index_path}")
returnResponse("前端入口点未找到。",status_code=500)
@app.on_event("startup")
asyncdefon_startup():
    logger.info("--==开始启动应用==--")
try:
        logger.info("环境变量已加载完成")
mongodb.connect()
logger.info("数据库连接初始化完成")
ifsettings.GRAPH_RAG:
            awaitneo4j.initialize()
logger.info("Neo4j连接初始化完成")
else:
            logger.info("Neo4j未启用")
ifsettings.MINIO_ENABLE:
            awaitminio.connect()
logger.info("MinIO 连接初始化完成")
else:
            logger.info("MinIO 未启用")
awaitinit_data()
logger.info("--==应用启动完成==--")
awaitCOEService.initialize()
ifsettings.INDEXER_ENABLED:
            fromapp.engines.indexing.base_indexerimportBaseIndexer
fromapscheduler.schedulers.backgroundimportBackgroundScheduler
scheduler=BackgroundScheduler()
indexer=BaseIndexer()
scheduler.add_job(indexer.execute,'interval',seconds=10)
scheduler.start()
logger.info("--==索引器初始化完成==--")
else:
            logger.info("--==索引器未启用==--")
exceptExceptionase:
        logger.error(f"应用启动失败: {str(e)}")
raisee
@app.on_event("shutdown")
asyncdefshutdown_db_client():
    fromapp.middleware.access_loggerimportshutdown_event
awaitshutdown_event()
logger.info("访问日志已完成最终刷新")
awaitmongodb.disconnect()
ifsettings.GRAPH_RAG:
        awaitneo4j.close()
logger.info("Neo4j连接已关闭")
ifsettings.MINIO_ENABLE:
        awaitminio.disconnect()
logger.info("MinIO 连接已关闭")
logger.info("--==应用关闭，断开数据库连接==--")


============================================================
文件: backend\app\role_tree.py
============================================================

routes=[{"name":'UseCases',"access":'canAccessUseCases',},{"name":'LLMmarket',"access":'canAccessLLMmarket',"routes":[{"name":'llmChat',"access":'canAccessLLMmarketLlmChat',},{"name":'llmModels',"access":'canAccessLLMmarketModels',},{"name":'llmComparison',"access":'canAccessLLMmarketLlmComparison',},],},{"name":'LMTools',"access":'canAccessLMTools',"routes":[{"name":'llmodelTuning',"access":'canAccessLLModelTuning',},{"name":'llmodelEvaluation',"access":'canAccessModelEvaluation',},],},{"name":"admin","access":"canAccessAdmin","routes":[{"name":"userManagement","access":"canAccessUserManagement"},{"name":"roleManagement","access":"canAccessRoleManagement"},{"name":"systemManagement","access":"canAccessSystemManagement"},{"name":"groupManagement","access":"canAccessGroupManagement"},{"name":'systemAppSettings',"access":'canAccessSystemAppSettings',},{"name":'systemAppInfo',"access":'canViewSystemAppInfo',},{"name":"conversationManagement","access":"canAccessConversationManagement"},{"name":"messageManagement","access":"canAccessMessageManagement"},{"name":"promptAdminManagement","access":"canAccessPromptAdminManagement"},{"name":"useCasesManagement","access":"canAccessUseCasesManagement"},{"name":"knowledgeBaseManagement","access":"canAccessKnowledgeBaseManagement"},{"name":"mcpManagement","access":"canAccessMcpManagement"},{"name":"announcementManagement","access":"canAccessAnnouncementManagement"},{"name":"logManagement","access":"canAccessLogManagement"},{"name":"ruleManagement","access":"canAccessRuleManagement"},]},{"name":"account","access":"canAccessAccount","routes":[{"name":"center","access":"canAccessCenter"},{"name":"settings","access":"canAccessSettings"}]},{"name":"modelManagement","access":"canAccessModelManagement","routes":[{"name":"largeLanguageModel","access":"canAccessLargeLanguageModel"},{"name":"embeddingModel","access":"canAccessEmbeddingModel"},{"name":"rerankModel","access":"canAccessRerankModel"},{"name":"modelAuthorization","access":"canAccessModelAuthorization"}]},{"name":"knowledgeManagement","access":"canAccessKnowledgeManagement","routes":[{"name":"knowledgeBase","access":"canAccessKnowledgeBase"},{"name":'knowledgeInfo',"access":'canAccessKnowledgeInfo',},{"name":'fileInfo',"access":'canAccessFileInfo',},{"name":"knowledgeDashboard","access":"canAccessKnowledgeDashboard"},{"name":"knowledgeQuestionAnswer","access":"canAccessKnowledgeQuestionAnswer"}]},{"name":"promotBase","access":"canAccessPromotBase",},{"name":"mcpSquare","access":"canAccessMCPSquare",},{"name":"FlowManagement","access":"canAccessFlowManagement","routes":[{"name":"wiseflow","access":"canAccessWiseflow"},{"name":"talklist","access":"canAccessTalklist"}]},{"name":"complianceAssistant","access":"canAccessComplianceAssistant","routes":[{"name":"metasploitAssistant","access":"canAccessMetasploitAssistant"},{"name":"complianceQA","access":"canAccessComplianceQA"},{"name":"complianceQA_EU","access":"canAccessComplianceQA_EU"},{"name":"caseAnalysis","access":"canAccessCaseAnalysis"},{"name":"LitigationCase","access":"canAccessLitigationCase"},{"name":"informationRetrieval","access":"canAccessInformationRetrieval"},]},{"name":"fileAuditAssistant","access":"canAccessFileAuditAssistant","routes":[{"name":"offlineAuditTaskList","access":"canAccessOfflineAuditTaskList"},{"name":"offlineAuditTask","access":"canAccessOfflineAuditTask"},{"name":"documentReviewCenter","access":"canAccessDocumentReviewCenter"}]},{"name":"aiContentRecognition","access":"canAccessAiContentRecognition","routes":[{"name":"ocrRecognition","access":"canAccessOcrRecognition"},{"name":"layoutRecognition","access":"canAccessLayoutRecognition"},{"name":"endToEndRecognition","access":"canAccessEndToEndRecognition"},{"name":"multimodalRecognition","access":"canAccessMultimodalRecognition"}]},{"name":'ComplaintQuestionAnswer',"access":'ComplaintQuestionAnswer',},{"name":'IntelligentReview',"access":'canAccessIntelligentReview',},{"name":'KnowledgeCenter',"access":'canAccessKnowledgeCenter',},{"name":'ComplaintAnalysis',"access":'canAccessComplaintAnalysis',},{"name":"WebInfoAssistant","access":"canAccessWebInfoAssistant","routes":[{"name":"DiplomaticIntelligentSearch","access":"canAccessDiplomaticIntelligentSearch"},{"name":"FinancialReputationRisk","access":"canAccessFinancialReputationRisk"},{"name":"PolicyTrackingTool","access":"canAccessPolicyTrackingTool"},{"name":"ReputationRiskCase","access":"canAccessReputationRiskCase"}]},{"name":'MediaInsightsReport',"access":'canAccessMediaInsightsReport',},{"name":"BusinessOpportunity","access":"canAccessBusinessOpportunity","routes":[{"name":"businessOpportunityAssistant","access":"canAccessBusinessOpportunityAssistant"},{"name":"AssistantSettings","access":"canAccessAssistantSettings"}]},{"name":"BidAssistant","access":"canAccessBidAssistant","routes":[{"name":"Qualification","access":"canAccessQualification"},{"name":"ContractCase","access":"canAccessContractCase"}],},{"name":'SolutionAssistant',"access":'canAccessSolutionAssistant',"routes":[{"name":'SolutionGeneration',"access":'canAccessSolutionGeneration',},{"name":'HistorySolution',"access":'canAccessHistorySolution',},{"name":'SolutionRetrieval',"access":'canAccessSolutionRetrieval',},],},{"name":'NegotiationAssistant',"access":'canAccessNegotiationAssistant',"routes":[{"name":'DocumentGeneration',"access":'canAccessDocumentGeneration',},{"name":'StrategyManagement',"access":'canAccessStrategyManagement',},],},{"name":"dataAnalysisAssistant","access":"canAccessDataAnalysisAssistant","routes":[{"name":"dataExploration","access":"canAccessDataExploration"},{"name":"statisticalAnalysis","access":"canAccessStatisticalAnalysis"},{"name":"visualAnalysis","access":"canAccessVisualAnalysis"}]},{"name":"customerAssistant","access":"canAccessCustomerAssistant","routes":[{"name":"customerServiceBot","access":"canAccessCustomerServiceBot"},{"name":"customerKnowledgeAssistant","access":"canAccessCustomerKnowledgeAssistant"},{"name":"workOrderAssistant","access":"canAccessWorkOrderAssistant"},{"name":"scriptMining","access":"canAccessScriptMining"},{"name":"ITIncidentReporting","access":"canAccessITIncidentReporting"},{"name":"PublicOpinionClsureReport","access":"canAccessPublicOpinionClsureReport"},{"name":"PublicOpinionInitialReport","access":"canAccessPublicOpinionInitialReport"},]},{"name":"marketingAssistant","access":"canAccessMarketingAssistant","routes":[{"name":"marketingQA","access":"canAccessMarketingQA"}]},{"name":"IntelligentRetrieval","access":"canAccessIntelligentRetrieval",},{"name":"voiceAssistant","access":"canAccessVoiceAssistant","routes":[{"name":"realTimeVoiceRecognition","access":"canAccessRealTimeVoiceRecognition"}]},{"name":'entityComplianceReview',"access":'canAccessEntityComplianceReview',},{"name":"preLoanAssistant","access":"canAccessPreLoanAssistant","routes":[{"name":"customerInvestigation","access":"canAccessCustomerInvestigation"},{"name":"creditReport","access":"canAccessCreditReport"},{"name":"aiReportGeneration","access":"canAccessAiReportGeneration"},{"name":"reportTemplateManagement","access":"canAccessReportTemplateManagement"},{"name":"reportTaskManagement","access":"canAccessReportTaskManagement"},{"name":"createReportTemplate","access":"canAccessCreateReportTemplate"}]},{"name":"midLoanAssistant","access":"canAccessMidLoanAssistant","routes":[{"name":"contractReview","access":"canAccessContractReview"},{"name":"loanMonitoring","access":"canAccessLoanMonitoring"}]},{"name":"postLoanAssistant","access":"canAccessPostLoanAssistant","routes":[{"name":"riskMonitoring","access":"canAccessRiskMonitoring"}]},{"name":"Dataset","access":"canAccessDataset","routes":[{"name":"structuredData","access":"canAccessStructuredData","routes":[{"name":"newStructuredData","access":"canCreateStructuredData",},{"name":"viewStructuredData","access":"canViewStructuredData",},],},{"name":"unstructuredData","access":"canAccessUnstructuredData","routes":[{"name":"newUnstructuredData","access":"canCreateUnstructuredData",},{"name":"viewUnstructuredData","access":"canViewUnstructuredData",},],},],},]


============================================================
文件: backend\app\agent\agents.py
============================================================

fromdataclassesimportdataclass
fromlanggraph.graph.stateimportCompiledStateGraph
from.schema.schemaimportAgentInfo
from.chatbotimportchatbot
DEFAULT_AGENT="chatbot"
CHAT_AGENT="chatbot"
@dataclass
classAgent:
    description:str
graph:CompiledStateGraph
agents:dict[str,Agent]={"chatbot":Agent(description="一个简单的聊天机器人.",graph=chatbot),}
defget_agent(agent_type=None):
    """
    获取对应类型的智能体
    """
ifagent_type==CHAT_AGENT:
        returnchatbot
else:
        returnchatbot
defget_agent_output_filter(agent_type=None):
    """
    获取对应类型的智能体输出过滤器
    """
returnNone
defget_all_agent_info()->list[AgentInfo]:
    return[AgentInfo(key=agent_id,description=agent.description)foragent_id,agentinagents.items()]
defget_all_agent_info()->list[AgentInfo]:
    return[AgentInfo(key=agent_id,description=agent.description)foragent_id,agentinagents.items()]


============================================================
文件: backend\app\agent\agent_router.py
============================================================

importjson
importtraceback
fromcollections.abcimportAsyncGenerator
fromdatetimeimportdatetime
fromtypingimportAny,Dict,List
importuuid
frombson.objectidimportObjectId
fromdotenvimportload_dotenv
fromfastapiimportAPIRouter,Depends,HTTPException,Query,status
fromfastapi.responsesimportStreamingResponse
fromfastapi.securityimportHTTPAuthorizationCredentials,HTTPBearer
fromlangchain_core.messagesimportAIMessage,AnyMessage,HumanMessage
fromlangchain_core.runnablesimportRunnableConfig
fromlanggraph.graph.stateimportCompiledStateGraph
fromlanggraph.typesimportCommand
frompydanticimportBaseModel
from.agentsimportCHAT_AGENT,DEFAULT_AGENT,get_agent,get_agent_output_filter
from.utilsimportconvert_message_content_to_string,remove_tool_calls
from..db.mongodbimportdb
from..utils.logging_configimportget_logger,setup_logging
setup_logging()
logger=get_logger(__name__)
asyncdefrobot_message_generator(system_app_params:Dict[str,Any],messages:List[Dict[str,Any]],stream:bool)->AsyncGenerator[str,None]:
    """
    Generate a stream of messages from the agent in OpenAI-compatible format.

    Args:
        kwargs: Dictionary containing input messages and configuration
        id: 会话ID，用于标识此次对话

    Returns:
        AsyncGenerator yielding SSE formatted messages
    """
agent_type=system_app_params.get('AGENT_TYPE',None)
llm_id=system_app_params.get('MODEL_ID',None)
llm=awaitdb["llms"].find_one({"id":int(llm_id)})
logger.info(llm)
model_name=llm.get('m_name',None)
all_generated_content=[]
processed_messages=[]
id=str(uuid.uuid4())
created_timestamp=int(datetime.now().timestamp())
message_counter=1
ifagent_typeisNone:
        created_timestamp=int(datetime.now().timestamp())
content="智能体类型不存在，请联系管理员"
error_chunk={"status":"error","message":content}
yieldf"event: error\ndata: {json.dumps(error_chunk)}\n\n"
processed_messages.append(content)
else:
        logger.info(f"启用智能体: {agent_type}")
logger.info(f"========================启用智能体: {agent_type}")
agent:CompiledStateGraph=get_agent(agent_type)
output_filter=get_agent_output_filter(agent_type)
formsginmessages:
            ifisinstance(msg,dict)and"role"inmsgand"content"inmsg:
                role=msg["role"]
content=msg["content"]
ifrole=="user":
                    processed_messages.append(HumanMessage(content=content))
elifrole=="assistant":
                    processed_messages.append(AIMessage(content=content))
else:
                processed_messages.append(msg)
messages_list=[{'role':msg.type,'content':msg.content}formsginprocessed_messages]
logger.info("已处理的消息列表:")
foridx,msginenumerate(messages_list,1):
            logger.info(f"  消息 {idx} ：角色: {msg['role']}")
logger.info(f"  内容: {msg['content']}")
logger.info(f"----------------------------")
logger.info(f"===============================================")
first_chunk={"id":id,"created":created_timestamp,"model":model_name,"choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":None}]}
logger.info(f"first_chunk: {first_chunk}")
yieldf"id: test_id_{message_counter}\ndata: {json.dumps(first_chunk)}\n\n"
message_counter+=1
thread_id=str(uuid.uuid4())
ifagent_type==CHAT_AGENT:
            logger.info(f"=================CHAT_AGENT=========================")
run_kwargs={"input":{"messages":processed_messages},"config":RunnableConfig(configurable={"system_app_params":system_app_params,"llm_id":int(llm_id),"thread_id":thread_id},run_id=id),}
else:
            logger.info(f"=================DEFAULT_AGENT=========================")
run_kwargs={"input":{"messages":processed_messages},"config":RunnableConfig(configurable={"system_app_params":system_app_params,"llm_id":int(llm_id),"thread_id":thread_id},run_id=id),}
asyncforeventinagent.astream_events(**run_kwargs,version="v2"):
            ifnotevent:
                continue
created_timestamp=int(datetime.now().timestamp())
logger.info(f"============》event: {event}")
if(event["event"]=="on_chat_model_stream"andstream):
                ifoutput_filter:
                    if"on_chat_model_stream"inoutput_filter:
                        tags_list=output_filter.get("on_chat_model_stream",None)
iftags_list:
                            langgraph_node=event["metadata"].get("langgraph_node",None)
iflanggraph_nodenotintags_list:
                                continue;
logger.info("==========#############################>on_chat_model_stream")
content=None
if"data"ineventand"chunk"inevent["data"]:
                    chunk_data=event["data"]["chunk"]
ifhasattr(chunk_data,"content"):
                        content=chunk_data.content
ifcontent:
                    chunk={"id":id,"created":created_timestamp,"model":model_name,"choices":[{"index":0,"delta":{"content":convert_message_content_to_string(content)},"finish_reason":None}]}
all_generated_content.append(convert_message_content_to_string(content))
logger.info(f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n")
yieldf"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
message_counter+=1
continue
ifevent["event"]=="on_chain_stream":
                processed_content=None
ifoutput_filter:
                    if"on_chain_stream"inoutput_filter:
                        tags_map=output_filter.get("on_chain_stream",None)
metadata=event.get("metadata",None)
ifmetadataandtags_map:
                            langgraph_node=metadata.get("langgraph_node",None)
iflanggraph_node:
                                out_key=tags_map.get(langgraph_node,None)
data=event.get("data",{})
chunk=data.get("chunk",{})ifdataelse{}
ifnotout_key:
                                    logger.info(f"过滤节点:{langgraph_node}没有匹配的输出键")
continue
if"next_question"inchunk:
                                    logger.info(f"找到next_question: {chunk['next_question']}")
processed_content=chunk["next_question"]
elifout_keyinchunk:
                                    logger.info(f"找到{out_key}: {chunk[out_key]}")
processed_content=chunk[out_key]
elif"agent_params"inchunkandout_keyinchunk["agent_params"]:
                                    logger.info(f"在agent_params中找到{out_key}: {chunk['agent_params'][out_key]}")
processed_content=chunk["agent_params"][out_key]
else:
                                    logger.warning(f"未找到输出内容，langgraph_node={langgraph_node}, out_key={out_key}")
logger.debug(f"chunk内容: {json.dumps(chunk,default=str)[:200]}...")
ifprocessed_content:
                    chunk={"id":id,"created":created_timestamp,"model":model_name,"choices":[{"index":0,"delta":{"content":processed_content},"finish_reason":None}]}
all_generated_content.append(processed_content)
logger.info('=========================发送流式输出')
logger.info(f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n")
yieldf"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
message_counter+=1
continue
final_chunk={"id":id,"created":created_timestamp,"model":model_name,"choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}
yieldf"id: test_id_{message_counter}\ndata: {json.dumps(final_chunk)}\n\n"
ifall_generated_content:
        complete_content=''.join(all_generated_content)
logger.info("==================完整内容==================")
logger.info(complete_content)
logger.info("==================完整内容结束==================")
logger.info("################结束#############")


============================================================
文件: backend\app\agent\chatbot.py
============================================================

fromlangchain_core.language_models.chat_modelsimportBaseChatModel
fromlangchain_core.messagesimportAIMessage,HumanMessage,SystemMessage
fromlangchain_core.promptsimportSystemMessagePromptTemplate
fromlangchain_core.runnablesimport(RunnableConfig,RunnableLambda,RunnableSerializable)
fromlanggraph.checkpoint.memoryimportMemorySaver
fromlanggraph.graphimportEND,MessagesState,StateGraph
from.core.llmimportget_model
fromapp.utils.logging_configimportget_logger,setup_logging
setup_logging()
logger=get_logger(__name__)
classAgentState(MessagesState,total=False):
    """`total=False` is PEP589 specs.

    documentation: https://typing.readthedocs.io/en/latest/spec/typeddict.html#totality
    """
defwrap_model(model:BaseChatModel)->RunnableSerializable[AgentState,AIMessage]:
    preprocessor=RunnableLambda(lambdastate:state["messages"],name="StateModifier",)
returnpreprocessor|model
asyncdefacall_model(state:AgentState,config:RunnableConfig)->AgentState:
    logger.info('===========>acall_model')
thread_id=config.get("configurable",{}).get("thread_id","default")
llm_id=config.get("configurable",{}).get("llm_id",1)
logger.info(f"使用模型ID: {llm_id}")
m=awaitget_model(llm_id,stream=True)
model_runnable=wrap_model(m)
system_prompt="你是一个简洁高效的助手，你的名字是清清。请用简短的语言回答用户问题，尽量不超过三句话。使用中文回答，保持回答友好、准确且直接。"
ifnotany(isinstance(msg,SystemMessage)formsginstate.get("messages",[])):
        system_msg=SystemMessage(content=system_prompt)
state["messages"]=[system_msg]+state.get("messages",[])
response=awaitmodel_runnable.ainvoke(state,config)
logger.info(f"模型响应: {response}")
logger.info(f"Thread {thread_id} response: {response}")
updated_messages=state.get("messages",[])+[response]
return{"messages":updated_messages}
agent=StateGraph(AgentState)
agent.add_node("model",acall_model)
agent.set_entry_point("model")
agent.add_edge("model",END)
chatbot=agent.compile(checkpointer=MemorySaver(),)


============================================================
文件: backend\app\agent\daliy_his_weixin.py
============================================================

importlogging
importtime
fromdatetimeimportdatetime,timedelta
importtraceback
fromfinancialPublicOpinionReport_weixin_V5_4importrun_agent
logging.basicConfig(level=logging.INFO,format='%(asctime)s | %(levelname)-8s | %(message)s')
logger=logging.getLogger(__name__)
defprint_banner():
    """打印横幅"""
banner="""
============================================================
                金融舆情分析智能体 - 历史报告生成
                     版本: V5.4
            启动时间: {}
============================================================
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
print(banner)
defrun_historical_reports(days=1):
    """循环生成近30天（每天一份）的金融舆情分析报告"""
print_banner()
logger.info("+"+"="*60+"+")
logger.info("|"+" "*20+"开始批量运行金融舆情分析智能体"+" "*19+"|")
logger.info("+"+"="*60+"+")
all_results=[]
total_reports=27
success_count=0
error_count=0
foriinrange(3,30):
        fetch_end_time=datetime.now().replace(hour=0,minute=0,second=0,microsecond=0)-timedelta(days=i)
fetch_start_time=fetch_end_time-timedelta(days=days)
try:
            start_time=datetime.now()
progress=f"[{i-2:2d}/{total_reports}]"
date_range=f"{fetch_start_time.strftime('%Y-%m-%d')} 至 {fetch_end_time.strftime('%Y-%m-%d')}"
logger.info("-"*70)
logger.info(f"{progress} 开始生成报告...")
logger.info(f"日期区间: {date_range}")
result=run_agent(fetch_start_time,fetch_end_time)
ifresult.get("status")=="success":
                end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
success_count+=1
logger.info(f"{progress} [成功] 金融舆情分析报告生成完成")
logger.info(f"        耗时: {elapsed_time:.2f} 秒")
logger.info(f"        存储路径: {result.get('storage_path','未知')}")
result["date"]=fetch_end_time.strftime("%Y-%m-%d")
all_results.append(result)
else:
                error_count+=1
logger.error(f"{progress} [失败] {result.get('message','未知错误')}")
all_results.append({"status":"error","message":result.get("message","未能生成报告文本"),"date":fetch_end_time.strftime("%Y-%m-%d")})
percent=(i-2)/total_reports*100
progress_bar="="*int(percent/2)+">"+" "*(50-int(percent/2))
logger.info(f"进度: [{progress_bar}] {percent:3.1f}%")
ifi<29:
                logger.info("等待5秒后继续...")
time.sleep(5)
exceptExceptionase:
            error_count+=1
logger.error(f"{progress} [错误] {str(e)}")
traceback.print_exc()
all_results.append({"status":"error","message":str(e),"date":fetch_end_time.strftime("%Y-%m-%d")})
logger.info("+"+"="*60+"+")
logger.info("|"+" "*25+"生成报告完成"+" "*24+"|")
logger.info("+"+"="*60+"+")
logger.info(f"总报告数: {total_reports}  成功: {success_count}  失败: {error_count}")
returnall_results
if__name__=="__main__":
    run_historical_reports()


============================================================
文件: backend\app\agent\daliy_schedule_weixin.py
============================================================

importlogging
importschedule
importtime
fromdatetimeimportdatetime,timedelta
fromtypingimportOptional,Dict
importtraceback
fromfinancialPublicOpinionReport_weixin_V5_4importrun_agent
logging.basicConfig(level=logging.INFO,format='%(asctime)s | %(levelname)-8s | %(message)s')
logger=logging.getLogger(__name__)
defprint_banner():
    """打印横幅"""
banner="""
============================================================
                金融舆情分析智能体 - 定时任务调度
                     版本: V5.4
            启动时间: {}
============================================================
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
print(banner)
defrun_financial_report_agent(days:Optional[int]=3)->Dict:
    """
    运行金融舆情分析智能体
    
    Args:
        days (int, optional): 分析的天数范围. 默认为3天.
        
    Returns:
        Dict: 运行结果
    """
try:
        logger.info("+"+"="*60+"+")
logger.info("|"+" "*20+f"开始分析近 {days} 天的数据"+" "*20+"|")
logger.info("+"+"="*60+"+")
end_time=datetime.now()
start_time=end_time-timedelta(days=days)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"分析时间范围：{start_time_str} 至 {end_time_str}")
result=run_agent(start_time,end_time)
ifresult.get("status")=="success":
            logger.info("[成功] 金融舆情分析报告生成成功")
logger.info(f"报告存储路径: {result.get('storage_path','未知')}")
else:
            logger.error(f"[失败] 金融舆情分析报告生成失败: {result.get('message','未知错误')}")
returnresult
exceptExceptionase:
        error_msg=f"运行金融舆情分析智能体时发生错误: {str(e)}"
logger.error(f"[错误] {error_msg}")
logger.error(traceback.format_exc())
return{"status":"error","message":error_msg}
defschedule_daily_report():
    """设置每日定时任务"""
try:
        schedule.every().day.at("18:00").do(run_financial_report_agent)
logger.info("[设置] 已设置每天下午6点运行金融舆情分析智能体")
next_run=schedule.next_run()
ifnext_run:
            time_diff=next_run-datetime.now()
hours,remainder=divmod(time_diff.seconds,3600)
minutes,seconds=divmod(remainder,60)
logger.info("-"*70)
logger.info(f"下次运行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"距离下次运行还有: {hours}小时{minutes}分钟")
logger.info("-"*70)
logger.info("[运行] 调度器已启动，等待执行...")
whileTrue:
            schedule.run_pending()
time.sleep(60)
exceptExceptionase:
        logger.error(f"[错误] 调度器运行出错: {str(e)}")
logger.error(traceback.format_exc())
if__name__=="__main__":
    print_banner()
logger.info("[启动] 启动金融舆情分析智能体定时任务")
schedule_daily_report()


============================================================
文件: backend\app\agent\financialPublicOpinionReport_weixin.py
============================================================

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""
importjson
importlogging
importtraceback
importsignal
importthreading
importplatform
importos
importfunctools
importconcurrent.futures
fromconcurrent.futuresimportThreadPoolExecutor
fromcontextlibimportcontextmanager
fromdatetimeimportdatetime,timedelta
fromtypingimportDict,List,Any,Optional,TypedDict,Callable
importpandasaspd
fromelasticsearchimportElasticsearch
fromlangchain_openaiimportChatOpenAI
fromlangchain_core.messagesimportHumanMessage,SystemMessage,AIMessage
fromlangchain_core.promptsimportChatPromptTemplate
fromlanggraph.graphimportStateGraph,END
classTimeoutException(Exception):
    pass
deftimeout(seconds):
    defdecorator(func):
        @functools.wraps(func)
defwrapper(*args,**kwargs):
            start_time=datetime.now()
func_name=func.__name__
logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
withThreadPoolExecutor(max_workers=1)asexecutor:
                future=executor.submit(func,*args,**kwargs)
try:
                    result=future.result(timeout=seconds)
elapsed=(datetime.now()-start_time).total_seconds()
logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
returnresult
exceptconcurrent.futures.TimeoutError:
                    future.cancel()
elapsed=(datetime.now()-start_time).total_seconds()
logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
raiseTimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
returnwrapper
returndecorator
IS_UNIX=platform.system()in('Linux','Darwin')andos.name=='posix'
ifIS_UNIX:
    @contextmanager
deftimeout_context(seconds):
        defhandle_timeout(signum,frame):
            raiseTimeoutException(f"操作超时（{seconds}秒）")
original_handler=signal.getsignal(signal.SIGALRM)
signal.signal(signal.SIGALRM,handle_timeout)
try:
            signal.alarm(seconds)
yield
finally:
            signal.alarm(0)
signal.signal(signal.SIGALRM,original_handler)
else:
    @contextmanager
deftimeout_context(seconds):
        defdo_nothing():
            pass
withThreadPoolExecutor(max_workers=1)asexecutor:
            future=executor.submit(do_nothing)
try:
                future.result(timeout=0.001)
yield
exceptconcurrent.futures.TimeoutError:
                logger.warning("初始化超时上下文出错")
yield
logging.basicConfig(level=logging.INFO,format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger=logging.getLogger(__name__)
TARGET_ES_HOST="**************"
TARGET_ES_PORT=9600
TARGET_ES_INDEX="pro_mcp_data_weixin"
LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
BIZ_LIST=["MzA4MDY1NTUyMg==","MzA5OTY5MzM4Ng==","Mzg2Mjg1NTg3NA==","Mzg4NTEwMzA5NQ==","Mzg5MjU4MDkyMw==","MzI4NTU0NDE4Mw==","MzI5MzQxOTI0MQ==","MzIwMDI3NjM2Mg==","MzIzMjc3NTYyNQ==","Mzk0NjUwMDIxOQ==","MzkxMDYyNzExMA==","MzkzNTYzMDYxMg==","MzU3MDMwODc2MA==","MzU4NzcwNDcxOA==","MzU4ODM4NzI5Nw==","MzU5MzkzMTY3Mg==","MzUxMDk5NDgwNQ==","MzUxMDkyMzA4Mw==","MzUzMzEyODIyMA==","MzUzNDcxMDgzNg==","MzA3MTIzNDcwMg==","MzA3NjU1NTQwMA==","MzA4MzY2ODYwMw==","MzAwNzMxODYyNg==","Mzg4NDU2MDM3Mw==","MzI1NzAwODc3Nw==","MzU3MDMwODc2MA==","MzU3NTYyNTIyMQ==","MzUzMzYwODI2MA=="]
llm=ChatOpenAI(temperature=0,model=LLM_MODEL,openai_api_base=LLM_API_BASE,api_key=OPENAI_API_KEY,request_timeout=120)
defsafe_llm_invoke(messages,timeout_seconds=120,default_response=None,description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
try:
        @timeout(timeout_seconds)
definvoke_with_timeout(msgs):
            returnllm.invoke(msgs)
result=invoke_with_timeout(messages)
logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
returnresult
exceptTimeoutExceptionase:
        logger.error(f"{description}超时: {str(e)}")
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
exceptExceptionase:
        logger.error(f"{description}失败: {str(e)}")
traceback.print_exc()
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
defget_es_scroll_data_batched(index,query_body,batch_size=1000,es_host=None,es_port=None):
    """滚动查询ES数据，并以批次方式返回"""
ifnotes_host:
        es_host=TARGET_ES_HOST
ifnotes_port:
        es_port=TARGET_ES_PORT
es=Elasticsearch([f"{es_host}:{es_port}"])
sid=None
try:
        result=es.search(index=index,scroll='10m',body=query_body,size=batch_size,request_timeout=3600)
sid=result['_scroll_id']
scroll_size=result['hits']['total']['value']
logger.info(f"索引 {index} 总数据量: {scroll_size}")
iflen(result['hits']['hits'])>0:
            yieldresult['hits']['hits']
scroll_count=len(result['hits']['hits'])
whilescroll_count>0:
            result=es.scroll(scroll_id=sid,scroll='10m',request_timeout=3600)
batch_data=result['hits']['hits']
scroll_count=len(batch_data)
ifscroll_count==0:
                break
yieldbatch_data
exceptExceptionase:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
traceback.print_exc()
finally:
        ifsid:
            try:
                es.clear_scroll(scroll_id=sid)
except:
                pass
ifes:
            try:
                es.close()
except:
                pass
logger.info(f"索引 {index} 查询完成")
defbuild_query_body(start_time,end_time,biz=None):
    """构建ES查询体"""
body={"query":{"bool":{"filter":[{"range":{"createTimeES":{"gte":start_time,"lte":end_time}}}]}},"sort":[{"createTimeES":"desc"}]}
ifbiz:
        ifisinstance(biz,str):
            body["query"]["bool"]["filter"].append({"term":{"site_id":{"value":biz}}})
elifisinstance(biz,list)andlen(biz)>0:
            body["query"]["bool"]["filter"].append({"terms":{"site_id":biz}})
returnbody
defget_recent_reports(hours=24):
    """获取近24小时内的报告数据"""
end_time=datetime.now()
start_time=end_time-timedelta(hours=hours)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")
query_body=build_query_body(start_time_str,end_time_str,BIZ_LIST)
all_data=[]
forbatchinget_es_scroll_data_batched(TARGET_ES_INDEX,query_body):
        all_data.extend([doc['_source']fordocinbatch])
logger.info(f"获取到 {len(all_data)} 条记录")
returnall_data
defget_latest_report_by_account(days=3):
    """获取每个公众号最近3天的最新一篇报告"""
end_time=datetime.now()
start_time=end_time-timedelta(days=days)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")
latest_reports={}
forbizinBIZ_LIST:
        query_body=build_query_body(start_time_str,end_time_str,biz)
query_body["size"]=1
try:
            es=Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
result=es.search(index=TARGET_ES_INDEX,body=query_body)
ifresult['hits']['hits']:
                doc=result['hits']['hits'][0]['_source']
latest_reports[biz]={'title':doc.get('title','无标题'),'content':doc.get('content','无内容'),'author':doc.get('new_author','未知作者'),'site_name':doc.get('source','未知公众号'),'publishtime':doc.get('publishtime','未知时间'),'authorViewpoint':doc.get('authorViewpoint',''),'characterEntity':doc.get('characterEntity',[]),'institutionalEntities':doc.get('institutionalEntities',[]),'locationEntity':doc.get('locationEntity',[]),'eventInfo':doc.get('eventInfo',[]),'summaryFacts':doc.get('summaryFacts',[]),'summary':doc.get('summary','')}
logger.info(f"获取到公众号 {doc.get('source',biz)} 最新文章{doc.get('publishtime','未知时间')}: {doc.get('title','无标题')}")
exceptExceptionase:
            logger.error(f"获取公众号 {biz} 最新报告时出错: {str(e)}")
finally:
            ifes:
                es.close()
logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告")
returnlatest_reports
defanalyze_report_with_reasoning(title,content,account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
system_msg=SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")
human_msg=HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000]ifcontentandlen(content)>6000elsecontent}
""")
logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000]ifcontentandlen(content)>6000elsecontent)}")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=120,default_response=default_analysis,description=f"'{account_name}'报告初始分析")
logger.info("开始生成结构化分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=60,default_response=default_result,description=f"'{account_name}'报告结构化分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints',[]))} 条核心观点")
returnanalysis
else:
            logger.error("无法找到JSON内容")
return{"summary":"无法解析报告内容","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"无法分析","reasoning_chain":[]}
exceptExceptionase:
        logger.error(f"分析报告内容时出错: {str(e)}")
return{"summary":"分析过程出错","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"分析出错","reasoning_chain":[]}
defanalyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
all_viewpoints=[]
all_entities=set()
all_institutions=set()
all_events=set()
report_summaries=[]
forbiz,reportinreports_data.items():
        if'analysis'inreportandreport['analysis'].get('core_viewpoints'):
            forviewpointinreport['analysis']['core_viewpoints']:
                all_viewpoints.append({'site_name':report['site_name'],'viewpoint':viewpoint})
ifreport.get('authorViewpoint'):
            all_viewpoints.append({'site_name':report['site_name'],'viewpoint':report['authorViewpoint']})
all_entities.update(report.get('characterEntity',[]))
all_institutions.update(report.get('institutionalEntities',[]))
all_events.update(report.get('eventInfo',[]))
ifreport.get('summary'):
            report_summaries.append({'site_name':report['site_name'],'summary':report['summary']})
logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")
data_summary={"total_reports":len(reports_data),"unique_entities":list(all_entities)[:10],"unique_institutions":list(all_institutions)[:10],"unique_events":list(all_events)[:10],"viewpoint_count":len(all_viewpoints)}
viewpoints_text="\n\n".join([f"{item['site_name']}: {item['viewpoint']}"foriteminall_viewpoints])
logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")
logger.info("开始向LLM发送综合分析请求...")
system_msg=SystemMessage(content="""\"你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容包括：
1. 宏观经济预期（并标注各观点来源）
2. 宏观政策预期（并标注各观点来源）
3. 利率走势预测（并标注各观点来源）
4. 投资建议（并标注各观点来源）
5. 共识观点（哪些机构持有相同观点）
6. 分歧观点（哪些机构观点不同）
7. 主要风险与机会（并标注来源）
请用结构化JSON格式返回，确保每个观点都注明出处。\"""")
human_msg=HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度包括但不限于:
1. 机构对宏观经济的预期
2. 对宏观政策的预期
3. 对利率走势的预测
4. 对投资的建议

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])}
- 主要事件: {', '.join(data_summary['unique_events'][:5])}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。

以下是各家机构的观点:
{viewpoints_text}
""")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成综合分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=180,default_response=default_analysis,description="多机构报告综合分析")
logger.info("开始生成结构化综合分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. macro_economic_outlook: 宏观经济展望总结（标注各观点来源机构）
2. policy_expectations: 政策预期总结（标注各观点来源机构）
3. interest_rate_forecast: 利率走势预测总结（标注各观点来源机构）
4. investment_recommendations: 投资建议总结（标注各观点来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）
10. reasoning_chain: 你的主要推理链条（简洁列表）

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "macro_economic_outlook": "无法在规定时间内完成综合分析",
  "policy_expectations": "无法在规定时间内完成综合分析",
  "interest_rate_forecast": "无法在规定时间内完成综合分析",
  "investment_recommendations": "无法在规定时间内完成综合分析",
  "consensus_points": ["无法识别共识观点"],
  "divergent_points": ["无法识别分歧观点"],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": ["无法识别市场风险"],
  "market_opportunities": ["无法识别市场机会"],
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=120,default_response=default_result,description="生成结构化综合分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points',[]))} 条共识观点, {len(analysis.get('divergent_points',[]))} 条分歧观点")
returnanalysis
else:
            logger.error("无法找到综合分析的JSON内容")
return{"macro_economic_outlook":"无法解析综合观点","policy_expectations":"无法解析综合观点","interest_rate_forecast":"无法解析综合观点","investment_recommendations":"无法解析综合观点","consensus_points":[],"divergent_points":[],"overall_summary":"无法生成综合分析","market_risks":[],"market_opportunities":[],"reasoning_chain":[]}
exceptExceptionase:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
return{"macro_economic_outlook":"分析过程出错","policy_expectations":"分析过程出错","interest_rate_forecast":"分析过程出错","investment_recommendations":"分析过程出错","consensus_points":[],"divergent_points":[],"overall_summary":"分析过程出错","market_risks":[],"market_opportunities":[],"reasoning_chain":[]}
defgenerate_executive_summary(overall_analysis,reports_count):
    """生成执行摘要"""
logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")
defformat_complex_item(item):
        ifisinstance(item,dict):
            if'content'initem:
                returnitem['content']
if'point'initem:
                returnitem['point']
returnstr(item)
returnstr(item)
defformat_list_safely(items,max_items=3):
        ifnotitems:
            return'无数据'
ifisinstance(items,str):
            returnitems
ifisinstance(items,list):
            formatted_items=[format_complex_item(item)foriteminitems[:max_items]]
iflen(items)>max_items:
                return', '.join(formatted_items)+'等'
return', '.join(formatted_items)
returnstr(items)
system_msg=SystemMessage(content="""\"你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
重点突出最关键的市场趋势、共识观点、分歧点、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。\"""")
try:
        macro_economic=format_list_safely(overall_analysis.get('macro_economic_outlook','无数据'))
policy_expectations=format_list_safely(overall_analysis.get('policy_expectations','无数据'))
interest_rate=format_list_safely(overall_analysis.get('interest_rate_forecast','无数据'))
investment=format_list_safely(overall_analysis.get('investment_recommendations','无数据'))
consensus=format_list_safely(overall_analysis.get('consensus_points','无共识'))
divergent=format_list_safely(overall_analysis.get('divergent_points','无分歧'))
risks=format_list_safely(overall_analysis.get('market_risks','未识别风险'))
opportunities=format_list_safely(overall_analysis.get('market_opportunities','未识别机会'))
human_msg=HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下要点：

1. 宏观经济展望：{macro_economic}

2. 政策预期：{policy_expectations}

3. 利率预测：{interest_rate}

4. 投资建议：{investment}

5. 共识观点：{consensus}

6. 分歧点：{divergent}

7. 主要风险：{risks}

8. 市场机会：{opportunities}

请生成一份不超过300字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。""")
logger.info("向LLM发送执行摘要生成请求...")
messages=[system_msg,human_msg]
default_summary="无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
result=safe_llm_invoke(messages,timeout_seconds=90,default_response=default_summary,description="生成执行摘要")
logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
returnresult.content
exceptExceptionase:
        logger.error(f"生成执行摘要时出错: {str(e)}")
traceback.print_exc()
return"无法生成执行摘要。错误原因："+str(e)
defgenerate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
logger.info("第1步: 获取每个公众号最新报告...")
latest_reports=get_latest_report_by_account(days=1)
logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
errors=[]
total=len(latest_reports)
foridx,(biz,report)inenumerate(latest_reports.items(),1):
        logger.info(f"正在分析第 {idx}/{total} 个公众号: {report['site_name']}")
try:
            analysis=analyze_report_with_reasoning(report['title'],report['content'],report['site_name'])
latest_reports[biz]['analysis']=analysis
logger.info(f"完成第 {idx}/{total} 个公众号分析: {report['site_name']}")
exceptExceptionase:
            error_msg=f"{report['site_name']} 分析失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
logger.info("第3步: 开始综合分析所有报告...")
try:
        overall_analysis=analyze_all_reports_with_storm(latest_reports)
logger.info("综合分析完成")
exceptExceptionase:
        error_msg=f"综合分析失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
overall_analysis={}
logger.info("第4步: 开始生成执行摘要...")
try:
        executive_summary=generate_executive_summary(overall_analysis,len(latest_reports))
logger.info("执行摘要生成完成")
exceptExceptionase:
        error_msg=f"执行摘要生成失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
executive_summary="执行摘要生成失败。"
logger.info("第5步: 组装最终报告...")
report={"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":executive_summary,"individual_reports":latest_reports,"overall_analysis":overall_analysis,"metadata":{"reports_count":len(latest_reports),"generation_method":"STORM结构化推理","time_range":f"近3天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("==================== 报告生成过程完成 ====================")
returnreport
defformat_structured_list(items,content_key='content',source_key='sources'):
    lines=[]
foriteminitems:
        ifisinstance(item,dict):
            content=item.get(content_key)oritem.get('point')oritem.get('risk')oritem.get('opportunity')or''
sources=item.get(source_key)oritem.get('institutions')oritem.get('source')or[]
ifisinstance(sources,list):
                sources='、'.join(sources)
ifsources:
                lines.append(f"- {content}（来源：{sources}）")
else:
                lines.append(f"- {content}")
elifisinstance(item,str):
            lines.append(f"- {item}")
return'\n'.join(lines)
defadd_heading_numbering(md_text):
    lines=md_text.split('\n')
h1,h2,h3=0,0,0
new_lines=[]
forlineinlines:
        ifline.startswith('# '):
            h1+=1;h2=0;h3=0
new_lines.append(f"# {h1}. {line[2:]}")
elifline.startswith('## '):
            h2+=1;h3=0
new_lines.append(f"## {h1}.{h2} {line[3:]}")
elifline.startswith('### '):
            h3+=1
new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
else:
            new_lines.append(line)
return'\n'.join(new_lines)
defformat_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
logger.info("开始格式化STORM报告...")
start_time=datetime.now()
output=f"# {report['report_title']}\n\n"
output+=f"生成时间: {report['generation_time']}\n"
output+=f"分析报告数: {report['metadata']['reports_count']}\n"
output+=f"时间范围: {report['metadata']['time_range']}\n\n"
logger.info("添加执行摘要部分...")
output+="## 执行摘要\n\n"
output+=f"{report['executive_summary']}\n\n"
logger.info("添加市场综合分析部分...")
output+="## 市场综合分析\n\n"
overall=report['overall_analysis']
logger.info("添加市场观点摘要...")
output+="### 市场观点摘要\n\n"
output+=f"{overall.get('overall_summary','')}\n\n"
logger.info("开始生成核心市场指标表格...")
table_start_time=datetime.now()
output+="### 核心市场指标\n\n"
output+="| 指标类别 | 分析结果 |\n"
output+="|---------|----------|\n"
output+=f"| 宏观经济展望 | {format_structured_list(overall.get('macro_economic_outlook',[]))} |\n"
output+=f"| 政策预期 | {format_structured_list(overall.get('policy_expectations',[]))} |\n"
output+=f"| 利率走势预测 | {format_structured_list(overall.get('interest_rate_forecast',[]))} |\n"
output+=f"| 投资建议 | {format_structured_list(overall.get('investment_recommendations',[]))} |\n\n"
table_end_time=datetime.now()
table_elapsed_time=(table_end_time-table_start_time).total_seconds()
logger.info(f"核心市场指标表格生成完成，耗时: {table_elapsed_time:.2f} 秒")
logger.info("添加市场观点分析部分...")
output+="### 市场观点分析\n\n"
logger.info("处理观点共识信息...")
output+="#### 观点共识\n"
consensus_points=overall.get('consensus_points',[])
forpointinconsensus_points:
        ifisinstance(point,str):
            output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
            sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (来源: {sources})\n"
else:
            output+=f"- {point}\n"
output+="\n"
logger.info(f"已添加 {len(consensus_points)} 条共识观点")
logger.info("处理观点分歧信息...")
output+="#### 观点分歧\n"
divergent_points=overall.get('divergent_points',[])
forpointindivergent_points:
        ifisinstance(point,str):
            output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
            sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (来源: {sources})\n"
else:
            output+=f"- {point}\n"
output+="\n"
logger.info(f"已添加 {len(divergent_points)} 条分歧观点")
logger.info("添加风险与机会部分...")
output+="### 风险与机会\n\n"
logger.info("处理主要风险信息...")
output+="#### 主要风险\n"
market_risks=overall.get('market_risks',[])
forriskinmarket_risks:
        ifisinstance(risk,str):
            output+=f"- {risk}\n"
elifisinstance(risk,dict)and'content'inriskand'sources'inrisk:
            sources=', '.join(risk['sources'])ifisinstance(risk['sources'],list)elserisk['sources']
output+=f"- {risk['content']} (来源: {sources})\n"
else:
            output+=f"- {risk}\n"
output+="\n"
logger.info(f"已添加 {len(market_risks)} 条市场风险")
logger.info("处理主要机会信息...")
output+="#### 主要机会\n"
market_opportunities=overall.get('market_opportunities',[])
foropportunityinmarket_opportunities:
        ifisinstance(opportunity,str):
            output+=f"- {opportunity}\n"
elifisinstance(opportunity,dict)and'content'inopportunityand'sources'inopportunity:
            sources=', '.join(opportunity['sources'])ifisinstance(opportunity['sources'],list)elseopportunity['sources']
output+=f"- {opportunity['content']} (来源: {sources})\n"
else:
            output+=f"- {opportunity}\n"
output+="\n"
logger.info(f"已添加 {len(market_opportunities)} 条市场机会")
ifoverall.get('reasoning_chain'):
        logger.info("添加分析推理过程部分...")
output+="### 分析推理过程\n\n"
reasoning_chain=overall.get('reasoning_chain',[])
fori,stepinenumerate(reasoning_chain,1):
            output+=f"{i}. {step}\n"
output+="\n"
logger.info(f"已添加 {len(reasoning_chain)} 条推理步骤")
logger.info("开始添加各机构观点详细分析部分...")
detailed_start_time=datetime.now()
output+="## 各机构观点详细分析\n\n"
report_count=0
forbiz,report_datainreport['individual_reports'].items():
        if'analysis'notinreport_data:
            continue
analysis=report_data['analysis']
output+=f"### {report_data['site_name']}\n\n"
output+=f"**标题**: {report_data['title']}\n\n"
output+=f"**发布时间**: {report_data['publishtime']}\n\n"
output+=f"**摘要**: {analysis.get('summary','')}\n\n"
output+="**核心观点**:\n"
forpointinanalysis.get('core_viewpoints',[]):
            output+=f"- {point}\n"
output+="\n"
output+="**观点依据**:\n"
forevidenceinanalysis.get('supporting_evidence',[]):
            output+=f"- {evidence}\n"
output+="\n"
ifanalysis.get('market_impact'):
            output+=f"**市场影响**: {analysis.get('market_impact')}\n\n"
output+=f"**关键洞见**: {analysis.get('key_insights','')}\n\n"
output+="---\n\n"
report_count+=1
detailed_end_time=datetime.now()
detailed_elapsed_time=(detailed_end_time-detailed_start_time).total_seconds()
logger.info(f"已添加 {report_count} 个机构的详细分析，耗时: {detailed_elapsed_time:.2f} 秒")
output+="## 附录\n\n"
output+=f"- 分析方法: {report['metadata']['generation_method']}\n"
output+=f"- 数据时间范围: {report['metadata']['time_range']}\n"
output+=f"- 报告生成时间: {report['generation_time']}\n"
output+=f"- 分析机构列表: {', '.join([report_data.get('site_name','未知')forbiz,report_datainreport['individual_reports'].items()if'analysis'inreport_data])}\n\n"
output+="## 执行情况总结\n\n"
errors=report.get("errors",[])
iferrors:
        output+="本次报告生成过程中出现如下异常：\n"
forerrinerrors:
            output+=f"- {err}\n"
else:
        output+="本次报告生成过程无异常。\n"
end_time=datetime.now()
total_elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
output=add_heading_numbering(output)
returnoutput
classAgentState(TypedDict):
    """智能体状态"""
report_data:Optional[Dict]
report_text:Optional[str]
error:Optional[str]
es_index:Optional[str]
es_host:Optional[str]
es_port:Optional[int]
fetch_start_time:Optional[str]
fetch_end_time:Optional[str]
llm_model:Optional[str]
llm_api_base:Optional[str]
llm_api_key:Optional[str]
deffetch_and_analyze_storm(state:AgentState)->AgentState:
    """使用STORM方法获取数据并分析"""
try:
        logger.info("====== 智能体工作流: 开始生成金融舆情分析报告 (STORM方法) ======")
start_time=datetime.now()
es_index=state.get("es_index")
es_host=state.get("es_host")
es_port=state.get("es_port")
fetch_start_time=state.get("fetch_start_time")
fetch_end_time=state.get("fetch_end_time")
llm_model=state.get("llm_model")
llm_api_base=state.get("llm_api_base")
llm_api_key=state.get("llm_api_key")
report_data=generate_report_with_storm()
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告生成完成，耗时: {elapsed_time:.2f} 秒")
state["report_data"]=report_data
returnstate
exceptExceptionase:
        logger.error(f"生成报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"生成报告失败: {str(e)}"
returnstate
defformat_storm_report_node(state:AgentState)->AgentState:
    """格式化STORM报告"""
try:
        logger.info("====== 智能体工作流: 开始格式化报告 ======")
start_time=datetime.now()
if"report_data"instateandstate["report_data"]:
            report_text=format_storm_report(state["report_data"])
state["report_text"]=report_text
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告格式化完成，耗时: {elapsed_time:.2f} 秒")
returnstate
exceptExceptionase:
        logger.error(f"格式化报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"格式化报告失败: {str(e)}"
returnstate
workflow=StateGraph(AgentState)
workflow.add_node("fetch_and_analyze_storm",fetch_and_analyze_storm)
workflow.add_node("format_storm_report",format_storm_report_node)
workflow.add_edge("fetch_and_analyze_storm","format_storm_report")
workflow.add_edge("format_storm_report",END)
workflow.set_entry_point("fetch_and_analyze_storm")
workflow=workflow.compile()
defrun_agent(days=1):
    """运行金融舆情分析智能体"""
logger.info("====================== 开始运行金融舆情分析智能体 ======================")
start_time=datetime.now()
fetch_end_time=datetime.now()
fetch_start_time=fetch_end_time-timedelta(days=days)
initial_state={"report_data":None,"report_text":None,"error":None,"es_index":TARGET_ES_INDEX,"es_host":TARGET_ES_HOST,"es_port":TARGET_ES_PORT,"fetch_start_time":fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),"fetch_end_time":fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),"llm_model":LLM_MODEL,"llm_api_base":LLM_API_BASE,"llm_api_key":OPENAI_API_KEY,}
try:
        logger.info("启动LangGraph工作流...")
result=workflow.invoke(initial_state)
ifresult.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
return{"status":"error","message":result["error"]}
ifresult.get("report_text"):
            end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
return{"status":"success","report_text":result["report_text"],"report_data":result["report_data"]}
else:
            logger.error("未能生成报告文本")
return{"status":"error","message":"未能生成报告文本"}
exceptExceptionase:
        logger.error(f"运行智能体时出错: {str(e)}")
traceback.print_exc()
return{"status":"error","message":str(e)}
ANALYSIS_CONFIG={"max_days":3,"min_word_count":500,"max_retries":3,"timeout":{"single_analysis":120,"overall_analysis":180,}}
if__name__=="__main__":
    result=run_agent()
ifresult["status"]=="success":
        print("报告生成成功！")
report_filename=f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
try:
            withopen(report_filename,"w",encoding="utf-8")asf:
                f.write(result["report_text"])
print(f"报告已保存至: {report_filename}")
exceptExceptionase:
            print(f"保存报告失败: {str(e)}")
lines=result["report_text"].split("\n")
preview_lines=lines[:20]+["...","（报告内容省略）","..."]+lines[-5:]
print("\n".join(preview_lines))
else:
        print(f"报告生成失败: {result['message']}")


============================================================
文件: backend\app\agent\financialPublicOpinionReport_weixin_V2.py
============================================================

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""
importconcurrent.futures
importfunctools
importjson
importlogging
importos
importplatform
importsignal
importthreading
importtraceback
fromconcurrent.futuresimportThreadPoolExecutor
fromcontextlibimportcontextmanager
fromdatetimeimportdatetime,timedelta
fromtypingimportAny,Callable,Dict,List,Optional,TypedDict
importpandasaspd
fromelasticsearchimportElasticsearch
fromlangchain_core.messagesimportAIMessage,HumanMessage,SystemMessage
fromlangchain_core.promptsimportChatPromptTemplate
fromlangchain_openaiimportChatOpenAI
fromlanggraph.graphimportEND,StateGraph
classTimeoutException(Exception):
    pass
deftimeout(seconds):
    defdecorator(func):
        @functools.wraps(func)
defwrapper(*args,**kwargs):
            start_time=datetime.now()
func_name=func.__name__
logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
withThreadPoolExecutor(max_workers=1)asexecutor:
                future=executor.submit(func,*args,**kwargs)
try:
                    result=future.result(timeout=seconds)
elapsed=(datetime.now()-start_time).total_seconds()
logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
returnresult
exceptconcurrent.futures.TimeoutError:
                    future.cancel()
elapsed=(datetime.now()-start_time).total_seconds()
logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
raiseTimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
returnwrapper
returndecorator
IS_UNIX=platform.system()in('Linux','Darwin')andos.name=='posix'
ifIS_UNIX:
    @contextmanager
deftimeout_context(seconds):
        defhandle_timeout(signum,frame):
            raiseTimeoutException(f"操作超时（{seconds}秒）")
original_handler=signal.getsignal(signal.SIGALRM)
signal.signal(signal.SIGALRM,handle_timeout)
try:
            signal.alarm(seconds)
yield
finally:
            signal.alarm(0)
signal.signal(signal.SIGALRM,original_handler)
else:
    @contextmanager
deftimeout_context(seconds):
        defdo_nothing():
            pass
withThreadPoolExecutor(max_workers=1)asexecutor:
            future=executor.submit(do_nothing)
try:
                future.result(timeout=0.001)
yield
exceptconcurrent.futures.TimeoutError:
                logger.warning("初始化超时上下文出错")
yield
logging.basicConfig(level=logging.INFO,format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger=logging.getLogger(__name__)
TARGET_ES_HOST="**************"
TARGET_ES_PORT=9600
TARGET_ES_INDEX="pro_mcp_data_weixin"
LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
BIZ_LIST=["MzA4MDY1NTUyMg==","MzA5OTY5MzM4Ng==","Mzg2Mjg1NTg3NA==","Mzg4NTEwMzA5NQ==","Mzg5MjU4MDkyMw==","MzI4NTU0NDE4Mw==","MzI5MzQxOTI0MQ==","MzIwMDI3NjM2Mg==","MzIzMjc3NTYyNQ==","Mzk0NjUwMDIxOQ==","MzkxMDYyNzExMA==","MzkzNTYzMDYxMg==","MzU3MDMwODc2MA==","MzU4NzcwNDcxOA==","MzU4ODM4NzI5Nw==","MzU5MzkzMTY3Mg==","MzUxMDk5NDgwNQ==","MzUxMDkyMzA4Mw==","MzUzMzEyODIyMA==","MzUzNDcxMDgzNg==","MzA3MTIzNDcwMg==","MzA3NjU1NTQwMA==","MzA4MzY2ODYwMw==","MzAwNzMxODYyNg==","Mzg4NDU2MDM3Mw==","MzI1NzAwODc3Nw==","MzU3MDMwODc2MA==","MzU3NTYyNTIyMQ==","MzUzMzYwODI2MA=="]
llm=ChatOpenAI(temperature=0,model=LLM_MODEL,openai_api_base=LLM_API_BASE,api_key=OPENAI_API_KEY,request_timeout=120)
defsafe_llm_invoke(messages,timeout_seconds=120,default_response=None,description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
try:
        @timeout(timeout_seconds)
definvoke_with_timeout(msgs):
            returnllm.invoke(msgs)
result=invoke_with_timeout(messages)
logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
returnresult
exceptTimeoutExceptionase:
        logger.error(f"{description}超时: {str(e)}")
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
exceptExceptionase:
        logger.error(f"{description}失败: {str(e)}")
traceback.print_exc()
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
defget_es_scroll_data_batched(index,query_body,batch_size=1000,es_host=None,es_port=None):
    """滚动查询ES数据，并以批次方式返回"""
ifnotes_host:
        es_host=TARGET_ES_HOST
ifnotes_port:
        es_port=TARGET_ES_PORT
es=Elasticsearch([f"{es_host}:{es_port}"])
sid=None
try:
        result=es.search(index=index,scroll='10m',body=query_body,size=batch_size,request_timeout=3600)
sid=result['_scroll_id']
scroll_size=result['hits']['total']['value']
logger.info(f"索引 {index} 总数据量: {scroll_size}")
iflen(result['hits']['hits'])>0:
            yieldresult['hits']['hits']
scroll_count=len(result['hits']['hits'])
whilescroll_count>0:
            result=es.scroll(scroll_id=sid,scroll='10m',request_timeout=3600)
batch_data=result['hits']['hits']
scroll_count=len(batch_data)
ifscroll_count==0:
                break
yieldbatch_data
exceptExceptionase:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
traceback.print_exc()
finally:
        ifsid:
            try:
                es.clear_scroll(scroll_id=sid)
except:
                pass
ifes:
            try:
                es.close()
except:
                pass
logger.info(f"索引 {index} 查询完成")
defbuild_query_body(start_time,end_time,biz=None):
    """构建ES查询体"""
body={"query":{"bool":{"filter":[{"range":{"createTimeES":{"gte":start_time,"lte":end_time}}}]}},"sort":[{"createTimeES":"desc"}]}
ifbiz:
        ifisinstance(biz,str):
            body["query"]["bool"]["filter"].append({"term":{"site_id":{"value":biz}}})
elifisinstance(biz,list)andlen(biz)>0:
            body["query"]["bool"]["filter"].append({"terms":{"site_id":biz}})
returnbody
defget_recent_reports(hours=24):
    """获取近24小时内的报告数据"""
end_time=datetime.now()
start_time=end_time-timedelta(hours=hours)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")
query_body=build_query_body(start_time_str,end_time_str,BIZ_LIST)
all_data=[]
forbatchinget_es_scroll_data_batched(TARGET_ES_INDEX,query_body):
        all_data.extend([doc['_source']fordocinbatch])
logger.info(f"获取到 {len(all_data)} 条记录")
returnall_data
defis_event_in_time_range(event_time_str):
    """检查事件是否在配置的时间范围内"""
try:
        event_time=datetime.strptime(event_time_str,"%Y-%m-%d %H:%M:%S")
current_time=datetime.now()
event_age_days=(current_time-event_time).days
ifevent_age_days<ANALYSIS_CONFIG["event_filter"]["min_event_age_days"]:
            returnFalse
ifevent_age_days>ANALYSIS_CONFIG["event_filter"]["max_event_age_days"]:
            returnFalse
ifANALYSIS_CONFIG["event_filter"]["exclude_future_events"]andevent_time>current_time:
            returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"事件时间解析错误: {str(e)}")
returnFalse
deffilter_events_by_time(report):
    """过滤报告中的事件信息"""
ifnotreport.get('eventInfo'):
        returnreport
filtered_events=[]
foreventinreport['eventInfo']:
        event_time=None
ifisinstance(event,dict):
            event_time=event.get('time')orevent.get('eventTime')orevent.get('timestamp')
ifnotevent_time:
            filtered_events.append(event)
continue
ifis_event_in_time_range(event_time):
            filtered_events.append(event)
report['eventInfo']=filtered_events
returnreport
ANALYSIS_CONFIG={"max_days":3,"min_word_count":500,"max_retries":3,"timeout":{"single_analysis":120,"overall_analysis":180,},"event_filter":{"max_event_age_days":7,"min_event_age_days":0,"exclude_future_events":True},"publish_filter":{"max_publish_age_days":7,"min_publish_age_days":0,"exclude_future_publish":True}}
defis_publish_time_valid(publish_time_str):
    """检查报告发布时间是否在配置的有效范围内"""
try:
        try:
            publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S")
exceptValueError:
            try:
                publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d")
exceptValueError:
                try:
                    publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S.%f")
exceptValueError:
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
returnFalse
current_time=datetime.now()
publish_age_days=(current_time-publish_time).days
ifpublish_age_days<ANALYSIS_CONFIG["publish_filter"]["min_publish_age_days"]:
            logger.info(f"报告发布时间太新: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifpublish_age_days>ANALYSIS_CONFIG["publish_filter"]["max_publish_age_days"]:
            logger.info(f"报告发布时间太旧: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifANALYSIS_CONFIG["publish_filter"]["exclude_future_publish"]andpublish_time>current_time:
            logger.info(f"报告发布时间在未来: {publish_time_str}")
returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"发布时间验证错误: {str(e)}")
returnFalse
defget_latest_report_by_account(days=3):
    """获取每个公众号最近3天的最新一篇报告"""
end_time=datetime.now()
start_time=end_time-timedelta(days=days)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")
latest_reports={}
filtered_count=0
total_count=0
forbizinBIZ_LIST:
        query_body=build_query_body(start_time_str,end_time_str,biz)
query_body["size"]=5
try:
            es=Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
result=es.search(index=TARGET_ES_INDEX,body=query_body)
valid_report_found=False
forhitinresult['hits']['hits']:
                total_count+=1
doc=hit['_source']
publish_time=doc.get('publishtime','')
ifnotpublish_timeornotis_publish_time_valid(publish_time):
                    filtered_count+=1
logger.info(f"过滤掉公众号 {doc.get('source',biz)} 的文章: {doc.get('title','无标题')}, 发布时间: {publish_time}")
continue
report={'title':doc.get('title','无标题'),'content':doc.get('content','无内容'),'author':doc.get('new_author','未知作者'),'site_name':doc.get('source','未知公众号'),'publishtime':publish_time,'authorViewpoint':doc.get('authorViewpoint',''),'characterEntity':doc.get('characterEntity',[]),'institutionalEntities':doc.get('institutionalEntities',[]),'locationEntity':doc.get('locationEntity',[]),'eventInfo':doc.get('eventInfo',[]),'summaryFacts':doc.get('summaryFacts',[]),'summary':doc.get('summary','')}
report=filter_events_by_time(report)
latest_reports[biz]=report
logger.info(f"获取到公众号 {doc.get('source',biz)} 有效文章, 发布时间: {publish_time}, 标题: {doc.get('title','无标题')}")
valid_report_found=True
break
ifnotvalid_report_found:
                logger.warning(f"未找到公众号 {biz} 的有效报告")
exceptExceptionase:
            logger.error(f"获取公众号 {biz} 最新报告时出错: {str(e)}")
finally:
            ifes:
                es.close()
logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告，过滤掉 {filtered_count}/{total_count} 篇不符合发布时间条件的报告")
returnlatest_reports
defanalyze_report_with_reasoning(title,content,account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
system_msg=SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")
human_msg=HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000]ifcontentandlen(content)>6000elsecontent}
""")
logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000]ifcontentandlen(content)>6000elsecontent)}")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=120,default_response=default_analysis,description=f"'{account_name}'报告初始分析")
logger.info("开始生成结构化分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=60,default_response=default_result,description=f"'{account_name}'报告结构化分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints',[]))} 条核心观点")
returnanalysis
else:
            logger.error("无法找到JSON内容")
return{"summary":"无法解析报告内容","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"无法分析","reasoning_chain":[]}
exceptExceptionase:
        logger.error(f"分析报告内容时出错: {str(e)}")
return{"summary":"分析过程出错","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"分析出错","reasoning_chain":[]}
defanalyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
all_viewpoints=[]
all_entities=set()
all_institutions=set()
all_events=set()
report_summaries=[]
forbiz,reportinreports_data.items():
        if'analysis'inreportandreport['analysis'].get('core_viewpoints'):
            forviewpointinreport['analysis']['core_viewpoints']:
                all_viewpoints.append({'site_name':report['site_name'],'viewpoint':viewpoint})
ifreport.get('authorViewpoint'):
            all_viewpoints.append({'site_name':report['site_name'],'viewpoint':report['authorViewpoint']})
all_entities.update(report.get('characterEntity',[]))
all_institutions.update(report.get('institutionalEntities',[]))
all_events.update(report.get('eventInfo',[]))
ifreport.get('summary'):
            report_summaries.append({'site_name':report['site_name'],'summary':report['summary']})
logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")
data_summary={"total_reports":len(reports_data),"unique_entities":list(all_entities)[:10],"unique_institutions":list(all_institutions)[:10],"unique_events":list(all_events)[:10],"viewpoint_count":len(all_viewpoints)}
viewpoints_text="\n\n".join([f"{item['site_name']}: {item['viewpoint']}"foriteminall_viewpoints])
logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")
logger.info("开始向LLM发送综合分析请求...")
system_msg=SystemMessage(content="""你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容需要按照以下维度进行详细分析：

1. 宏观经济预期分析
   - 各机构对宏观经济的预判（标注观点来源机构）
   - 主要依据和论据（标注来源）
   - 共识观点和分歧观点

2. 宏观政策预期分析
   - 各机构对政策走向的判断（标注观点来源机构）
   - 政策预期的主要依据（标注来源）
   - 共识观点和分歧观点

3. 利率走势预测分析
   - 各机构对利率走势的预测（标注观点来源机构）
   - 预测的主要依据（标注来源）
   - 共识观点和分歧观点

4. 投资策略建议分析
   - 各机构的投资策略建议（标注观点来源机构）
   - 投资机会分析（标注来源）
   - 共识建议和分歧建议

5. 市场观点分析
   - 市场主流观点
   - 观点共识（哪些机构持有相同观点）
   - 观点分歧（哪些机构观点不同）

6. 风险与机会分析
   - 主要风险点（标注提出风险的机构）
   - 主要机会点（标注提出机会的机构）

请用结构化JSON格式返回，确保每个观点都注明出处机构。""")
human_msg=HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度必须包括：
1. 宏观经济预期分析
2. 宏观政策预期分析
3. 利率走势预测分析
4. 投资策略建议分析
5. 市场观点分析（共识与分歧）
6. 风险与机会分析

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])}
- 主要事件: {', '.join(data_summary['unique_events'][:5])}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。每个维度的分析都要尽可能详细，并确保标注来源机构。

以下是各家机构的观点:
{viewpoints_text}
""")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成综合分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=180,default_response=default_analysis,description="多机构报告综合分析")
logger.info("开始生成结构化综合分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构，并按照要求的维度进行详细分析。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，必须包含以下字段:

1. macro_economic_outlook: 宏观经济预期分析（数组格式，每项包含观点内容和来源机构）
2. policy_expectations: 宏观政策预期分析（数组格式，每项包含观点内容和来源机构）
3. interest_rate_forecast: 利率走势预测分析（数组格式，每项包含观点内容和来源机构）
4. investment_recommendations: 投资策略建议分析（数组格式，每项包含观点内容和来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）

对于每个数组项，请使用以下格式：
{
  "content": "观点内容",
  "sources": ["机构A", "机构B"]
}

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "macro_economic_outlook": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "policy_expectations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "interest_rate_forecast": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "investment_recommendations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "consensus_points": [{"content": "无法识别共识观点", "sources": []}],
  "divergent_points": [{"content": "无法识别分歧观点", "sources": []}],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": [{"content": "无法识别市场风险", "sources": []}],
  "market_opportunities": [{"content": "无法识别市场机会", "sources": []}]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=120,default_response=default_result,description="生成结构化综合分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points',[]))} 条共识观点, {len(analysis.get('divergent_points',[]))} 条分歧观点")
returnanalysis
else:
            logger.error("无法找到综合分析的JSON内容")
return{"macro_economic_outlook":[{"content":"无法解析综合观点","sources":[]}],"policy_expectations":[{"content":"无法解析综合观点","sources":[]}],"interest_rate_forecast":[{"content":"无法解析综合观点","sources":[]}],"investment_recommendations":[{"content":"无法解析综合观点","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"无法生成综合分析","market_risks":[],"market_opportunities":[]}
exceptExceptionase:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
return{"macro_economic_outlook":[{"content":"分析过程出错","sources":[]}],"policy_expectations":[{"content":"分析过程出错","sources":[]}],"interest_rate_forecast":[{"content":"分析过程出错","sources":[]}],"investment_recommendations":[{"content":"分析过程出错","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"分析过程出错","market_risks":[],"market_opportunities":[]}
defgenerate_executive_summary(overall_analysis,reports_count):
    """生成执行摘要"""
logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")
defformat_complex_item(items,max_items=3):
        ifnotitems:
            return'无数据'
ifisinstance(items,str):
            returnitems
formatted_points=[]
count=0
foriteminitems[:max_items]:
            ifisinstance(item,dict):
                content=item.get('content','')
sources=item.get('sources',[])
ifisinstance(sources,list)andsources:
                    sources_str='、'.join(sources)
formatted_points.append(f"{content}（来源：{sources_str}）")
else:
                    formatted_points.append(content)
elifisinstance(item,str):
                formatted_points.append(item)
count+=1
ifcount>=max_items:
                break
iflen(items)>max_items:
            formatted_points.append("等")
return'；'.join(formatted_points)
system_msg=SystemMessage(content="""你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
摘要应包含以下几个关键维度的分析：宏观经济预期、宏观政策预期、利率走势预测、投资策略建议、市场观点共识与分歧、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。""")
try:
        macro_economic=format_complex_item(overall_analysis.get('macro_economic_outlook',[]))
policy_expectations=format_complex_item(overall_analysis.get('policy_expectations',[]))
interest_rate=format_complex_item(overall_analysis.get('interest_rate_forecast',[]))
investment=format_complex_item(overall_analysis.get('investment_recommendations',[]))
consensus=format_complex_item(overall_analysis.get('consensus_points',[]))
divergent=format_complex_item(overall_analysis.get('divergent_points',[]))
risks=format_complex_item(overall_analysis.get('market_risks',[]))
opportunities=format_complex_item(overall_analysis.get('market_opportunities',[]))
overall_summary=overall_analysis.get('overall_summary','无法获取整体市场观点摘要')
human_msg=HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下维度：

1. 宏观经济预期分析：{macro_economic}

2. 宏观政策预期分析：{policy_expectations}

3. 利率走势预测分析：{interest_rate}

4. 投资策略建议分析：{investment}

5. 市场观点分析：
   - 共识观点：{consensus}
   - 分歧观点：{divergent}

6. 风险与机会：
   - 主要风险：{risks}
   - 主要机会：{opportunities}

7. 整体市场观点：{overall_summary}

请生成一份不超过400字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。
摘要需要结构清晰，重点突出，便于高管快速把握市场关键信息。""")
logger.info("向LLM发送执行摘要生成请求...")
messages=[system_msg,human_msg]
default_summary="无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
result=safe_llm_invoke(messages,timeout_seconds=90,default_response=default_summary,description="生成执行摘要")
logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
returnresult.content
exceptExceptionase:
        logger.error(f"生成执行摘要时出错: {str(e)}")
traceback.print_exc()
return"无法生成执行摘要。错误原因："+str(e)
defgenerate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
days_to_fetch=ANALYSIS_CONFIG.get("max_days",3)
logger.info(f"第1步: 获取每个公众号最新报告（{days_to_fetch}天内）...")
latest_reports=get_latest_report_by_account(days=days_to_fetch)
logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
errors=[]
total=len(latest_reports)
logger.info(f"第2步: 开始并行分析 {total} 个公众号报告...")
defanalyze_single_report(biz_report_tuple):
        biz,report=biz_report_tuple
try:
            content=report['content']
ifcontentandlen(content)<ANALYSIS_CONFIG.get("min_word_count",500):
                returnbiz,None,f"{report['site_name']} 内容过短，不进行分析"
logger.info(f"开始分析公众号: {report['site_name']}")
analysis=analyze_report_with_reasoning(report['title'],content,report['site_name'])
logger.info(f"完成公众号分析: {report['site_name']}")
returnbiz,analysis,None
exceptExceptionase:
            error_msg=f"{report['site_name']} 分析失败: {str(e)}"
logger.error(error_msg)
returnbiz,None,error_msg
max_workers=min(10,total)
withThreadPoolExecutor(max_workers=max_workers)asexecutor:
        results=list(executor.map(analyze_single_report,latest_reports.items()))
valid_reports_count=0
forbiz,analysis,errorinresults:
        iferror:
            errors.append(error)
elifanalysis:
            latest_reports[biz]['analysis']=analysis
valid_reports_count+=1
logger.info(f"成功分析 {valid_reports_count}/{total} 个报告")
ifvalid_reports_count==0:
        error_msg="未能成功分析任何报告，无法生成综合分析"
logger.error(error_msg)
errors.append(error_msg)
return{"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":"无法生成报告，未能成功分析任何内容。","individual_reports":{},"overall_analysis":{},"metadata":{"reports_count":0,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("第3步: 开始综合分析所有报告...")
try:
        overall_analysis=analyze_all_reports_with_storm(latest_reports)
logger.info("综合分析完成")
exceptExceptionase:
        error_msg=f"综合分析失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
overall_analysis={}
logger.info("第4步: 开始生成执行摘要...")
try:
        executive_summary=generate_executive_summary(overall_analysis,valid_reports_count)
logger.info("执行摘要生成完成")
exceptExceptionase:
        error_msg=f"执行摘要生成失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
executive_summary="执行摘要生成失败。"
logger.info("第5步: 组装最终报告...")
report={"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":executive_summary,"individual_reports":latest_reports,"overall_analysis":overall_analysis,"metadata":{"reports_count":valid_reports_count,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("==================== 报告生成过程完成 ====================")
returnreport
defformat_structured_list(items,content_key='content',source_key='sources'):
    lines=[]
foriteminitems:
        ifisinstance(item,dict):
            content=item.get(content_key)oritem.get('point')oritem.get('risk')oritem.get('opportunity')or''
sources=item.get(source_key)oritem.get('institutions')oritem.get('source')or[]
ifisinstance(sources,list):
                sources='、'.join(sources)
ifsources:
                lines.append(f"- {content}（来源：{sources}）")
else:
                lines.append(f"- {content}")
elifisinstance(item,str):
            lines.append(f"- {item}")
return'\n'.join(lines)
defadd_heading_numbering(md_text):
    lines=md_text.split('\n')
h1,h2,h3=0,0,0
new_lines=[]
forlineinlines:
        ifline.startswith('# '):
            h1+=1;h2=0;h3=0
new_lines.append(f"# {h1}. {line[2:]}")
elifline.startswith('## '):
            h2+=1;h3=0
new_lines.append(f"## {h1}.{h2} {line[3:]}")
elifline.startswith('### '):
            h3+=1
new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
else:
            new_lines.append(line)
return'\n'.join(new_lines)
defformat_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
logger.info("开始格式化STORM报告...")
start_time=datetime.now()
output=f"# {report['report_title']}\n\n"
output+=f"生成时间: {report['generation_time']}\n"
output+=f"分析报告数: {report['metadata']['reports_count']}\n"
output+=f"时间范围: {report['metadata']['time_range']}\n\n"
overall=report['overall_analysis']
logger.info("生成第一章：执行摘要...")
output+="## 执行摘要\n\n"
output+=f"{report['executive_summary']}\n\n"
logger.info("生成第二章：市场综合分析...")
output+="## 市场综合分析\n\n"
logger.info("生成2.1节：宏观经济预期分析...")
output+="### 宏观经济预期分析\n\n"
macro_economic=overall.get('macro_economic_outlook','')
ifisinstance(macro_economic,str):
        output+=macro_economic+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(macro_economic)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观经济观点共识与分歧。\n"
output+="\n"
logger.info("生成2.2节：宏观政策预期分析...")
output+="### 宏观政策预期分析\n\n"
policy_expectations=overall.get('policy_expectations','')
ifisinstance(policy_expectations,str):
        output+=policy_expectations+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(policy_expectations)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观政策观点共识与分歧。\n"
output+="\n"
logger.info("生成2.3节：利率走势预测分析...")
output+="### 利率走势预测分析\n\n"
interest_rate=overall.get('interest_rate_forecast','')
ifisinstance(interest_rate,str):
        output+=interest_rate+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(interest_rate)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的利率走势观点共识与分歧。\n"
output+="\n"
logger.info("生成2.4节：投资策略建议分析...")
output+="### 投资策略建议分析\n\n"
investment=overall.get('investment_recommendations','')
ifisinstance(investment,str):
        output+=investment+"\n\n"
else:
        output+="#### 主要投资建议汇总\n\n"
output+=format_structured_list(investment)+"\n\n"
output+="#### 建议共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的投资建议共识与分歧。\n"
output+="\n"
logger.info("生成2.5节：市场观点分析...")
output+="### 市场观点分析\n\n"
output+="#### 市场主流观点\n\n"
output+=f"{overall.get('overall_summary','未能提取市场主流观点。')}\n\n"
output+="#### 观点共识\n\n"
consensus_points=overall.get('consensus_points',[])
ifconsensus_points:
        forpointinconsensus_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (支持机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点共识。\n"
output+="\n"
output+="#### 观点分歧\n\n"
divergent_points=overall.get('divergent_points',[])
ifdivergent_points:
        forpointindivergent_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (相关机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点分歧。\n"
output+="\n"
logger.info("生成2.6节：风险与机会...")
output+="### 风险与机会\n\n"
output+="#### 市场主要风险\n\n"
market_risks=overall.get('market_risks',[])
ifmarket_risks:
        forriskinmarket_risks:
            ifisinstance(risk,str):
                output+=f"- {risk}\n"
elifisinstance(risk,dict):
                content=risk.get('content')orrisk.get('risk')orstr(risk)
sources=risk.get('sources')orrisk.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场风险点。\n"
output+="\n"
output+="#### 市场主要机会\n\n"
market_opportunities=overall.get('market_opportunities',[])
ifmarket_opportunities:
        foropportunityinmarket_opportunities:
            ifisinstance(opportunity,str):
                output+=f"- {opportunity}\n"
elifisinstance(opportunity,dict):
                content=opportunity.get('content')oropportunity.get('opportunity')orstr(opportunity)
sources=opportunity.get('sources')oropportunity.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场机会点。\n"
output+="\n"
logger.info("生成第三章：各机构观点明细...")
output+="## 各机构观点明细\n\n"
report_count=0
forbiz,report_datainreport['individual_reports'].items():
        if'analysis'notinreport_data:
            continue
analysis=report_data['analysis']
output+=f"### {report_data['site_name']}\n\n"
output+="#### 报告信息\n\n"
output+=f"**标题**: {report_data['title']}\n\n"
output+=f"**发布时间**: {report_data['publishtime']}\n\n"
output+="#### 核心观点摘要\n\n"
output+=f"{analysis.get('summary','未提供摘要')}\n\n"
output+="#### 具体观点\n\n"
core_viewpoints=analysis.get('core_viewpoints',[])
ifcore_viewpoints:
            forpointincore_viewpoints:
                output+=f"- {point}\n"
else:
            output+="未提取到核心观点。\n"
output+="\n"
output+="#### 观点依据\n\n"
supporting_evidence=analysis.get('supporting_evidence',[])
ifsupporting_evidence:
            forevidenceinsupporting_evidence:
                output+=f"- {evidence}\n"
else:
            output+="未提取到观点依据。\n"
output+="\n"
ifanalysis.get('market_impact')oranalysis.get('key_insights'):
            output+="#### 市场影响与洞见\n\n"
ifanalysis.get('market_impact'):
                output+=f"**市场影响**: {analysis.get('market_impact')}\n\n"
ifanalysis.get('key_insights'):
                output+=f"**关键洞见**: {analysis.get('key_insights')}\n\n"
output+="---\n\n"
report_count+=1
ifreport_count==0:
        output+="未能获取到任何机构的有效分析结果。\n\n"
logger.info("生成第四章：附录...")
output+="## 附录\n\n"
output+="### 分析方法说明\n\n"
output+=f"本报告采用 {report['metadata']['generation_method']} 方法，通过大语言模型对各金融机构公众号发布的研究报告进行自动化分析和总结。\n"
output+="分析过程包括：\n"
output+="1. 获取各公众号最新研究报告\n"
output+="2. 对每篇报告进行结构化分析，提取核心观点和依据\n"
output+="3. 综合分析所有报告，识别共识观点和分歧点\n"
output+="4. 按照宏观经济、政策预期、利率走势和投资建议等维度进行归纳\n\n"
output+="### 数据来源说明\n\n"
output+=f"- 数据时间范围: {report['metadata']['time_range']}\n"
output+=f"- 报告生成时间: {report['generation_time']}\n"
output+=f"- 分析机构列表: {', '.join([report_data.get('site_name','未知')forbiz,report_datainreport['individual_reports'].items()if'analysis'inreport_data])}\n\n"
output+="### 执行情况\n\n"
errors=report.get("errors",[])
iferrors:
        output+="本次报告生成过程中出现如下异常：\n"
forerrinerrors:
            output+=f"- {err}\n"
else:
        output+="本次报告生成过程无异常。\n"
end_time=datetime.now()
total_elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
output=add_heading_numbering(output)
returnoutput
classAgentState(TypedDict):
    """智能体状态"""
report_data:Optional[Dict]
report_text:Optional[str]
error:Optional[str]
es_index:Optional[str]
es_host:Optional[str]
es_port:Optional[int]
fetch_start_time:Optional[str]
fetch_end_time:Optional[str]
llm_model:Optional[str]
llm_api_base:Optional[str]
llm_api_key:Optional[str]
deffetch_and_analyze_storm(state:AgentState)->AgentState:
    """使用STORM方法获取数据并分析"""
try:
        logger.info("====== 智能体工作流: 开始生成金融舆情分析报告 (STORM方法) ======")
start_time=datetime.now()
es_index=state.get("es_index")
es_host=state.get("es_host")
es_port=state.get("es_port")
fetch_start_time=state.get("fetch_start_time")
fetch_end_time=state.get("fetch_end_time")
llm_model=state.get("llm_model")
llm_api_base=state.get("llm_api_base")
llm_api_key=state.get("llm_api_key")
report_data=generate_report_with_storm()
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告生成完成，耗时: {elapsed_time:.2f} 秒")
state["report_data"]=report_data
returnstate
exceptExceptionase:
        logger.error(f"生成报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"生成报告失败: {str(e)}"
returnstate
defformat_storm_report_node(state:AgentState)->AgentState:
    """格式化STORM报告"""
try:
        logger.info("====== 智能体工作流: 开始格式化报告 ======")
start_time=datetime.now()
if"report_data"instateandstate["report_data"]:
            report_text=format_storm_report(state["report_data"])
state["report_text"]=report_text
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告格式化完成，耗时: {elapsed_time:.2f} 秒")
returnstate
exceptExceptionase:
        logger.error(f"格式化报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"格式化报告失败: {str(e)}"
returnstate
workflow=StateGraph(AgentState)
workflow.add_node("fetch_and_analyze_storm",fetch_and_analyze_storm)
workflow.add_node("format_storm_report",format_storm_report_node)
workflow.add_edge("fetch_and_analyze_storm","format_storm_report")
workflow.add_edge("format_storm_report",END)
workflow.set_entry_point("fetch_and_analyze_storm")
workflow=workflow.compile()
defrun_agent(days=None):
    """运行金融舆情分析智能体"""
logger.info("====================== 开始运行金融舆情分析智能体 ======================")
start_time=datetime.now()
ifdaysisNone:
        days=ANALYSIS_CONFIG.get("max_days",3)
fetch_end_time=datetime.now()
fetch_start_time=fetch_end_time-timedelta(days=days)
initial_state={"report_data":None,"report_text":None,"error":None,"es_index":TARGET_ES_INDEX,"es_host":TARGET_ES_HOST,"es_port":TARGET_ES_PORT,"fetch_start_time":fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),"fetch_end_time":fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),"llm_model":LLM_MODEL,"llm_api_base":LLM_API_BASE,"llm_api_key":OPENAI_API_KEY,}
try:
        logger.info("启动LangGraph工作流...")
result=workflow.invoke(initial_state)
ifresult.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
return{"status":"error","message":result["error"]}
ifresult.get("report_text"):
            end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
return{"status":"success","report_text":result["report_text"],"report_data":result["report_data"]}
else:
            logger.error("未能生成报告文本")
return{"status":"error","message":"未能生成报告文本"}
exceptExceptionase:
        logger.error(f"运行智能体时出错: {str(e)}")
traceback.print_exc()
return{"status":"error","message":str(e)}
if__name__=="__main__":
    result=run_agent()
ifresult["status"]=="success":
        print("报告生成成功！")
report_filename=f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
try:
            withopen(report_filename,"w",encoding="utf-8")asf:
                f.write(result["report_text"])
print(f"报告已保存至: {report_filename}")
exceptExceptionase:
            print(f"保存报告失败: {str(e)}")
lines=result["report_text"].split("\n")
preview_lines=lines[:20]+["...","（报告内容省略）","..."]+lines[-5:]
print("\n".join(preview_lines))
else:
        print(f"报告生成失败: {result['message']}")


============================================================
文件: backend\app\agent\financialPublicOpinionReport_weixin_V3.py
============================================================

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""
importconcurrent.futures
importfunctools
importjson
importlogging
importos
importplatform
importsignal
importthreading
importtraceback
importio
importuuid
fromconcurrent.futuresimportThreadPoolExecutor
fromcontextlibimportcontextmanager
fromdatetimeimportdatetime,timedelta
fromtypingimportAny,Callable,Dict,List,Optional,TypedDict,Tuple
importpandasaspd
fromelasticsearchimportElasticsearch
fromlangchain_core.messagesimportAIMessage,HumanMessage,SystemMessage
fromlangchain_core.promptsimportChatPromptTemplate
fromlangchain_openaiimportChatOpenAI
fromlanggraph.graphimportEND,StateGraph
importmongoengine
frommongoengineimportDocument,StringField,DateTimeField,ListField
fromminioimportMinio
fromdotenvimportload_dotenv
load_dotenv()
logging.basicConfig(level=logging.INFO,format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger=logging.getLogger(__name__)
TARGET_ES_HOST=os.getenv("ES_HOST","**************")
TARGET_ES_PORT=int(os.getenv("ES_PORT","9600"))
TARGET_ES_INDEX=os.getenv("ES_INDEX","pro_mcp_data_weixin")
LLM_MODEL=os.getenv("LLM_MODEL","Qwen/Qwen3-32B")
LLM_API_BASE=os.getenv("LLM_API_BASE","https://api.siliconflow.cn/v1")
OPENAI_API_KEY=os.getenv("OPENAI_API_KEY","sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn")
MONGO_URI=os.getenv("MONGO_URI","mongodb://localhost:27017/wiseAgent")
MINIO_ENDPOINT=os.getenv("MINIO_ENDPOINT","localhost:9000")
MINIO_ACCESS_KEY=os.getenv("MINIO_ACCESS_KEY","minioadmin")
MINIO_SECRET_KEY=os.getenv("MINIO_SECRET_KEY","minioadmin")
MINIO_BUCKET=os.getenv("MINIO_BUCKET","wiseagent")
MINIO_SECURE=os.getenv("MINIO_SECURE","false").lower()=="true"
try:
    minio_client=Minio(endpoint=MINIO_ENDPOINT,access_key=MINIO_ACCESS_KEY,secret_key=MINIO_SECRET_KEY,secure=MINIO_SECURE)
ifnotminio_client.bucket_exists(MINIO_BUCKET):
        minio_client.make_bucket(MINIO_BUCKET)
logger.info(f"MinIO存储桶 '{MINIO_BUCKET}' 创建成功")
else:
        logger.info(f"MinIO存储桶 '{MINIO_BUCKET}' 已存在")
exceptExceptionase:
    logger.error(f"MinIO初始化失败: {str(e)}")
traceback.print_exc()
try:
    mongoengine.connect(host=MONGO_URI)
logger.info(f"MongoDB连接成功: {MONGO_URI.split('@')[-1]if'@'inMONGO_URIelseMONGO_URI}")
exceptExceptionase:
    logger.error(f"MongoDB连接失败: {str(e)}")
traceback.print_exc()
classMediaInsightsReport(Document):
    title=StringField(required=True)
content=StringField(required=True)
storage_path=StringField(required=False)
created_at=DateTimeField(default=datetime.now)
report_type=StringField(required=True)
start_time=DateTimeField(required=True)
end_time=DateTimeField(required=True)
media_ids=ListField(StringField())
BIZ_LIST=["MzA4MDY1NTUyMg==","MzA5OTY5MzM4Ng==","Mzg2Mjg1NTg3NA==","Mzg4NTEwMzA5NQ==","Mzg5MjU4MDkyMw==","MzI4NTU0NDE4Mw==","MzI5MzQxOTI0MQ==","MzIwMDI3NjM2Mg==","MzIzMjc3NTYyNQ==","Mzk0NjUwMDIxOQ==","MzkxMDYyNzExMA==","MzkzNTYzMDYxMg==","MzU3MDMwODc2MA==","MzU4NzcwNDcxOA==","MzU4ODM4NzI5Nw==","MzU5MzkzMTY3Mg==","MzUxMDk5NDgwNQ==","MzUxMDkyMzA4Mw==","MzUzMzEyODIyMA==","MzUzNDcxMDgzNg==","MzA3MTIzNDcwMg==","MzA3NjU1NTQwMA==","MzA4MzY2ODYwMw==","MzAwNzMxODYyNg==","Mzg4NDU2MDM3Mw==","MzI1NzAwODc3Nw==","MzU3MDMwODc2MA==","MzU3NTYyNTIyMQ==","MzUzMzYwODI2MA=="]
classTimeoutException(Exception):
    pass
deftimeout(seconds):
    defdecorator(func):
        @functools.wraps(func)
defwrapper(*args,**kwargs):
            start_time=datetime.now()
func_name=func.__name__
logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
withThreadPoolExecutor(max_workers=1)asexecutor:
                future=executor.submit(func,*args,**kwargs)
try:
                    result=future.result(timeout=seconds)
elapsed=(datetime.now()-start_time).total_seconds()
logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
returnresult
exceptconcurrent.futures.TimeoutError:
                    future.cancel()
elapsed=(datetime.now()-start_time).total_seconds()
logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
raiseTimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
returnwrapper
returndecorator
IS_UNIX=platform.system()in('Linux','Darwin')andos.name=='posix'
ifIS_UNIX:
    @contextmanager
deftimeout_context(seconds):
        defhandle_timeout(signum,frame):
            raiseTimeoutException(f"操作超时（{seconds}秒）")
original_handler=signal.getsignal(signal.SIGALRM)
signal.signal(signal.SIGALRM,handle_timeout)
try:
            signal.alarm(seconds)
yield
finally:
            signal.alarm(0)
signal.signal(signal.SIGALRM,original_handler)
else:
    @contextmanager
deftimeout_context(seconds):
        defdo_nothing():
            pass
withThreadPoolExecutor(max_workers=1)asexecutor:
            future=executor.submit(do_nothing)
try:
                future.result(timeout=0.001)
yield
exceptconcurrent.futures.TimeoutError:
                logger.warning("初始化超时上下文出错")
yield
ANALYSIS_CONFIG={"max_days":3,"min_word_count":500,"max_retries":3,"timeout":{"single_analysis":120,"overall_analysis":180,},"event_filter":{"max_event_age_days":7,"min_event_age_days":0,"exclude_future_events":True},"publish_filter":{"max_publish_age_days":7,"min_publish_age_days":0,"exclude_future_publish":True}}
try:
    llm=ChatOpenAI(temperature=0,model=LLM_MODEL,openai_api_base=LLM_API_BASE,api_key=OPENAI_API_KEY,request_timeout=120)
logger.info(f"大模型 {LLM_MODEL} 初始化成功")
exceptExceptionase:
    logger.error(f"大模型初始化失败: {str(e)}")
traceback.print_exc()
logger.info("==================== 配置信息 ====================")
logger.info(f"运行环境: {platform.platform()}")
logger.info(f"Python版本: {platform.python_version()}")
logger.info("\n=== ElasticSearch配置 ===")
logger.info(f"ES主机: {TARGET_ES_HOST}")
logger.info(f"ES端口: {TARGET_ES_PORT}")
logger.info(f"ES索引: {TARGET_ES_INDEX}")
logger.info("\n=== LLM配置 ===")
logger.info(f"模型名称: {LLM_MODEL}")
logger.info(f"API基础URL: {LLM_API_BASE}")
logger.info("API密钥: ******"+OPENAI_API_KEY[-8:]iflen(OPENAI_API_KEY)>8else"*****")
logger.info("\n=== MinIO配置 ===")
logger.info(f"MinIO端点: {MINIO_ENDPOINT}")
logger.info(f"MinIO访问密钥: ******{MINIO_ACCESS_KEY[-4:]iflen(MINIO_ACCESS_KEY)>4else'****'}")
logger.info(f"MinIO存储桶: {MINIO_BUCKET}")
logger.info(f"MinIO安全连接: {MINIO_SECURE}")
logger.info("\n=== MongoDB配置 ===")
mongo_display=MONGO_URI.split('@')[-1]if'@'inMONGO_URIelseMONGO_URI.split('//')[1]
logger.info(f"MongoDB URI: {mongo_display}")
logger.info("\n=== 分析配置 ===")
logger.info(f"最大获取天数: {ANALYSIS_CONFIG['max_days']}")
logger.info(f"最小文章字数: {ANALYSIS_CONFIG['min_word_count']}")
logger.info(f"分析失败重试次数: {ANALYSIS_CONFIG['max_retries']}")
logger.info(f"单篇分析超时时间: {ANALYSIS_CONFIG['timeout']['single_analysis']}秒")
logger.info(f"综合分析超时时间: {ANALYSIS_CONFIG['timeout']['overall_analysis']}秒")
logger.info("事件过滤配置:")
logger.info(f"  - 最大事件年龄: {ANALYSIS_CONFIG['event_filter']['max_event_age_days']}天")
logger.info(f"  - 最小事件年龄: {ANALYSIS_CONFIG['event_filter']['min_event_age_days']}天")
logger.info(f"  - 排除未来事件: {ANALYSIS_CONFIG['event_filter']['exclude_future_events']}")
logger.info("发布过滤配置:")
logger.info(f"  - 最大发布年龄: {ANALYSIS_CONFIG['publish_filter']['max_publish_age_days']}天")
logger.info(f"  - 最小发布年龄: {ANALYSIS_CONFIG['publish_filter']['min_publish_age_days']}天")
logger.info(f"  - 排除未来发布: {ANALYSIS_CONFIG['publish_filter']['exclude_future_publish']}")
logger.info(f"\n监控的公众号数量: {len(BIZ_LIST)}")
logger.info("==================== 配置信息结束 ====================\n")
defsave_report_to_storage(report_data:dict,report_text:str)->Tuple[str,str]:
    """
    保存报告到 MongoDB 和 MinIO
    
    Args:
        report_data: 报告数据字典
        report_text: 报告文本内容（Markdown格式）
        
    Returns:
        Tuple[str, str]: 存储路径和报告ID
    """
try:
        logger.info("开始保存金融舆情分析报告...")
time_range=report_data['metadata']['time_range']
try:
            start_date_str=time_range.split('截至')[1].strip().rstrip(')')
end_date=datetime.strptime(start_date_str,'%Y-%m-%d')
days=int(time_range.split('近')[1].split('天')[0].strip())
start_date=end_date-timedelta(days=days)
exceptException:
            logger.warning(f"无法从时间范围 '{time_range}' 解析日期，使用当前时间")
end_date=datetime.now()
days=3
start_date=end_date-timedelta(days=days)
timestamp=datetime.now().strftime('%Y%m%d_%H%M%S')
filename=f"financial_report_storm_{timestamp}.md"
report_bytes=report_text.encode('utf-8')
object_name=f"financial_reports/{filename}"
logger.info(f"正在将报告上传到 MinIO，文件名: {object_name}")
try:
            minio_client.put_object(bucket_name=MINIO_BUCKET,object_name=object_name,data=io.BytesIO(report_bytes),length=len(report_bytes),content_type='text/markdown')
storage_path=f"{MINIO_BUCKET}/{object_name}"
logger.info(f"报告已成功上传到MinIO: {storage_path}")
exceptExceptionase:
            logger.error(f"MinIO上传失败: {str(e)}")
storage_path=f"local://{filename}"
withopen(filename,'wb')asf:
                f.write(report_bytes)
logger.info(f"报告已保存到本地文件: {filename}")
logger.info("正在创建媒体洞察报告记录...")
report_doc=MediaInsightsReport(title=report_data['report_title'],content=report_text,storage_path=storage_path,report_type='financial_opinion',start_time=start_date,end_time=end_date,media_ids=BIZ_LIST)
report_doc.save()
report_id=str(report_doc.id)
logger.info(f"报告已成功保存。MinIO路径: {storage_path}, MongoDB ID: {report_id}")
returnstorage_path,report_id
exceptExceptionase:
        logger.error(f"保存报告失败: {str(e)}")
traceback.print_exc()
fallback_id=str(uuid.uuid4())
local_filename=f"financial_report_fallback_{fallback_id}.md"
try:
            withopen(local_filename,'w',encoding='utf-8')asf:
                f.write(report_text)
logger.info(f"由于错误，报告已备份到本地文件: {local_filename}")
returnlocal_filename,fallback_id
exceptException:
            raise
defsafe_llm_invoke(messages,timeout_seconds=120,default_response=None,description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
try:
        @timeout(timeout_seconds)
definvoke_with_timeout(msgs):
            returnllm.invoke(msgs)
result=invoke_with_timeout(messages)
logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
returnresult
exceptTimeoutExceptionase:
        logger.error(f"{description}超时: {str(e)}")
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
exceptExceptionase:
        logger.error(f"{description}失败: {str(e)}")
traceback.print_exc()
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
defrun_agent(days=None):
    """运行金融舆情分析智能体"""
logger.info("====================== 开始运行金融舆情分析智能体 ======================")
start_time=datetime.now()
logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"分析天数: {daysifdaysisnotNoneelseANALYSIS_CONFIG['max_days']}天")
try:
        logger.info("开始生成报告...")
report_data=generate_report_with_storm()
ifnotreport_data:
            logger.error("报告生成失败，返回数据为空")
return{"status":"error","message":"报告生成失败，返回数据为空"}
logger.info("开始格式化报告...")
report_text=format_storm_report(report_data)
ifnotreport_text:
            logger.error("报告格式化失败，返回文本为空")
return{"status":"error","message":"报告格式化失败，返回文本为空"}
try:
            storage_path,report_id=save_report_to_storage(report_data,report_text)
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
return{"status":"success","report_text":report_text,"report_data":report_data,"storage_path":storage_path,"report_id":report_id}
exceptExceptionase:
            logger.error(f"保存报告失败: {str(e)}")
return{"status":"error","message":f"报告生成成功但保存失败: {str(e)}"}
exceptExceptionase:
        logger.error(f"运行智能体时出错: {str(e)}")
traceback.print_exc()
return{"status":"error","message":str(e)}
classAgentState(TypedDict):
    """智能体状态"""
report_data:Optional[Dict]
report_text:Optional[str]
error:Optional[str]
if__name__=="__main__":
    logger.info("====================== 启动金融舆情分析脚本 ======================")
logger.info(f"本次运行采用直接调用方式，不使用LangGraph工作流")
result=run_agent()
ifresult["status"]=="success":
        print("报告生成成功！")
report_filename=f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
try:
            withopen(report_filename,"w",encoding="utf-8")asf:
                f.write(result["report_text"])
print(f"报告已保存至: {report_filename}")
exceptExceptionase:
            print(f"保存报告失败: {str(e)}")
lines=result["report_text"].split("\n")
preview_lines=lines[:20]+["...","（报告内容省略）","..."]+lines[-5:]
print("\n".join(preview_lines))
else:
        print(f"报告生成失败: {result['message']}")
defget_es_scroll_data_batched(index,query_body,batch_size=1000,es_host=None,es_port=None):
    """滚动查询ES数据，并以批次方式返回"""
ifnotes_host:
        es_host=TARGET_ES_HOST
ifnotes_port:
        es_port=TARGET_ES_PORT
es=Elasticsearch([f"{es_host}:{es_port}"])
sid=None
try:
        result=es.search(index=index,scroll='10m',body=query_body,size=batch_size,request_timeout=3600)
sid=result['_scroll_id']
scroll_size=result['hits']['total']['value']
logger.info(f"索引 {index} 总数据量: {scroll_size}")
iflen(result['hits']['hits'])>0:
            yieldresult['hits']['hits']
scroll_count=len(result['hits']['hits'])
whilescroll_count>0:
            result=es.scroll(scroll_id=sid,scroll='10m',request_timeout=3600)
batch_data=result['hits']['hits']
scroll_count=len(batch_data)
ifscroll_count==0:
                break
yieldbatch_data
exceptExceptionase:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
traceback.print_exc()
finally:
        ifsid:
            try:
                es.clear_scroll(scroll_id=sid)
except:
                pass
ifes:
            try:
                es.close()
except:
                pass
logger.info(f"索引 {index} 查询完成")
defbuild_query_body(start_time,end_time,biz=None):
    """构建ES查询体"""
body={"query":{"bool":{"filter":[{"range":{"createTimeES":{"gte":start_time,"lte":end_time}}}]}},"sort":[{"createTimeES":"desc"}]}
ifbiz:
        ifisinstance(biz,str):
            body["query"]["bool"]["filter"].append({"term":{"site_id":{"value":biz}}})
elifisinstance(biz,list)andlen(biz)>0:
            body["query"]["bool"]["filter"].append({"terms":{"site_id":biz}})
returnbody
defget_recent_reports(hours=24):
    """获取近24小时内的报告数据"""
end_time=datetime.now()
start_time=end_time-timedelta(hours=hours)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")
query_body=build_query_body(start_time_str,end_time_str,BIZ_LIST)
all_data=[]
forbatchinget_es_scroll_data_batched(TARGET_ES_INDEX,query_body):
        all_data.extend([doc['_source']fordocinbatch])
logger.info(f"获取到 {len(all_data)} 条记录")
returnall_data
defis_event_in_time_range(event_time_str):
    """检查事件是否在配置的时间范围内"""
try:
        event_time=datetime.strptime(event_time_str,"%Y-%m-%d %H:%M:%S")
current_time=datetime.now()
event_age_days=(current_time-event_time).days
ifevent_age_days<ANALYSIS_CONFIG["event_filter"]["min_event_age_days"]:
            returnFalse
ifevent_age_days>ANALYSIS_CONFIG["event_filter"]["max_event_age_days"]:
            returnFalse
ifANALYSIS_CONFIG["event_filter"]["exclude_future_events"]andevent_time>current_time:
            returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"事件时间解析错误: {str(e)}")
returnFalse
deffilter_events_by_time(report):
    """过滤报告中的事件信息"""
ifnotreport.get('eventInfo'):
        returnreport
filtered_events=[]
foreventinreport['eventInfo']:
        event_time=None
ifisinstance(event,dict):
            event_time=event.get('time')orevent.get('eventTime')orevent.get('timestamp')
ifnotevent_time:
            filtered_events.append(event)
continue
ifis_event_in_time_range(event_time):
            filtered_events.append(event)
report['eventInfo']=filtered_events
returnreport
defis_publish_time_valid(publish_time_str):
    """检查报告发布时间是否在配置的有效范围内"""
try:
        try:
            publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S")
exceptValueError:
            try:
                publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d")
exceptValueError:
                try:
                    publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S.%f")
exceptValueError:
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
returnFalse
current_time=datetime.now()
publish_age_days=(current_time-publish_time).days
ifpublish_age_days<ANALYSIS_CONFIG["publish_filter"]["min_publish_age_days"]:
            logger.info(f"报告发布时间太新: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifpublish_age_days>ANALYSIS_CONFIG["publish_filter"]["max_publish_age_days"]:
            logger.info(f"报告发布时间太旧: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifANALYSIS_CONFIG["publish_filter"]["exclude_future_publish"]andpublish_time>current_time:
            logger.info(f"报告发布时间在未来: {publish_time_str}")
returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"发布时间验证错误: {str(e)}")
returnFalse
defget_latest_report_by_account(days=3):
    """获取每个公众号最近3天的最新一篇报告"""
end_time=datetime.now()
start_time=end_time-timedelta(days=days)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")
latest_reports={}
filtered_count=0
total_count=0
forbizinBIZ_LIST:
        query_body=build_query_body(start_time_str,end_time_str,biz)
query_body["size"]=5
try:
            es=Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
result=es.search(index=TARGET_ES_INDEX,body=query_body)
valid_report_found=False
forhitinresult['hits']['hits']:
                total_count+=1
doc=hit['_source']
publish_time=doc.get('publishtime','')
ifnotpublish_timeornotis_publish_time_valid(publish_time):
                    filtered_count+=1
logger.info(f"过滤掉公众号 {doc.get('source',biz)} 的文章: {doc.get('title','无标题')}, 发布时间: {publish_time}")
continue
report={'title':doc.get('title','无标题'),'content':doc.get('content','无内容'),'author':doc.get('new_author','未知作者'),'site_name':doc.get('source','未知公众号'),'publishtime':publish_time,'authorViewpoint':doc.get('authorViewpoint',''),'characterEntity':doc.get('characterEntity',[]),'institutionalEntities':doc.get('institutionalEntities',[]),'locationEntity':doc.get('locationEntity',[]),'eventInfo':doc.get('eventInfo',[]),'summaryFacts':doc.get('summaryFacts',[]),'summary':doc.get('summary','')}
report=filter_events_by_time(report)
latest_reports[biz]=report
logger.info(f"获取到公众号 {doc.get('source',biz)} 有效文章, 发布时间: {publish_time}, 标题: {doc.get('title','无标题')}")
valid_report_found=True
break
ifnotvalid_report_found:
                logger.warning(f"未找到公众号 {biz} 的有效报告")
exceptExceptionase:
            logger.error(f"获取公众号 {biz} 最新报告时出错: {str(e)}")
finally:
            if'es'inlocals():
                es.close()
logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告，过滤掉 {filtered_count}/{total_count} 篇不符合发布时间条件的报告")
returnlatest_reports
defanalyze_report_with_reasoning(title,content,account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
system_msg=SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")
human_msg=HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000]ifcontentandlen(content)>6000elsecontent}
""")
logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000]ifcontentandlen(content)>6000elsecontent)}")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=120,default_response=default_analysis,description=f"'{account_name}'报告初始分析")
logger.info("开始生成结构化分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=60,default_response=default_result,description=f"'{account_name}'报告结构化分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints',[]))} 条核心观点")
returnanalysis
else:
            logger.error("无法找到JSON内容")
return{"summary":"无法解析报告内容","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"无法分析","reasoning_chain":[]}
exceptExceptionase:
        logger.error(f"分析报告内容时出错: {str(e)}")
return{"summary":"分析过程出错","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"分析出错","reasoning_chain":[]}
defanalyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
all_viewpoints=[]
all_entities=set()
all_institutions=set()
all_events=set()
report_summaries=[]
forbiz,reportinreports_data.items():
        if'analysis'inreportandreport['analysis'].get('core_viewpoints'):
            forviewpointinreport['analysis']['core_viewpoints']:
                all_viewpoints.append({'site_name':report['site_name'],'viewpoint':viewpoint})
ifreport.get('authorViewpoint'):
            all_viewpoints.append({'site_name':report['site_name'],'viewpoint':report['authorViewpoint']})
all_entities.update(report.get('characterEntity',[]))
all_institutions.update(report.get('institutionalEntities',[]))
all_events.update(report.get('eventInfo',[]))
ifreport.get('summary'):
            report_summaries.append({'site_name':report['site_name'],'summary':report['summary']})
logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")
data_summary={"total_reports":len(reports_data),"unique_entities":list(all_entities)[:10],"unique_institutions":list(all_institutions)[:10],"unique_events":list(all_events)[:10],"viewpoint_count":len(all_viewpoints)}
viewpoints_text="\n\n".join([f"{item['site_name']}: {item['viewpoint']}"foriteminall_viewpoints])
logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")
logger.info("开始向LLM发送综合分析请求...")
system_msg=SystemMessage(content="""你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容需要按照以下维度进行详细分析：

1. 宏观经济预期分析
   - 各机构对宏观经济的预判（标注观点来源机构）
   - 主要依据和论据（标注来源）
   - 共识观点和分歧观点

2. 宏观政策预期分析
   - 各机构对政策走向的判断（标注观点来源机构）
   - 政策预期的主要依据（标注来源）
   - 共识观点和分歧观点

3. 利率走势预测分析
   - 各机构对利率走势的预测（标注观点来源机构）
   - 预测的主要依据（标注来源）
   - 共识观点和分歧观点

4. 投资策略建议分析
   - 各机构的投资策略建议（标注观点来源机构）
   - 投资机会分析（标注来源）
   - 共识建议和分歧建议

5. 市场观点分析
   - 市场主流观点
   - 观点共识（哪些机构持有相同观点）
   - 观点分歧（哪些机构观点不同）

6. 风险与机会分析
   - 主要风险点（标注提出风险的机构）
   - 主要机会点（标注提出机会的机构）

请用结构化JSON格式返回，确保每个观点都注明出处机构。""")
human_msg=HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度必须包括：
1. 宏观经济预期分析
2. 宏观政策预期分析
3. 利率走势预测分析
4. 投资策略建议分析
5. 市场观点分析（共识与分歧）
6. 风险与机会分析

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])ifdata_summary['unique_institutions']else'无'}
- 主要事件: {', '.join(data_summary['unique_events'][:5])ifdata_summary['unique_events']else'无'}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。每个维度的分析都要尽可能详细，并确保标注来源机构。

以下是各家机构的观点:
{viewpoints_text}
""")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成综合分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=180,default_response=default_analysis,description="多机构报告综合分析")
logger.info("开始生成结构化综合分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构，并按照要求的维度进行详细分析。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，必须包含以下字段:

1. macro_economic_outlook: 宏观经济预期分析（数组格式，每项包含观点内容和来源机构）
2. policy_expectations: 宏观政策预期分析（数组格式，每项包含观点内容和来源机构）
3. interest_rate_forecast: 利率走势预测分析（数组格式，每项包含观点内容和来源机构）
4. investment_recommendations: 投资策略建议分析（数组格式，每项包含观点内容和来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）

对于每个数组项，请使用以下格式：
{
  "content": "观点内容",
  "sources": ["机构A", "机构B"]
}

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "macro_economic_outlook": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "policy_expectations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "interest_rate_forecast": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "investment_recommendations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "consensus_points": [{"content": "无法识别共识观点", "sources": []}],
  "divergent_points": [{"content": "无法识别分歧观点", "sources": []}],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": [{"content": "无法识别市场风险", "sources": []}],
  "market_opportunities": [{"content": "无法识别市场机会", "sources": []}]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=120,default_response=default_result,description="生成结构化综合分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points',[]))} 条共识观点, {len(analysis.get('divergent_points',[]))} 条分歧观点")
returnanalysis
else:
            logger.error("无法找到综合分析的JSON内容")
return{"macro_economic_outlook":[{"content":"无法解析综合观点","sources":[]}],"policy_expectations":[{"content":"无法解析综合观点","sources":[]}],"interest_rate_forecast":[{"content":"无法解析综合观点","sources":[]}],"investment_recommendations":[{"content":"无法解析综合观点","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"无法生成综合分析","market_risks":[],"market_opportunities":[]}
exceptExceptionase:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
return{"macro_economic_outlook":[{"content":"分析过程出错","sources":[]}],"policy_expectations":[{"content":"分析过程出错","sources":[]}],"interest_rate_forecast":[{"content":"分析过程出错","sources":[]}],"investment_recommendations":[{"content":"分析过程出错","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"分析过程出错","market_risks":[],"market_opportunities":[]}
defgenerate_executive_summary(overall_analysis,reports_count):
    """生成执行摘要"""
logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")
defformat_complex_item(items,max_items=3):
        ifnotitems:
            return'无数据'
ifisinstance(items,str):
            returnitems
formatted_points=[]
count=0
foriteminitems[:max_items]:
            ifisinstance(item,dict):
                content=item.get('content','')
sources=item.get('sources',[])
ifisinstance(sources,list)andsources:
                    sources_str='、'.join(sources)
formatted_points.append(f"{content}（来源：{sources_str}）")
else:
                    formatted_points.append(content)
elifisinstance(item,str):
                formatted_points.append(item)
count+=1
ifcount>=max_items:
                break
iflen(items)>max_items:
            formatted_points.append("等")
return'；'.join(formatted_points)
system_msg=SystemMessage(content="""你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
摘要应包含以下几个关键维度的分析：宏观经济预期、宏观政策预期、利率走势预测、投资策略建议、市场观点共识与分歧、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。""")
try:
        macro_economic=format_complex_item(overall_analysis.get('macro_economic_outlook',[]))
policy_expectations=format_complex_item(overall_analysis.get('policy_expectations',[]))
interest_rate=format_complex_item(overall_analysis.get('interest_rate_forecast',[]))
investment=format_complex_item(overall_analysis.get('investment_recommendations',[]))
consensus=format_complex_item(overall_analysis.get('consensus_points',[]))
divergent=format_complex_item(overall_analysis.get('divergent_points',[]))
risks=format_complex_item(overall_analysis.get('market_risks',[]))
opportunities=format_complex_item(overall_analysis.get('market_opportunities',[]))
overall_summary=overall_analysis.get('overall_summary','无法获取整体市场观点摘要')
human_msg=HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下维度：

1. 宏观经济预期分析：{macro_economic}

2. 宏观政策预期分析：{policy_expectations}

3. 利率走势预测分析：{interest_rate}

4. 投资策略建议分析：{investment}

5. 市场观点分析：
   - 共识观点：{consensus}
   - 分歧观点：{divergent}

6. 风险与机会：
   - 主要风险：{risks}
   - 主要机会：{opportunities}

7. 整体市场观点：{overall_summary}

请生成一份不超过400字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。
摘要需要结构清晰，重点突出，便于高管快速把握市场关键信息。""")
logger.info("向LLM发送执行摘要生成请求...")
messages=[system_msg,human_msg]
default_summary="无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
result=safe_llm_invoke(messages,timeout_seconds=90,default_response=default_summary,description="生成执行摘要")
logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
returnresult.content
exceptExceptionase:
        logger.error(f"生成执行摘要时出错: {str(e)}")
traceback.print_exc()
return"无法生成执行摘要。错误原因："+str(e)
defformat_structured_list(items,content_key='content',source_key='sources'):
    lines=[]
foriteminitems:
        ifisinstance(item,dict):
            content=item.get(content_key)oritem.get('point')oritem.get('risk')oritem.get('opportunity')or''
sources=item.get(source_key)oritem.get('institutions')oritem.get('source')or[]
ifisinstance(sources,list):
                sources='、'.join(sources)
ifsources:
                lines.append(f"- {content}（来源：{sources}）")
else:
                lines.append(f"- {content}")
elifisinstance(item,str):
            lines.append(f"- {item}")
return'\n'.join(lines)
defadd_heading_numbering(md_text):
    lines=md_text.split('\n')
h1,h2,h3=0,0,0
new_lines=[]
forlineinlines:
        ifline.startswith('# '):
            h1+=1;h2=0;h3=0
new_lines.append(f"# {h1}. {line[2:]}")
elifline.startswith('## '):
            h2+=1;h3=0
new_lines.append(f"## {h1}.{h2} {line[3:]}")
elifline.startswith('### '):
            h3+=1
new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
else:
            new_lines.append(line)
return'\n'.join(new_lines)
defgenerate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
days_to_fetch=ANALYSIS_CONFIG.get("max_days",3)
logger.info(f"第1步: 获取每个公众号最新报告（{days_to_fetch}天内）...")
latest_reports=get_latest_report_by_account(days=days_to_fetch)
logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
errors=[]
total=len(latest_reports)
logger.info(f"第2步: 开始并行分析 {total} 个公众号报告...")
defanalyze_single_report(biz_report_tuple):
        biz,report=biz_report_tuple
try:
            content=report['content']
ifcontentandlen(content)<ANALYSIS_CONFIG.get("min_word_count",500):
                returnbiz,None,f"{report['site_name']} 内容过短，不进行分析"
logger.info(f"开始分析公众号: {report['site_name']}")
analysis=analyze_report_with_reasoning(report['title'],content,report['site_name'])
logger.info(f"完成公众号分析: {report['site_name']}")
returnbiz,analysis,None
exceptExceptionase:
            error_msg=f"{report['site_name']} 分析失败: {str(e)}"
logger.error(error_msg)
returnbiz,None,error_msg
max_workers=min(10,total)
withThreadPoolExecutor(max_workers=max_workers)asexecutor:
        results=list(executor.map(analyze_single_report,latest_reports.items()))
valid_reports_count=0
forbiz,analysis,errorinresults:
        iferror:
            errors.append(error)
elifanalysis:
            latest_reports[biz]['analysis']=analysis
valid_reports_count+=1
logger.info(f"成功分析 {valid_reports_count}/{total} 个报告")
ifvalid_reports_count==0:
        error_msg="未能成功分析任何报告，无法生成综合分析"
logger.error(error_msg)
errors.append(error_msg)
return{"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":"无法生成报告，未能成功分析任何内容。","individual_reports":{},"overall_analysis":{},"metadata":{"reports_count":0,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("第3步: 开始综合分析所有报告...")
try:
        overall_analysis=analyze_all_reports_with_storm(latest_reports)
logger.info("综合分析完成")
exceptExceptionase:
        error_msg=f"综合分析失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
overall_analysis={}
logger.info("第4步: 开始生成执行摘要...")
try:
        executive_summary=generate_executive_summary(overall_analysis,valid_reports_count)
logger.info("执行摘要生成完成")
exceptExceptionase:
        error_msg=f"执行摘要生成失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
executive_summary="执行摘要生成失败。"
logger.info("第5步: 组装最终报告...")
report={"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":executive_summary,"individual_reports":latest_reports,"overall_analysis":overall_analysis,"metadata":{"reports_count":valid_reports_count,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("==================== 报告生成过程完成 ====================")
returnreport
defformat_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
logger.info("开始格式化STORM报告...")
start_time=datetime.now()
output=f"# {report['report_title']}\n\n"
output+=f"生成时间: {report['generation_time']}\n"
output+=f"分析报告数: {report['metadata']['reports_count']}\n"
output+=f"时间范围: {report['metadata']['time_range']}\n\n"
overall=report['overall_analysis']
logger.info("生成第一章：执行摘要...")
output+="## 执行摘要\n\n"
output+=f"{report['executive_summary']}\n\n"
logger.info("生成第二章：市场综合分析...")
output+="## 市场综合分析\n\n"
logger.info("生成2.1节：宏观经济预期分析...")
output+="### 宏观经济预期分析\n\n"
macro_economic=overall.get('macro_economic_outlook','')
ifisinstance(macro_economic,str):
        output+=macro_economic+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(macro_economic)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观经济观点共识与分歧。\n"
output+="\n"
logger.info("生成2.2节：宏观政策预期分析...")
output+="### 宏观政策预期分析\n\n"
policy_expectations=overall.get('policy_expectations','')
ifisinstance(policy_expectations,str):
        output+=policy_expectations+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(policy_expectations)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观政策观点共识与分歧。\n"
output+="\n"
logger.info("生成2.3节：利率走势预测分析...")
output+="### 利率走势预测分析\n\n"
interest_rate=overall.get('interest_rate_forecast','')
ifisinstance(interest_rate,str):
        output+=interest_rate+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(interest_rate)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的利率走势观点共识与分歧。\n"
output+="\n"
logger.info("生成2.4节：投资策略建议分析...")
output+="### 投资策略建议分析\n\n"
investment=overall.get('investment_recommendations','')
ifisinstance(investment,str):
        output+=investment+"\n\n"
else:
        output+="#### 主要投资建议汇总\n\n"
output+=format_structured_list(investment)+"\n\n"
output+="#### 建议共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的投资建议共识与分歧。\n"
output+="\n"
logger.info("生成2.5节：市场观点分析...")
output+="### 市场观点分析\n\n"
output+="#### 市场主流观点\n\n"
output+=f"{overall.get('overall_summary','未能提取市场主流观点。')}\n\n"
output+="#### 观点共识\n\n"
consensus_points=overall.get('consensus_points',[])
ifconsensus_points:
        forpointinconsensus_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (支持机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点共识。\n"
output+="\n"
output+="#### 观点分歧\n\n"
divergent_points=overall.get('divergent_points',[])
ifdivergent_points:
        forpointindivergent_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (相关机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点分歧。\n"
output+="\n"
logger.info("生成2.6节：风险与机会...")
output+="### 风险与机会\n\n"
output+="#### 市场主要风险\n\n"
market_risks=overall.get('market_risks',[])
ifmarket_risks:
        forriskinmarket_risks:
            ifisinstance(risk,str):
                output+=f"- {risk}\n"
elifisinstance(risk,dict):
                content=risk.get('content')orrisk.get('risk')orstr(risk)
sources=risk.get('sources')orrisk.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场风险点。\n"
output+="\n"
output+="#### 市场主要机会\n\n"
market_opportunities=overall.get('market_opportunities',[])
ifmarket_opportunities:
        foropportunityinmarket_opportunities:
            ifisinstance(opportunity,str):
                output+=f"- {opportunity}\n"
elifisinstance(opportunity,dict):
                content=opportunity.get('content')oropportunity.get('opportunity')orstr(opportunity)
sources=opportunity.get('sources')oropportunity.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场机会点。\n"
output+="\n"
logger.info("生成第三章：各机构观点明细...")
output+="## 各机构观点明细\n\n"
report_count=0
forbiz,report_datainreport['individual_reports'].items():
        if'analysis'notinreport_data:
            continue
analysis=report_data['analysis']
output+=f"### {report_data['site_name']}\n\n"
output+="#### 报告信息\n\n"
output+=f"**标题**: {report_data['title']}\n\n"
output+=f"**发布时间**: {report_data['publishtime']}\n\n"
output+="#### 核心观点摘要\n\n"
output+=f"{analysis.get('summary','未提供摘要')}\n\n"
output+="#### 具体观点\n\n"
core_viewpoints=analysis.get('core_viewpoints',[])
ifcore_viewpoints:
            forpointincore_viewpoints:
                output+=f"- {point}\n"
else:
            output+="未提取到核心观点。\n"
output+="\n"
output+="#### 观点依据\n\n"
supporting_evidence=analysis.get('supporting_evidence',[])
ifsupporting_evidence:
            forevidenceinsupporting_evidence:
                output+=f"- {evidence}\n"
else:
            output+="未提取到观点依据。\n"
output+="\n"
ifanalysis.get('market_impact')oranalysis.get('key_insights'):
            output+="#### 市场影响与洞见\n\n"
ifanalysis.get('market_impact'):
                output+=f"**市场影响**: {analysis.get('market_impact')}\n\n"
ifanalysis.get('key_insights'):
                output+=f"**关键洞见**: {analysis.get('key_insights')}\n\n"
output+="---\n\n"
report_count+=1
ifreport_count==0:
        output+="未能获取到任何机构的有效分析结果。\n\n"
logger.info("生成第四章：附录...")
output+="## 附录\n\n"
output+="### 分析方法说明\n\n"
output+=f"本报告采用 {report['metadata']['generation_method']} 方法，通过大语言模型对各金融机构公众号发布的研究报告进行自动化分析和总结。\n"
output+="分析过程包括：\n"
output+="1. 获取各公众号最新研究报告\n"
output+="2. 对每篇报告进行结构化分析，提取核心观点和依据\n"
output+="3. 综合分析所有报告，识别共识观点和分歧点\n"
output+="4. 按照宏观经济、政策预期、利率走势和投资建议等维度进行归纳\n\n"
output+="### 数据来源说明\n\n"
output+=f"- 数据时间范围: {report['metadata']['time_range']}\n"
output+=f"- 报告生成时间: {report['generation_time']}\n"
output+=f"- 分析机构列表: {', '.join([report_data.get('site_name','未知')forbiz,report_datainreport['individual_reports'].items()if'analysis'inreport_data])}\n\n"
output+="### 执行情况\n\n"
errors=report.get("errors",[])
iferrors:
        output+="本次报告生成过程中出现如下异常：\n"
forerrinerrors:
            output+=f"- {err}\n"
else:
        output+="本次报告生成过程无异常。\n"
end_time=datetime.now()
total_elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
output=add_heading_numbering(output)
returnoutput


============================================================
文件: backend\app\agent\financialPublicOpinionReport_weixin_V4.py
============================================================

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""
importconcurrent.futures
importfunctools
importjson
importlogging
importos
importplatform
importsignal
importthreading
importtraceback
importuuid
importio
fromconcurrent.futuresimportThreadPoolExecutor
fromcontextlibimportcontextmanager
fromdatetimeimportdatetime,timedelta
fromtypingimportAny,Callable,Dict,List,Optional,TypedDict
fromioimportBytesIO
importmarkdown
fromreportlab.libimportcolors
fromreportlab.lib.pagesizesimportA4
fromreportlab.lib.stylesimportgetSampleStyleSheet,ParagraphStyle
fromreportlab.lib.unitsimportinch
fromreportlab.pdfbaseimportpdfmetrics
fromreportlab.pdfbase.ttfontsimportTTFont
fromreportlab.platypusimportSimpleDocTemplate,Paragraph,Spacer,Table,TableStyle
importtempfile
importpandasaspd
fromelasticsearchimportElasticsearch
fromlangchain_core.messagesimportAIMessage,HumanMessage,SystemMessage
fromlangchain_core.promptsimportChatPromptTemplate
fromlangchain_openaiimportChatOpenAI
fromlanggraph.graphimportEND,StateGraph
fromminioimportMinio
frommongoengineimportDocument,StringField,DateTimeField,ListField,ObjectIdField,connect,disconnect
frombsonimportObjectId
importre
importtime
classMediaInsightsReport(Document):
    meta={'collection':'media_insights_reports'}
_id=ObjectIdField(primary_key=True,default=lambda:ObjectId())
title=StringField(required=True)
content=StringField(required=True)
storage_path=StringField(required=True)
created_at=DateTimeField(default=datetime.now)
report_type=StringField(required=True)
start_time=DateTimeField(required=True)
end_time=DateTimeField(required=True)
media_ids=ListField(StringField(),default=list)
classTimeoutException(Exception):
    pass
deftimeout(seconds):
    defdecorator(func):
        @functools.wraps(func)
defwrapper(*args,**kwargs):
            start_time=datetime.now()
func_name=func.__name__
logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
withThreadPoolExecutor(max_workers=1)asexecutor:
                future=executor.submit(func,*args,**kwargs)
try:
                    result=future.result(timeout=seconds)
elapsed=(datetime.now()-start_time).total_seconds()
logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
returnresult
exceptconcurrent.futures.TimeoutError:
                    future.cancel()
elapsed=(datetime.now()-start_time).total_seconds()
logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
raiseTimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
returnwrapper
returndecorator
IS_UNIX=platform.system()in('Linux','Darwin')andos.name=='posix'
ifIS_UNIX:
    @contextmanager
deftimeout_context(seconds):
        defhandle_timeout(signum,frame):
            raiseTimeoutException(f"操作超时（{seconds}秒）")
original_handler=signal.getsignal(signal.SIGALRM)
signal.signal(signal.SIGALRM,handle_timeout)
try:
            signal.alarm(seconds)
yield
finally:
            signal.alarm(0)
signal.signal(signal.SIGALRM,original_handler)
else:
    @contextmanager
deftimeout_context(seconds):
        defdo_nothing():
            pass
withThreadPoolExecutor(max_workers=1)asexecutor:
            future=executor.submit(do_nothing)
try:
                future.result(timeout=0.001)
yield
exceptconcurrent.futures.TimeoutError:
                logger.warning("初始化超时上下文出错")
yield
logging.basicConfig(level=logging.INFO,format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger=logging.getLogger(__name__)
TARGET_ES_HOST="**************"
TARGET_ES_PORT=9600
TARGET_ES_INDEX="pro_mcp_data_weixin"
MONGODB_USER="wiseAgent001"
MONGODB_PASSWORD="1qaz@WSX"
DATABASE_NAME="wiseagent"
MONGODB_AUTH_SOURCE="wiseagent"
MONGODB_HOST="*************"
MONGODB_PORT=37017
MINIO_ENDPOINT="**************:9002"
MINIO_ACCESS_KEY="qFHq6y3pNfboA7YBNynE"
MINIO_SECRET_KEY="P5lTULcF2IEtX47mkdmfuDpQVkYJEeL2AKEhvDRr"
MINIO_BUCKET="wiseagentfiles"
LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
BIZ_LIST=["MzA4MDY1NTUyMg==","MzA5OTY5MzM4Ng==","Mzg2Mjg1NTg3NA==","Mzg4NTEwMzA5NQ==","Mzg5MjU4MDkyMw==","MzI4NTU0NDE4Mw==","MzI5MzQxOTI0MQ==","MzIwMDI3NjM2Mg==","MzIzMjc3NTYyNQ==","Mzk0NjUwMDIxOQ==","MzkxMDYyNzExMA==","MzkzNTYzMDYxMg==","MzU3MDMwODc2MA==","MzU4NzcwNDcxOA==","MzU4ODM4NzI5Nw==","MzU5MzkzMTY3Mg==","MzUxMDk5NDgwNQ==","MzUxMDkyMzA4Mw==","MzUzMzEyODIyMA==","MzUzNDcxMDgzNg==","MzA3MTIzNDcwMg==","MzA3NjU1NTQwMA==","MzA4MzY2ODYwMw==","MzAwNzMxODYyNg==","Mzg4NDU2MDM3Mw==","MzI1NzAwODc3Nw==","MzU3MDMwODc2MA==","MzU3NTYyNTIyMQ==","MzUzMzYwODI2MA=="]
llm=ChatOpenAI(temperature=0,model=LLM_MODEL,openai_api_base=LLM_API_BASE,api_key=OPENAI_API_KEY,request_timeout=120)
defsafe_llm_invoke(messages,timeout_seconds=120,default_response=None,description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
try:
        @timeout(timeout_seconds)
definvoke_with_timeout(msgs):
            returnllm.invoke(msgs)
result=invoke_with_timeout(messages)
logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
returnresult
exceptTimeoutExceptionase:
        logger.error(f"{description}超时: {str(e)}")
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
exceptExceptionase:
        logger.error(f"{description}失败: {str(e)}")
traceback.print_exc()
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
defget_es_scroll_data_batched(index,query_body,batch_size=1000,es_host=None,es_port=None,max_retries=3):
    """滚动查询ES数据，并以批次方式返回"""
ifnotes_host:
        es_host=TARGET_ES_HOST
ifnotes_port:
        es_port=TARGET_ES_PORT
retry_count=0
whileretry_count<max_retries:
        es=None
sid=None
try:
            es=Elasticsearch([f"{es_host}:{es_port}"],timeout=60)
ifnotes.ping():
                raiseConnectionError("无法连接到 Elasticsearch")
result=es.search(index=index,scroll='10m',body=query_body,size=batch_size,request_timeout=3600)
sid=result['_scroll_id']
scroll_size=result['hits']['total']['value']
logger.info(f"索引 {index} 总数据量: {scroll_size}")
iflen(result['hits']['hits'])>0:
                yieldresult['hits']['hits']
scroll_count=len(result['hits']['hits'])
whilescroll_count>0:
                try:
                    result=es.scroll(scroll_id=sid,scroll='10m',request_timeout=3600)
batch_data=result['hits']['hits']
scroll_count=len(batch_data)
ifscroll_count==0:
                        break
yieldbatch_data
exceptExceptionasscroll_error:
                    logger.error(f"滚动查询时出错: {str(scroll_error)}")
break
break
exceptExceptionase:
            retry_count+=1
logger.error(f"获取索引 {index} 数据时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
ifretry_count<max_retries:
                logger.info(f"等待 5 秒后重试...")
time.sleep(5)
else:
                logger.error(f"达到最大重试次数，放弃获取数据")
raise
finally:
            ifsidandes:
                try:
                    es.clear_scroll(scroll_id=sid)
except:
                    pass
ifes:
                try:
                    es.close()
except:
                    pass
logger.info(f"索引 {index} 查询完成")
defbuild_query_body(start_time,end_time,biz=None):
    """构建ES查询体"""
body={"query":{"bool":{"filter":[{"range":{"publishtime":{"gte":start_time,"lte":end_time}}}]}},"sort":[{"publishtime":"desc"}]}
ifbiz:
        ifisinstance(biz,str):
            body["query"]["bool"]["filter"].append({"term":{"site_id":{"value":biz}}})
elifisinstance(biz,list)andlen(biz)>0:
            body["query"]["bool"]["filter"].append({"terms":{"site_id":biz}})
returnbody
defget_recent_reports(hours=24,max_retries=3):
    """获取近24小时内的报告数据"""
end_time=datetime.now()
start_time=end_time-timedelta(hours=hours)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")
query_body=build_query_body(start_time_str,end_time_str,BIZ_LIST)
all_data=[]
retry_count=0
whileretry_count<max_retries:
        try:
            forbatchinget_es_scroll_data_batched(TARGET_ES_INDEX,query_body):
                all_data.extend([doc['_source']fordocinbatch])
break
exceptExceptionase:
            retry_count+=1
logger.error(f"获取报告数据时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
ifretry_count<max_retries:
                logger.info(f"等待 5 秒后重试...")
time.sleep(5)
all_data=[]
else:
                logger.error(f"达到最大重试次数，放弃获取数据")
raise
logger.info(f"获取到 {len(all_data)} 条记录")
returnall_data
defis_event_in_time_range(event_time_str):
    """检查事件是否在配置的时间范围内"""
try:
        event_time=datetime.strptime(event_time_str,"%Y-%m-%d %H:%M:%S")
current_time=datetime.now()
event_age_days=(current_time-event_time).days
ifevent_age_days<ANALYSIS_CONFIG["event_filter"]["min_event_age_days"]:
            returnFalse
ifevent_age_days>ANALYSIS_CONFIG["event_filter"]["max_event_age_days"]:
            returnFalse
ifANALYSIS_CONFIG["event_filter"]["exclude_future_events"]andevent_time>current_time:
            returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"事件时间解析错误: {str(e)}")
returnFalse
deffilter_events_by_time(report):
    """过滤报告中的事件信息"""
ifnotreport.get('eventInfo'):
        returnreport
filtered_events=[]
foreventinreport['eventInfo']:
        event_time=None
ifisinstance(event,dict):
            event_time=event.get('time')orevent.get('eventTime')orevent.get('timestamp')
ifnotevent_time:
            filtered_events.append(event)
continue
ifis_event_in_time_range(event_time):
            filtered_events.append(event)
report['eventInfo']=filtered_events
returnreport
ANALYSIS_CONFIG={"max_days":3,"min_word_count":500,"max_retries":3,"timeout":{"single_analysis":120,"overall_analysis":180,},"event_filter":{"max_event_age_days":7,"min_event_age_days":0,"exclude_future_events":True},"publish_filter":{"max_publish_age_days":7,"min_publish_age_days":0,"exclude_future_publish":True}}
defis_publish_time_valid(publish_time_str):
    """检查报告发布时间是否在配置的有效范围内"""
try:
        try:
            publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S")
exceptValueError:
            try:
                publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d")
exceptValueError:
                try:
                    publish_time=datetime.strptime(publish_time_str,"%Y-%m-%d %H:%M:%S.%f")
exceptValueError:
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
returnFalse
current_time=datetime.now()
publish_age_days=(current_time-publish_time).days
ifpublish_age_days<ANALYSIS_CONFIG["publish_filter"]["min_publish_age_days"]:
            logger.info(f"报告发布时间太新: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifpublish_age_days>ANALYSIS_CONFIG["publish_filter"]["max_publish_age_days"]:
            logger.info(f"报告发布时间太旧: {publish_time_str}, 年龄: {publish_age_days}天")
returnFalse
ifANALYSIS_CONFIG["publish_filter"]["exclude_future_publish"]andpublish_time>current_time:
            logger.info(f"报告发布时间在未来: {publish_time_str}")
returnFalse
returnTrue
exceptExceptionase:
        logger.error(f"发布时间验证错误: {str(e)}")
returnFalse
defget_latest_report_by_account(days=3,max_retries=3):
    """获取每个公众号最近3天的最新一篇报告"""
end_time=datetime.now()
start_time=end_time-timedelta(days=days)
end_time_str=end_time.strftime("%Y-%m-%d %H:%M:%S")
start_time_str=start_time.strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")
latest_reports={}
filtered_count=0
total_count=0
forbizinBIZ_LIST:
        retry_count=0
whileretry_count<max_retries:
            try:
                query_body=build_query_body(start_time_str,end_time_str,biz)
query_body["size"]=5
es=Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"],timeout=60)
result=es.search(index=TARGET_ES_INDEX,body=query_body,request_timeout=60)
valid_report_found=False
forhitinresult['hits']['hits']:
                    total_count+=1
doc=hit['_source']
publish_time=doc.get('publishtime','')
ifnotpublish_timeornotis_publish_time_valid(publish_time):
                        filtered_count+=1
logger.info(f"过滤掉公众号 {doc.get('source',biz)} 的文章: {doc.get('title','无标题')}, 发布时间: {publish_time}")
continue
report={'title':doc.get('title','无标题'),'content':doc.get('content','无内容'),'author':doc.get('new_author','未知作者'),'site_name':doc.get('source','未知公众号'),'publishtime':publish_time,'authorViewpoint':doc.get('authorViewpoint',''),'characterEntity':doc.get('characterEntity',[]),'institutionalEntities':doc.get('institutionalEntities',[]),'locationEntity':doc.get('locationEntity',[]),'eventInfo':doc.get('eventInfo',[]),'summaryFacts':doc.get('summaryFacts',[]),'summary':doc.get('summary','')}
report=filter_events_by_time(report)
latest_reports[biz]=report
logger.info(f"获取到公众号 {doc.get('source',biz)} 有效文章, 发布时间: {publish_time}, 标题: {doc.get('title','无标题')}")
valid_report_found=True
break
ifnotvalid_report_found:
                    logger.warning(f"未找到公众号 {biz} 的有效报告")
break
exceptExceptionase:
                retry_count+=1
logger.error(f"获取公众号 {biz} 最新报告时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
ifretry_count<max_retries:
                    logger.info(f"等待 5 秒后重试...")
time.sleep(5)
else:
                    logger.error(f"达到最大重试次数，跳过公众号 {biz}")
finally:
                if'es'inlocals():
                    try:
                        es.close()
except:
                        pass
logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告，过滤掉 {filtered_count}/{total_count} 篇不符合发布时间条件的报告")
returnlatest_reports
defanalyze_report_with_reasoning(title,content,account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
system_msg=SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")
human_msg=HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000]ifcontentandlen(content)>6000elsecontent}
""")
logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000]ifcontentandlen(content)>6000elsecontent)}")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=120,default_response=default_analysis,description=f"'{account_name}'报告初始分析")
logger.info("开始生成结构化分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=60,default_response=default_result,description=f"'{account_name}'报告结构化分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints',[]))} 条核心观点")
returnanalysis
else:
            logger.error("无法找到JSON内容")
return{"summary":"无法解析报告内容","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"无法分析","reasoning_chain":[]}
exceptExceptionase:
        logger.error(f"分析报告内容时出错: {str(e)}")
return{"summary":"分析过程出错","core_viewpoints":[],"supporting_evidence":[],"key_insights":"","market_impact":"分析出错","reasoning_chain":[]}
defanalyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
all_viewpoints=[]
all_entities=set()
all_institutions=set()
all_events=set()
report_summaries=[]
forbiz,reportinreports_data.items():
        if'analysis'inreportandreport['analysis'].get('core_viewpoints'):
            forviewpointinreport['analysis']['core_viewpoints']:
                all_viewpoints.append({'site_name':report['site_name'],'viewpoint':viewpoint})
ifreport.get('authorViewpoint'):
            all_viewpoints.append({'site_name':report['site_name'],'viewpoint':report['authorViewpoint']})
all_entities.update(report.get('characterEntity',[]))
all_institutions.update(report.get('institutionalEntities',[]))
all_events.update(report.get('eventInfo',[]))
ifreport.get('summary'):
            report_summaries.append({'site_name':report['site_name'],'summary':report['summary']})
logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")
data_summary={"total_reports":len(reports_data),"unique_entities":list(all_entities)[:10],"unique_institutions":list(all_institutions)[:10],"unique_events":list(all_events)[:10],"viewpoint_count":len(all_viewpoints)}
viewpoints_text="\n\n".join([f"{item['site_name']}: {item['viewpoint']}"foriteminall_viewpoints])
logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")
logger.info("开始向LLM发送综合分析请求...")
system_msg=SystemMessage(content="""你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容需要按照以下维度进行详细分析：

1. 宏观经济预期分析
   - 各机构对宏观经济的预判（标注观点来源机构）
   - 主要依据和论据（标注来源）
   - 共识观点和分歧观点

2. 宏观政策预期分析
   - 各机构对政策走向的判断（标注观点来源机构）
   - 政策预期的主要依据（标注来源）
   - 共识观点和分歧观点

3. 利率走势预测分析
   - 各机构对利率走势的预测（标注观点来源机构）
   - 预测的主要依据（标注来源）
   - 共识观点和分歧观点

4. 投资策略建议分析
   - 各机构的投资策略建议（标注观点来源机构）
   - 投资机会分析（标注来源）
   - 共识建议和分歧建议

5. 市场观点分析
   - 市场主流观点
   - 观点共识（哪些机构持有相同观点）
   - 观点分歧（哪些机构观点不同）

6. 风险与机会分析
   - 主要风险点（标注提出风险的机构）
   - 主要机会点（标注提出机会的机构）

请用结构化JSON格式返回，确保每个观点都注明出处机构。""")
human_msg=HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度必须包括：
1. 宏观经济预期分析
2. 宏观政策预期分析
3. 利率走势预测分析
4. 投资策略建议分析
5. 市场观点分析（共识与分歧）
6. 风险与机会分析

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])}
- 主要事件: {', '.join(data_summary['unique_events'][:5])}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。每个维度的分析都要尽可能详细，并确保标注来源机构。

以下是各家机构的观点:
{viewpoints_text}
""")
messages=[system_msg,human_msg]
default_analysis=AIMessage(content="无法在规定时间内完成综合分析。")
initial_analysis=safe_llm_invoke(messages,timeout_seconds=180,default_response=default_analysis,description="多机构报告综合分析")
logger.info("开始生成结构化综合分析结果...")
system_msg2=SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构，并按照要求的维度进行详细分析。")
human_msg2=HumanMessage(content="""请以JSON格式返回分析结果，必须包含以下字段:

1. macro_economic_outlook: 宏观经济预期分析（数组格式，每项包含观点内容和来源机构）
2. policy_expectations: 宏观政策预期分析（数组格式，每项包含观点内容和来源机构）
3. interest_rate_forecast: 利率走势预测分析（数组格式，每项包含观点内容和来源机构）
4. investment_recommendations: 投资策略建议分析（数组格式，每项包含观点内容和来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）

对于每个数组项，请使用以下格式：
{
  "content": "观点内容",
  "sources": ["机构A", "机构B"]
}

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")
try:
        messages2=[system_msg2,initial_analysis,human_msg2]
default_result=AIMessage(content="""
{
  "macro_economic_outlook": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "policy_expectations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "interest_rate_forecast": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "investment_recommendations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "consensus_points": [{"content": "无法识别共识观点", "sources": []}],
  "divergent_points": [{"content": "无法识别分歧观点", "sources": []}],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": [{"content": "无法识别市场风险", "sources": []}],
  "market_opportunities": [{"content": "无法识别市场机会", "sources": []}]
}
""")
result=safe_llm_invoke(messages2,timeout_seconds=120,default_response=default_result,description="生成结构化综合分析")
content=result.content
start_pos=content.find('{')
end_pos=content.rfind('}')+1
ifstart_pos>=0andend_pos>start_pos:
            json_str=content[start_pos:end_pos]
analysis=json.loads(json_str)
analysis["reasoning_process"]=initial_analysis.content
logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points',[]))} 条共识观点, {len(analysis.get('divergent_points',[]))} 条分歧观点")
returnanalysis
else:
            logger.error("无法找到综合分析的JSON内容")
return{"macro_economic_outlook":[{"content":"无法解析综合观点","sources":[]}],"policy_expectations":[{"content":"无法解析综合观点","sources":[]}],"interest_rate_forecast":[{"content":"无法解析综合观点","sources":[]}],"investment_recommendations":[{"content":"无法解析综合观点","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"无法生成综合分析","market_risks":[],"market_opportunities":[]}
exceptExceptionase:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
return{"macro_economic_outlook":[{"content":"分析过程出错","sources":[]}],"policy_expectations":[{"content":"分析过程出错","sources":[]}],"interest_rate_forecast":[{"content":"分析过程出错","sources":[]}],"investment_recommendations":[{"content":"分析过程出错","sources":[]}],"consensus_points":[],"divergent_points":[],"overall_summary":"分析过程出错","market_risks":[],"market_opportunities":[]}
defgenerate_executive_summary(overall_analysis,reports_count):
    """生成执行摘要"""
logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")
defformat_complex_item(items,max_items=3):
        ifnotitems:
            return'无数据'
ifisinstance(items,str):
            returnitems
formatted_points=[]
count=0
foriteminitems[:max_items]:
            ifisinstance(item,dict):
                content=item.get('content','')
sources=item.get('sources',[])
ifisinstance(sources,list)andsources:
                    sources_str='、'.join(sources)
formatted_points.append(f"{content}（来源：{sources_str}）")
else:
                    formatted_points.append(content)
elifisinstance(item,str):
                formatted_points.append(item)
count+=1
ifcount>=max_items:
                break
iflen(items)>max_items:
            formatted_points.append("等")
return'；'.join(formatted_points)
system_msg=SystemMessage(content="""你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
摘要应包含以下几个关键维度的分析：宏观经济预期、宏观政策预期、利率走势预测、投资策略建议、市场观点共识与分歧、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。""")
try:
        macro_economic=format_complex_item(overall_analysis.get('macro_economic_outlook',[]))
policy_expectations=format_complex_item(overall_analysis.get('policy_expectations',[]))
interest_rate=format_complex_item(overall_analysis.get('interest_rate_forecast',[]))
investment=format_complex_item(overall_analysis.get('investment_recommendations',[]))
consensus=format_complex_item(overall_analysis.get('consensus_points',[]))
divergent=format_complex_item(overall_analysis.get('divergent_points',[]))
risks=format_complex_item(overall_analysis.get('market_risks',[]))
opportunities=format_complex_item(overall_analysis.get('market_opportunities',[]))
overall_summary=overall_analysis.get('overall_summary','无法获取整体市场观点摘要')
human_msg=HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下维度：

1. 宏观经济预期分析：{macro_economic}

2. 宏观政策预期分析：{policy_expectations}

3. 利率走势预测分析：{interest_rate}

4. 投资策略建议分析：{investment}

5. 市场观点分析：
   - 共识观点：{consensus}
   - 分歧观点：{divergent}

6. 风险与机会：
   - 主要风险：{risks}
   - 主要机会：{opportunities}

7. 整体市场观点：{overall_summary}

请生成一份不超过400字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。
摘要需要结构清晰，重点突出，便于高管快速把握市场关键信息。""")
logger.info("向LLM发送执行摘要生成请求...")
messages=[system_msg,human_msg]
default_summary="无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
result=safe_llm_invoke(messages,timeout_seconds=90,default_response=default_summary,description="生成执行摘要")
logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
returnresult.content
exceptExceptionase:
        logger.error(f"生成执行摘要时出错: {str(e)}")
traceback.print_exc()
return"无法生成执行摘要。错误原因："+str(e)
defgenerate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
days_to_fetch=ANALYSIS_CONFIG.get("max_days",3)
logger.info(f"第1步: 获取每个公众号最新报告（{days_to_fetch}天内）...")
latest_reports=get_latest_report_by_account(days=days_to_fetch)
logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
errors=[]
total=len(latest_reports)
logger.info(f"第2步: 开始并行分析 {total} 个公众号报告...")
defanalyze_single_report(biz_report_tuple):
        biz,report=biz_report_tuple
try:
            content=report['content']
ifcontentandlen(content)<ANALYSIS_CONFIG.get("min_word_count",500):
                returnbiz,None,f"{report['site_name']} 内容过短，不进行分析"
logger.info(f"开始分析公众号: {report['site_name']}")
analysis=analyze_report_with_reasoning(report['title'],content,report['site_name'])
logger.info(f"完成公众号分析: {report['site_name']}")
returnbiz,analysis,None
exceptExceptionase:
            error_msg=f"{report['site_name']} 分析失败: {str(e)}"
logger.error(error_msg)
returnbiz,None,error_msg
max_workers=max(1,min(10,total))
logger.info(f"使用线程池，工作线程数: {max_workers}")
withThreadPoolExecutor(max_workers=max_workers)asexecutor:
        results=list(executor.map(analyze_single_report,latest_reports.items()))
valid_reports_count=0
forbiz,analysis,errorinresults:
        iferror:
            errors.append(error)
elifanalysis:
            latest_reports[biz]['analysis']=analysis
valid_reports_count+=1
logger.info(f"成功分析 {valid_reports_count}/{total} 个报告")
ifvalid_reports_count==0:
        error_msg="未能成功分析任何报告，无法生成综合分析"
logger.error(error_msg)
errors.append(error_msg)
return{"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":"无法生成报告，未能成功分析任何内容。","individual_reports":{},"overall_analysis":{},"metadata":{"reports_count":0,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("第3步: 开始综合分析所有报告...")
try:
        overall_analysis=analyze_all_reports_with_storm(latest_reports)
logger.info("综合分析完成")
exceptExceptionase:
        error_msg=f"综合分析失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
overall_analysis={}
logger.info("第4步: 开始生成执行摘要...")
try:
        executive_summary=generate_executive_summary(overall_analysis,valid_reports_count)
logger.info("执行摘要生成完成")
exceptExceptionase:
        error_msg=f"执行摘要生成失败: {str(e)}"
logger.error(error_msg)
errors.append(error_msg)
executive_summary="执行摘要生成失败。"
logger.info("第5步: 组装最终报告...")
report={"generation_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"report_title":f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}","executive_summary":executive_summary,"individual_reports":latest_reports,"overall_analysis":overall_analysis,"metadata":{"reports_count":valid_reports_count,"generation_method":"STORM结构化推理","time_range":f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"},"errors":errors}
logger.info("==================== 报告生成过程完成 ====================")
returnreport
defformat_structured_list(items,content_key='content',source_key='sources'):
    lines=[]
foriteminitems:
        ifisinstance(item,dict):
            content=item.get(content_key)oritem.get('point')oritem.get('risk')oritem.get('opportunity')or''
sources=item.get(source_key)oritem.get('institutions')oritem.get('source')or[]
ifisinstance(sources,list):
                sources='、'.join(sources)
ifsources:
                lines.append(f"- {content}（来源：{sources}）")
else:
                lines.append(f"- {content}")
elifisinstance(item,str):
            lines.append(f"- {item}")
return'\n'.join(lines)
defadd_heading_numbering(md_text):
    lines=md_text.split('\n')
h1,h2,h3=0,0,0
new_lines=[]
forlineinlines:
        ifline.startswith('# '):
            h1+=1;h2=0;h3=0
new_lines.append(f"# {h1}. {line[2:]}")
elifline.startswith('## '):
            h2+=1;h3=0
new_lines.append(f"## {h1}.{h2} {line[3:]}")
elifline.startswith('### '):
            h3+=1
new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
else:
            new_lines.append(line)
return'\n'.join(new_lines)
defformat_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
logger.info("开始格式化STORM报告...")
start_time=datetime.now()
output=f"# {report['report_title']}\n\n"
output+=f"生成时间: {report['generation_time']}\n"
output+=f"分析报告数: {report['metadata']['reports_count']}\n"
output+=f"时间范围: {report['metadata']['time_range']}\n\n"
overall=report['overall_analysis']
logger.info("生成第一章：执行摘要...")
output+="## 执行摘要\n\n"
output+=f"{report['executive_summary']}\n\n"
logger.info("生成第二章：市场综合分析...")
output+="## 市场综合分析\n\n"
logger.info("生成2.1节：宏观经济预期分析...")
output+="### 宏观经济预期分析\n\n"
macro_economic=overall.get('macro_economic_outlook','')
ifisinstance(macro_economic,str):
        output+=macro_economic+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(macro_economic)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'宏观经济'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观经济观点共识与分歧。\n"
output+="\n"
logger.info("生成2.2节：宏观政策预期分析...")
output+="### 宏观政策预期分析\n\n"
policy_expectations=overall.get('policy_expectations','')
ifisinstance(policy_expectations,str):
        output+=policy_expectations+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(policy_expectations)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and'政策'inpoint.get('content','').lower():
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的宏观政策观点共识与分歧。\n"
output+="\n"
logger.info("生成2.3节：利率走势预测分析...")
output+="### 利率走势预测分析\n\n"
interest_rate=overall.get('interest_rate_forecast','')
ifisinstance(interest_rate,str):
        output+=interest_rate+"\n\n"
else:
        output+="#### 主要观点汇总\n\n"
output+=format_structured_list(interest_rate)+"\n\n"
output+="#### 观点共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('利率'inpoint.get('content','').lower()or'收益率'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的利率走势观点共识与分歧。\n"
output+="\n"
logger.info("生成2.4节：投资策略建议分析...")
output+="### 投资策略建议分析\n\n"
investment=overall.get('investment_recommendations','')
ifisinstance(investment,str):
        output+=investment+"\n\n"
else:
        output+="#### 主要投资建议汇总\n\n"
output+=format_structured_list(investment)+"\n\n"
output+="#### 建议共识与分歧\n\n"
consensus_found=False
forpointinoverall.get('consensus_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                consensus_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 共识: {point['content']} (来源: {sources})\n"
divergent_found=False
forpointinoverall.get('divergent_points',[]):
            ifisinstance(point,dict)and('投资'inpoint.get('content','').lower()or'配置'inpoint.get('content','').lower()):
                divergent_found=True
sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- 分歧: {point['content']} (来源: {sources})\n"
ifnotconsensus_foundandnotdivergent_found:
            output+="未发现明确的投资建议共识与分歧。\n"
output+="\n"
logger.info("生成2.5节：市场观点分析...")
output+="### 市场观点分析\n\n"
output+="#### 市场主流观点\n\n"
output+=f"{overall.get('overall_summary','未能提取市场主流观点。')}\n\n"
output+="#### 观点共识\n\n"
consensus_points=overall.get('consensus_points',[])
ifconsensus_points:
        forpointinconsensus_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (支持机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点共识。\n"
output+="\n"
output+="#### 观点分歧\n\n"
divergent_points=overall.get('divergent_points',[])
ifdivergent_points:
        forpointindivergent_points:
            ifisinstance(point,str):
                output+=f"- {point}\n"
elifisinstance(point,dict)and'content'inpointand'sources'inpoint:
                sources=', '.join(point['sources'])ifisinstance(point['sources'],list)elsepoint['sources']
output+=f"- {point['content']} (相关机构: {sources})\n"
else:
                output+=f"- {str(point)}\n"
else:
        output+="未发现明确的市场观点分歧。\n"
output+="\n"
logger.info("生成2.6节：风险与机会...")
output+="### 风险与机会\n\n"
output+="#### 市场主要风险\n\n"
market_risks=overall.get('market_risks',[])
ifmarket_risks:
        forriskinmarket_risks:
            ifisinstance(risk,str):
                output+=f"- {risk}\n"
elifisinstance(risk,dict):
                content=risk.get('content')orrisk.get('risk')orstr(risk)
sources=risk.get('sources')orrisk.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场风险点。\n"
output+="\n"
output+="#### 市场主要机会\n\n"
market_opportunities=overall.get('market_opportunities',[])
ifmarket_opportunities:
        foropportunityinmarket_opportunities:
            ifisinstance(opportunity,str):
                output+=f"- {opportunity}\n"
elifisinstance(opportunity,dict):
                content=opportunity.get('content')oropportunity.get('opportunity')orstr(opportunity)
sources=opportunity.get('sources')oropportunity.get('source')or[]
ifisinstance(sources,list):
                    sources='、'.join(sources)
ifsources:
                    output+=f"- {content}（提示机构：{sources}）\n"
else:
                    output+=f"- {content}\n"
else:
        output+="未发现明确的市场机会点。\n"
output+="\n"
logger.info("生成第三章：各机构观点明细...")
output+="## 各机构观点明细\n\n"
report_count=0
forbiz,report_datainreport['individual_reports'].items():
        if'analysis'notinreport_data:
            continue
analysis=report_data['analysis']
output+=f"### {report_data['site_name']}\n\n"
output+="#### 报告信息\n\n"
output+=f"**标题**: {report_data['title']}\n\n"
output+=f"**发布时间**: {report_data['publishtime']}\n\n"
output+="#### 核心观点摘要\n\n"
output+=f"{analysis.get('summary','未提供摘要')}\n\n"
output+="#### 具体观点\n\n"
core_viewpoints=analysis.get('core_viewpoints',[])
ifcore_viewpoints:
            forpointincore_viewpoints:
                output+=f"- {point}\n"
else:
            output+="未提取到核心观点。\n"
output+="\n"
output+="#### 观点依据\n\n"
supporting_evidence=analysis.get('supporting_evidence',[])
ifsupporting_evidence:
            forevidenceinsupporting_evidence:
                output+=f"- {evidence}\n"
else:
            output+="未提取到观点依据。\n"
output+="\n"
ifanalysis.get('market_impact')oranalysis.get('key_insights'):
            output+="#### 市场影响与洞见\n\n"
ifanalysis.get('market_impact'):
                output+=f"**市场影响**: {analysis.get('market_impact')}\n\n"
ifanalysis.get('key_insights'):
                output+=f"**关键洞见**: {analysis.get('key_insights')}\n\n"
output+="---\n\n"
report_count+=1
ifreport_count==0:
        output+="未能获取到任何机构的有效分析结果。\n\n"
logger.info("生成第四章：附录...")
output+="## 附录\n\n"
output+="### 分析方法说明\n\n"
output+=f"本报告采用有wiseAgent平台分析智能体生成，通过大语言模型对各金融机构公众号发布的研究报告进行自动化分析和总结。\n"
output+="分析过程包括：\n"
output+="1. 获取各公众号最新研究报告\n"
output+="2. 对每篇报告进行结构化分析，提取核心观点和依据\n"
output+="3. 综合分析所有报告，识别共识观点和分歧点\n"
output+="4. 按照宏观经济、政策预期、利率走势和投资建议等维度进行归纳\n\n"
output+="### 数据来源说明\n\n"
output+=f"- 数据时间范围: {report['metadata']['time_range']}\n"
output+=f"- 报告生成时间: {report['generation_time']}\n"
output+=f"- 分析机构列表: {', '.join([report_data.get('site_name','未知')forbiz,report_datainreport['individual_reports'].items()if'analysis'inreport_data])}\n\n"
output+="### 执行情况\n\n"
errors=report.get("errors",[])
iferrors:
        output+="本次报告生成过程中出现如下异常：\n"
forerrinerrors:
            output+=f"- {err}\n"
else:
        output+="本次报告生成过程无异常。\n"
end_time=datetime.now()
total_elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
output=add_heading_numbering(output)
returnoutput
classAgentState(TypedDict):
    """智能体状态"""
report_data:Optional[Dict]
report_text:Optional[str]
error:Optional[str]
es_index:Optional[str]
es_host:Optional[str]
es_port:Optional[int]
fetch_start_time:Optional[str]
fetch_end_time:Optional[str]
llm_model:Optional[str]
llm_api_base:Optional[str]
llm_api_key:Optional[str]
deffetch_and_analyze_storm(state:AgentState)->AgentState:
    """使用STORM方法获取数据并分析"""
try:
        logger.info("====== 智能体工作流: 开始生成金融舆情分析报告 (STORM方法) ======")
start_time=datetime.now()
es_index=state.get("es_index")
es_host=state.get("es_host")
es_port=state.get("es_port")
fetch_start_time=state.get("fetch_start_time")
fetch_end_time=state.get("fetch_end_time")
llm_model=state.get("llm_model")
llm_api_base=state.get("llm_api_base")
llm_api_key=state.get("llm_api_key")
report_data=generate_report_with_storm()
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告生成完成，耗时: {elapsed_time:.2f} 秒")
state["report_data"]=report_data
returnstate
exceptExceptionase:
        logger.error(f"生成报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"生成报告失败: {str(e)}"
returnstate
defformat_storm_report_node(state:AgentState)->AgentState:
    """格式化STORM报告"""
try:
        logger.info("====== 智能体工作流: 开始格式化报告 ======")
start_time=datetime.now()
if"report_data"instateandstate["report_data"]:
            report_text=format_storm_report(state["report_data"])
state["report_text"]=report_text
try:
                logger.info("初始化MinIO客户端...")
minio_client=Minio(endpoint=MINIO_ENDPOINT,access_key=MINIO_ACCESS_KEY,secret_key=MINIO_SECRET_KEY,secure=False)
bucket_name=MINIO_BUCKET
ifnotminio_client.bucket_exists(bucket_name):
                    minio_client.make_bucket(bucket_name)
logger.info(f"创建MinIO bucket: {bucket_name}")
else:
                    logger.info(f"MinIO bucket已存在: {bucket_name}")
report_id=str(uuid.uuid4())
timestamp=datetime.now().strftime('%Y%m%d_%H%M%S')
logger.info("开始将Markdown转换为PDF...")
try:
                    html=markdown.markdown(report_text,extensions=['tables','fenced_code'])
temp_file=tempfile.NamedTemporaryFile(suffix='.pdf',delete=False)
temp_file.close()
pdf_path=temp_file.name
try:
                        font_paths=['/System/Library/Fonts/PingFang.ttc','/System/Library/Fonts/SimSun.ttf','/usr/share/fonts/truetype/wqy/wqy-microhei.ttc','C:\\Windows\\Fonts\\simhei.ttf','C:\\Windows\\Fonts\\msyh.ttc',]
font_loaded=False
forfont_pathinfont_paths:
                            ifos.path.exists(font_path):
                                font_name=os.path.basename(font_path).split('.')[0]
pdfmetrics.registerFont(TTFont(font_name,font_path))
logger.info(f"已加载字体: {font_name} 从 {font_path}")
title_style.fontName=font_name
heading1_style.fontName=font_name
heading2_style.fontName=font_name
normal_style.fontName=font_name
font_loaded=True
break
ifnotfont_loaded:
                            logger.warning("未找到可用的中文字体，将使用默认字体")
exceptExceptionase:
                        logger.warning(f"加载字体时出错: {str(e)}")
logger.warning("将使用默认字体")
doc=SimpleDocTemplate(pdf_path,pagesize=A4,rightMargin=72,leftMargin=72,topMargin=72,bottomMargin=72)
styles=getSampleStyleSheet()
title_style=ParagraphStyle('CustomTitle',parent=styles['Heading1'],fontSize=24,spaceAfter=30,textColor=colors.HexColor('#1a5276'))
heading1_style=ParagraphStyle('CustomH1',parent=styles['Heading1'],fontSize=18,spaceAfter=20,textColor=colors.HexColor('#2874a6'))
heading2_style=ParagraphStyle('CustomH2',parent=styles['Heading2'],fontSize=16,spaceAfter=15,textColor=colors.HexColor('#3498db'))
normal_style=ParagraphStyle('CustomNormal',parent=styles['Normal'],fontSize=12,spaceAfter=12,leading=16)
story=[]
lines=html.split('\n')
current_style=normal_style
defclean_html_tags(text):
                        importre
text=text.replace('<strong>','<b>').replace('</strong>','</b>')
text=text.replace('<em>','<i>').replace('</em>','</i>')
text=re.sub(r'<[^>]+>','',text)
returntext
forlineinlines:
                        line=line.strip()
ifnotline:
                            story.append(Spacer(1,12))
continue
if'<h1>'inlineand'</h1>'inline:
                            line=re.sub(r'<h1>(.*?)</h1>',r'\1',line)
current_style=title_style
elif'<h2>'inlineand'</h2>'inline:
                            line=re.sub(r'<h2>(.*?)</h2>',r'\1',line)
current_style=heading1_style
elif'<h3>'inlineand'</h3>'inline:
                            line=re.sub(r'<h3>(.*?)</h3>',r'\1',line)
current_style=heading2_style
else:
                            current_style=normal_style
line=clean_html_tags(line)
ifline:
                            try:
                                para=Paragraph(line,current_style)
story.append(para)
story.append(Spacer(1,6))
exceptExceptionase:
                                logger.warning(f"处理段落时出错: {str(e)}, 内容: {line[:50]}...")
try:
                                    clean_line=re.sub(r'[^\w\s,.;:!?()[\]{}\'"-]','',line)
para=Paragraph(clean_line,normal_style)
story.append(para)
except:
                                    pass
try:
                        doc.build(story)
logger.info(f"PDF生成成功: {pdf_path}")
exceptExceptionase:
                        logger.error(f"生成PDF时出错: {str(e)}")
raise
withopen(pdf_path,'rb')aspdf_file:
                        pdf_bytes=pdf_file.read()
pdf_filename=f"financial_report_storm_{timestamp}_{report_id}.pdf"
pdf_object_path=f"financial_reports/{pdf_filename}"
minio_client.put_object(bucket_name=bucket_name,object_name=pdf_object_path,data=BytesIO(pdf_bytes),length=len(pdf_bytes),content_type="application/pdf")
pdf_storage_path=minio_client.presigned_get_object(bucket_name=bucket_name,object_name=pdf_object_path,expires=timedelta(days=7))
logger.info(f"PDF报告已上传到MinIO: {pdf_storage_path}")
md_filename=f"financial_report_storm_{timestamp}_{report_id}.md"
md_object_path=f"financial_reports/{md_filename}"
md_bytes=report_text.encode('utf-8')
minio_client.put_object(bucket_name=bucket_name,object_name=md_object_path,data=BytesIO(md_bytes),length=len(md_bytes),content_type="text/markdown")
try:
                        ifos.path.exists(pdf_path):
                            os.remove(pdf_path)
exceptExceptionase:
                        logger.warning(f"清理临时文件失败: {str(e)}")
exceptExceptionase:
                    logger.error(f"PDF转换失败: {str(e)}")
traceback.print_exc()
logger.info("回退到保存Markdown格式...")
filename=f"financial_report_storm_{timestamp}_{report_id}.md"
object_path=f"financial_reports/{filename}"
report_bytes=report_text.encode('utf-8')
minio_client.put_object(bucket_name=bucket_name,object_name=object_path,data=BytesIO(report_bytes),length=len(report_bytes),content_type="text/markdown")
pdf_storage_path=minio_client.presigned_get_object(bucket_name=bucket_name,object_name=object_path,expires=timedelta(days=7))
logger.info(f"Markdown报告已上传到MinIO: {pdf_storage_path}")
logger.info("初始化MongoDB连接...")
disconnect()
connect(db=DATABASE_NAME,username=MONGODB_USER,password=MONGODB_PASSWORD,authentication_source=MONGODB_AUTH_SOURCE,host=MONGODB_HOST,port=MONGODB_PORT)
logger.info("MongoDB连接已建立")
report_data=state["report_data"]
try:
                    time_range=report_data['metadata']['time_range']
days_str=time_range.split("截至")[0].strip()
days=int(''.join(filter(str.isdigit,days_str)))
end_date_str=time_range.split("截至")[1].strip()
end_time_date=datetime.strptime(end_date_str,"%Y-%m-%d")
start_time_date=end_time_date-timedelta(days=days)
logger.info(f"解析时间范围成功: {start_time_date} 至 {end_time_date}")
exceptExceptionase:
                    logger.error(f"解析时间范围出错: {str(e)}")
end_time_date=datetime.now()
start_time_date=end_time_date-timedelta(days=3)
logger.info(f"使用默认时间范围: {start_time_date} 至 {end_time_date}")
media_ids=BIZ_LIST
media_report=MediaInsightsReport(title=report_data['report_title'],content=report_text,storage_path=pdf_storage_path,report_type="financial_weixin",start_time=start_time_date,end_time=end_time_date,media_ids=media_ids)
media_report.save()
logger.info(f"报告信息已保存到MongoDB，ID: {media_report.id}")
state["storage_path"]=pdf_storage_path
state["mongodb_id"]=str(media_report.id)
exceptExceptionase:
                logger.error(f"保存报告时出错: {str(e)}")
traceback.print_exc()
state["storage_error"]=f"保存报告失败: {str(e)}"
end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"报告格式化和保存完成，耗时: {elapsed_time:.2f} 秒")
returnstate
exceptExceptionase:
        logger.error(f"格式化报告时出错: {str(e)}")
traceback.print_exc()
state["error"]=f"格式化报告失败: {str(e)}"
returnstate
workflow=StateGraph(AgentState)
workflow.add_node("fetch_and_analyze_storm",fetch_and_analyze_storm)
workflow.add_node("format_storm_report",format_storm_report_node)
workflow.add_edge("fetch_and_analyze_storm","format_storm_report")
workflow.add_edge("format_storm_report",END)
workflow.set_entry_point("fetch_and_analyze_storm")
workflow=workflow.compile()
defrun_agent(days=None):
    """运行金融舆情分析智能体"""
logger.info("====================== 开始运行金融舆情分析智能体 ======================")
start_time=datetime.now()
ifdaysisNone:
        days=ANALYSIS_CONFIG.get("max_days",3)
fetch_end_time=datetime.now()
fetch_start_time=fetch_end_time-timedelta(days=days)
initial_state={"report_data":None,"report_text":None,"error":None,"es_index":TARGET_ES_INDEX,"es_host":TARGET_ES_HOST,"es_port":TARGET_ES_PORT,"fetch_start_time":fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),"fetch_end_time":fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),"llm_model":LLM_MODEL,"llm_api_base":LLM_API_BASE,"llm_api_key":OPENAI_API_KEY,}
try:
        logger.info("启动LangGraph工作流...")
result=workflow.invoke(initial_state)
ifresult.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
return{"status":"error","message":result["error"]}
ifresult.get("report_text"):
            end_time=datetime.now()
elapsed_time=(end_time-start_time).total_seconds()
logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
return{"status":"success","report_text":result["report_text"],"report_data":result["report_data"],"storage_path":result.get("storage_path",""),"mongodb_id":result.get("mongodb_id","")}
else:
            logger.error("未能生成报告文本")
return{"status":"error","message":"未能生成报告文本"}
exceptExceptionase:
        logger.error(f"运行智能体时出错: {str(e)}")
traceback.print_exc()
return{"status":"error","message":str(e)}
if__name__=="__main__":
    result=run_agent()
ifresult["status"]=="success":
        print("报告生成成功！")
print(f"MinIO存储路径: {result.get('storage_path','未保存')}")
print(f"MongoDB ID: {result.get('mongodb_id','未保存')}")
report_filename=f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
try:
            withopen(report_filename,"w",encoding="utf-8")asf:
                f.write(result["report_text"])
print(f"报告已保存至: {report_filename}")
exceptExceptionase:
            print(f"保存报告失败: {str(e)}")
lines=result["report_text"].split("\n")
preview_lines=lines[:20]+["...","（报告内容省略）","..."]+lines[-5:]
print("\n".join(preview_lines))
else:
        print(f"报告生成失败: {result['message']}")


============================================================
文件: backend\app\agent\financialPublicOpinionReport_weixin_V5.1.py
============================================================

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""
importconcurrent.futures
importfunctools
importjson
importlogging
importos
importplatform
importsignal
importthreading
importtraceback
importuuid
importio
fromconcurrent.futuresimportThreadPoolExecutor
fromcontextlibimportcontextmanager
fromdatetimeimportdatetime,timedelta
fromtypingimportAny,Callable,Dict,List,Optional,TypedDict
fromioimportBytesIO
importmarkdown
fromreportlab.libimportcolors
fromreportlab.lib.pagesizesimportA4
fromreportlab.lib.stylesimportgetSampleStyleSheet,ParagraphStyle
fromreportlab.lib.unitsimportinch
fromreportlab.pdfbaseimportpdfmetrics
fromreportlab.pdfbase.ttfontsimportTTFont
fromreportlab.platypusimportSimpleDocTemplate,Paragraph,Spacer,Table,TableStyle
importtempfile
importpandasaspd
fromelasticsearchimportElasticsearch
fromlangchain_core.messagesimportAIMessage,HumanMessage,SystemMessage
fromlangchain_core.promptsimportChatPromptTemplate
fromlangchain_openaiimportChatOpenAI
fromlanggraph.graphimportEND,StateGraph
fromminioimportMinio
frommongoengineimportDocument,StringField,DateTimeField,ListField,ObjectIdField,connect,disconnect
frombsonimportObjectId
importre
importtime
classMediaInsightsReport(Document):
    meta={'collection':'media_insights_reports'}
_id=ObjectIdField(primary_key=True,default=lambda:ObjectId())
title=StringField(required=True)
content=StringField(required=True)
storage_path=StringField(required=True)
created_at=DateTimeField(default=datetime.now)
report_type=StringField(required=True)
start_time=DateTimeField(required=True)
end_time=DateTimeField(required=True)
media_ids=ListField(StringField(),default=list)
classTimeoutException(Exception):
    pass
deftimeout(seconds):
    defdecorator(func):
        @functools.wraps(func)
defwrapper(*args,**kwargs):
            start_time=datetime.now()
func_name=func.__name__
logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
withThreadPoolExecutor(max_workers=1)asexecutor:
                future=executor.submit(func,*args,**kwargs)
try:
                    result=future.result(timeout=seconds)
elapsed=(datetime.now()-start_time).total_seconds()
logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
returnresult
exceptconcurrent.futures.TimeoutError:
                    future.cancel()
elapsed=(datetime.now()-start_time).total_seconds()
logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
raiseTimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
returnwrapper
returndecorator
IS_UNIX=platform.system()in('Linux','Darwin')andos.name=='posix'
ifIS_UNIX:
    @contextmanager
deftimeout_context(seconds):
        defhandle_timeout(signum,frame):
            raiseTimeoutException(f"操作超时（{seconds}秒）")
original_handler=signal.getsignal(signal.SIGALRM)
signal.signal(signal.SIGALRM,handle_timeout)
try:
            signal.alarm(seconds)
yield
finally:
            signal.alarm(0)
signal.signal(signal.SIGALRM,original_handler)
else:
    @contextmanager
deftimeout_context(seconds):
        defdo_nothing():
            pass
withThreadPoolExecutor(max_workers=1)asexecutor:
            future=executor.submit(do_nothing)
try:
                future.result(timeout=0.001)
yield
exceptconcurrent.futures.TimeoutError:
                logger.warning("初始化超时上下文出错")
yield
logging.basicConfig(level=logging.INFO,format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger=logging.getLogger(__name__)
TARGET_ES_HOST="**************"
TARGET_ES_PORT=9600
TARGET_ES_INDEX="pro_mcp_data_weixin"
MONGODB_USER="wiseAgent001"
MONGODB_PASSWORD="1qaz@WSX"
DATABASE_NAME="wiseagent"
MONGODB_AUTH_SOURCE="wiseagent"
MONGODB_HOST="************"
MONGODB_PORT=37017
MINIO_ENDPOINT="**************:9002"
MINIO_ACCESS_KEY="qFHq6y3pNfboA7YBNynE"
MINIO_SECRET_KEY="P5lTULcF2IEtX47mkdmfuDpQVkYJEeL2AKEhvDRr"
MINIO_BUCKET="wiseagentfiles"
LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
BIZ_LIST=["MzA4MDY1NTUyMg==","MzA5OTY5MzM4Ng==","Mzg2Mjg1NTg3NA==","Mzg4NTEwMzA5NQ==","Mzg5MjU4MDkyMw==","MzI4NTU0NDE4Mw==","MzI5MzQxOTI0MQ==","MzIwMDI3NjM2Mg==","MzIzMjc3NTYyNQ==","Mzk0NjUwMDIxOQ==","MzkxMDYyNzExMA==","MzkzNTYzMDYxMg==","MzU3MDMwODc2MA==","MzU4NzcwNDcxOA==","MzU4ODM4NzI5Nw==","MzU5MzkzMTY3Mg==","MzUxMDk5NDgwNQ==","MzUxMDkyMzA4Mw==","MzUzMzEyODIyMA==","MzUzNDcxMDgzNg==","MzA3MTIzNDcwMg==","MzA3NjU1NTQwMA==","MzA4MzY2ODYwMw==","MzAwNzMxODYyNg==","Mzg4NDU2MDM3Mw==","MzI1NzAwODc3Nw==","MzU3MDMwODc2MA==","MzU3NTYyNTIyMQ==","MzUzMzYwODI2MA=="]
llm=ChatOpenAI(temperature=0,model=LLM_MODEL,openai_api_base=LLM_API_BASE,api_key=OPENAI_API_KEY,request_timeout=120)
MAX_DAYS=3
defsafe_llm_invoke(messages,timeout_seconds=120,default_response=None,description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
try:
        @timeout(timeout_seconds)
definvoke_with_timeout(msgs):
            returnllm.invoke(msgs)
result=invoke_with_timeout(messages)
logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
returnresult
exceptTimeoutExceptionase:
        logger.error(f"{description}超时: {str(e)}")
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
exceptExceptionase:
        logger.error(f"{description}失败: {str(e)}")
traceback.print_exc()
ifdefault_responseisnotNone:
            ifisinstance(default_response,str):
                returnAIMessage(content=default_response)
returndefault_response
raise
defget_es_scroll_data_batched(index,query_body,batch_size=1000,es_host=None,es_port=None,max_retries=3):
    """滚动查询ES数据，并以批次方式返回"""
ifnotes_host:
        es_host=TARGET_ES_HOST
ifnotes_port:
        es_port=TARGET_ES_PORT
retry_count=0
whileretry_count<max_retries:
        es=None
sid=None
try:
            es=Elasticsearch([f"{es_host}:{es_port}"],timeout=60)
ifnotes.ping():
                raiseConnectionError("无法连接到 Elasticsearch")
result=es.search(index=index,scroll='10m',body=query_body,size=batch_size,request_timeout=3600)
sid=result['_scroll_id']
scroll_size=result['hits']['total']['value']
logger.info(f"索引 {index} 总数据量: {scroll_size}")
iflen(result['hits']['hits'])>0:
                yieldresult['hits']['hits']
scroll_count=len(result['hits']['hits'])
whilescroll_count>0:
                try:
                    result=es.scroll(scroll_id=sid,scroll='10m',request_timeout=3600)
batch_data=result['hits']['hits']
scroll_count=len(batch_data)
ifscroll_count==0:
                        break
yieldbatch_data
exceptExceptionasscroll_error:
                    logger.error(f"滚动查询时出错: {str(scroll_error)}")
break
break
exceptExceptionase:

... (文件被截断，原文件共1345行) ...


============================================================
文件: backend\app\agent\core\llm.py
============================================================

importos
fromfunctoolsimportcache
fromtypingimportTypeAlias
fromapp.db.mongodbimportdb
fromapp.utils.configimportsettings
fromlangchain_ollamaimportChatOllama
fromlangchain_openaiimportChatOpenAI,OpenAIEmbeddings
fromapp.models.llmimportLLMModel,Provider
fromapp.models.embeddingimportEmbeddingModel
ModelT:TypeAlias=(ChatOpenAI)
@cache
asyncdefget_embedding_model(emb_id)->OpenAIEmbeddings:
    embedding:EmbeddingModel=awaitdb["embeddings"].find_one({"id":int(emb_id)})
embedding_name=embedding.get('embedding_name',None)
api_key=embedding.get('api_key',None)
service_url=embedding.get('service_url',None)
returnOpenAIEmbeddings(model=embedding_name,openai_api_key=api_key,openai_api_base=service_url)
@cache
asyncdefget_model(model_id,stream:bool=True)->ModelT:
    """
    获取LLM模型实例

    Args:
        stream: 是否启用流式响应，默认为True

    Returns:
        配置好的LLM模型实例
    """
llm:LLMModel=awaitdb["llms"].find_one({"id":int(model_id)})
model_name=llm.get('m_name',None)
api_key=llm.get('api_key',None)
temperature=llm.get('temperature',0.1)
service_url=llm.get('service_url',None)
returnChatOpenAI(model=model_name,temperature=temperature,openai_api_key=api_key,openai_api_base=service_url,streaming=stream,)


============================================================
文件: backend\app\agent\core\vectorstore.py
============================================================

importos
importtraceback
fromtypingimportAny,Dict,List,Optional
importpsycopg2
fromapp.utils.configimportsettings
frombsonimportObjectId
fromlangchain_community.vectorstoresimportPGVector
fromlangchain_core.documentsimportDocument
fromlangchain_openaiimportOpenAIEmbeddings
frompsycopg2importsql
frompsycopg2.extrasimportJson
fromsqlalchemy.ext.asyncioimportAsyncSession,create_async_engine
fromsqlalchemy.ormimportsessionmaker
from..core.llmimportget_embedding_model
from..db.mongodbimportdb
from..utils.logging_configimportget_logger,setup_logging
setup_logging()
logger=get_logger(__name__)
asyncdefretrieve_documents_with_sql(query:str,top_k:int=5,knowledge_base_id:Optional[str]=None,embedding_model:Optional[OpenAIEmbeddings]=None,similarity_threshold:float=0.5)->List[Dict[str,Any]]:
    """
    使用原生SQL查询直接从PostgreSQL数据库检索文档

    Args:
        query (str): 查询文本
        top_k (int): 返回的最大结果数
        knowledge_base_id (Optional[str]): 知识库ID过滤
        embedding_model (Optional[OpenAIEmbeddings]): 嵌入模型，如果为None则创建新的
        similarity_threshold (float): 相似度阈值，低于此值的结果将被过滤掉

    Returns:
        List[Dict[str, Any]]: 格式化的文档列表
    """
try:
        ifembedding_modelisNone:
            embedding_model=get_embedding_model()
logger.info(f"使用默认嵌入模型: {type(embedding_model).__name__}")
query_embedding=embedding_model.embed_query(query)
DB_VECTOR_DIMENSION=1536
iflen(query_embedding)!=DB_VECTOR_DIMENSION:
            iflen(query_embedding)<DB_VECTOR_DIMENSION:
                query_embedding=query_embedding+[0.0]*(DB_VECTOR_DIMENSION-len(query_embedding))
logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
else:
                query_embedding=query_embedding[:DB_VECTOR_DIMENSION]
logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")
logger.info(f"最终查询向量维度: {len(query_embedding)}")
conn=psycopg2.connect(host=settings.POSTGRES_HOST,port=settings.POSTGRES_PORT,database=settings.POSTGRES_DB,user=settings.POSTGRES_USER,password=settings.POSTGRES_PASSWORD)
cursor=conn.cursor()
logger.info(f"成功连接到数据库: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")
sql_query=f"""
            SELECT
                id,
                content,
                file_id,
                knowledge_base_id,
                chunk_id,
                1 - (embedding <=> %s::vector) AS similarity
            FROM
                {settings.VECTOR_DATABASE_TABLE}
            WHERE
                1=1
        """
params=[query_embedding]
ifknowledge_base_id:
            sql_query+=" AND knowledge_base_id = %s"
params.append(knowledge_base_id)
logger.info(f"使用知识库过滤: {knowledge_base_id}")
sql_query+=f" AND (1 - (embedding <=> %s::vector)) >= %s"
params.append(query_embedding)
params.append(similarity_threshold)
sql_query+="""
            ORDER BY similarity DESC
            LIMIT %s;
        """
params.append(top_k)
logger.info(f"执行向量相似度查询，查询: '{query}', top_k: {top_k}, 相似度阈值: {similarity_threshold}")
cursor.execute(sql_query,params)
results=cursor.fetchall()
documents=[]
forrowinresults:
            doc_id=row[0]
content=row[1]
file_id=row[2]iflen(row)>2elseNone
kb_id=row[3]iflen(row)>3elseNone
chunk_id=row[4]iflen(row)>4elseNone
similarity=row[5]iflen(row)>5else0.0
documents.append({"id":doc_id,"content":content,"file_id":file_id,"knowledge_base_id":kb_id,"chunk_id":chunk_id,"similarity":similarity,"distance":1.0-similarity})
logger.info(f"向量检索到 {len(documents)} 个文档，相似度阈值: {similarity_threshold}")
cursor.close()
conn.close()
returndocuments
exceptExceptionase:
        logger.error(f"向量文档检索失败: {str(e)}")
logger.error(traceback.format_exc())
return[]
asyncdefinsert_index_data(index_data,chunk,embedding_model:Optional[OpenAIEmbeddings]=None,):
    conn=None
try:
        conn=psycopg2.connect(host=settings.POSTGRES_HOST,port=settings.POSTGRES_PORT,database=settings.POSTGRES_DB,user=settings.POSTGRES_USER,password=settings.POSTGRES_PASSWORD)
ifembedding_modelisNone:
            embedding_model=get_embedding_model()
logger.info(f"使用默认嵌入模型: {type(embedding_model).__name__}")
cursor=conn.cursor()
insert_query=sql.SQL("""
            INSERT INTO knowledge_index
            (id, content, knowledge_base_id, chunk_id, embedding)
            VALUES (%s, %s, %s, %s, %s)
        """)
logger.info(f"index_data: {index_data}")
foriteminindex_data:
            id=str(ObjectId())
embedding=embedding_model.embed_query(item)
data=(id,item,str(chunk["knowledge_base_id"]),str(chunk["_id"]),embedding+[0.0]*(1536-len(embedding)))
logger.info(f"插入知识库索引: {data}")
cursor.execute(insert_query,data)
conn.commit()
logger.info("数据插入成功")
exceptExceptionase:
        logger.error(f"插入失败: {str(e)}")
logger.error(traceback.format_exc())
ifconn:conn.rollback()
finally:
        ifconn:conn.close()
asyncdefget_chunks_by_ids(ids:List[str])->List[Dict[str,Any]]:
    """
    根据给定的ID列表获取对应的文档内容

    Args:
        ids (List[str]): 文档ID列表

    Returns:
        List[Dict[str, Any]]: 文档内容列表
    """
try:
        chunks=awaitdb.knowledge_chunk.find({"_id":{"$in":[ObjectId(id)foridinids]}}).to_list(length=None)
forchunkinchunks:
            chunk["_id"]=str(chunk["_id"])
if"knowledge_base_id"inchunkandisinstance(chunk["knowledge_base_id"],ObjectId):
                chunk["knowledge_base_id"]=str(chunk["knowledge_base_id"])
logger.info(f"成功获取 {len(chunks)} 个文档块")
returnchunks
exceptExceptionase:
        logger.error(f"获取文档块失败: {str(e)}")
logger.error(traceback.format_exc())

... (文件被截断，原文件共294行) ...


============================================================
文件: backend\app\agent\schema\schema.py
============================================================

fromtypingimportAny,Literal,Optional
fromlangchain_openaiimportChatOpenAI
frompydanticimportBaseModel,Field,SerializeAsAny,validator
fromtyping_extensionsimportNotRequired,TypedDict
classAgentInfo(BaseModel):
    """Info about an available agent."""
key:str=Field(description="Agent key.",examples=["research-assistant"],)
description:str=Field(description="Description of the agent.",examples=["A research assistant for generating research papers."],)
classServiceMetadata(BaseModel):
    """Metadata about the service including available agents and models."""
agents:list[AgentInfo]=Field(description="List of available agents.",)
models:list[ChatOpenAI]=Field(description="List of available LLMs.",)
default_agent:str=Field(description="Default agent used when none is specified.",examples=["research-assistant"],)
default_model:ChatOpenAI=Field(description="Default model used when none is specified.",)
classUserInput(BaseModel):
    """Basic user input for the agent."""
message:str=Field(description="User input to the agent.",examples=["What is the weather in Tokyo?"],)
model:SerializeAsAny[ChatOpenAI]|None=Field(title="Model",description="LLM Model to use for the agent.",default='deepseek-v3',examples=['deepseek-v3'],)
thread_id:str|None=Field(description="Thread ID to persist and continue a multi-turn conversation.",default=None,examples=["847c6285-8fc9-4560-a83f-4e6285809254"],)
agent_config:dict[str,Any]=Field(description="Additional configuration to pass through to the agent",default={},examples=[{"spicy_level":0.8}],)
classStreamInput(BaseModel):
    """Input for streaming API, compatible with OpenAI format."""
message:str
thread_id:Optional[str]=None
stream_tokens:bool=True
agent_config:dict[str,Any]=Field(description="Additional configuration to pass through to the agent",default={},)
model:Optional[str]=None
temperature:Optional[float]=0
top_p:Optional[float]=1.0
n:Optional[int]=1
stream:Optional[bool]=True
max_tokens:Optional[int]=None
presence_penalty:Optional[float]=0
frequency_penalty:Optional[float]=0
@validator('message',pre=True,always=True)
defvalidate_message(cls,v,values):
        messages=values.get('messages')
ifvisNoneandmessages:
            ifisinstance(messages,list)andlen(messages)>0:
                formsginreversed(messages):
                    ifisinstance(msg,dict)andmsg.get('role')=='user':
                        returnmsg.get('content','')
returnv
classConfig:
        extra="allow"
classToolCall(TypedDict):
    """Represents a request to call a tool."""
name:str
"""The name of the tool to be called."""
args:dict[str,Any]
"""The arguments to the tool call."""
id:str|None
"""An identifier associated with the tool call."""
type:NotRequired[Literal["tool_call"]]
classChatMessage(BaseModel):
    """Message in a chat."""
type:Literal["human","ai","tool","custom"]=Field(description="Role of the message.",examples=["human","ai","tool","custom"],)
content:str=Field(description="Content of the message.",examples=["Hello, world!"],)
tool_calls:list[ToolCall]=Field(description="Tool calls in the message.",default=[],)
tool_call_id:str|None=Field(description="Tool call that this message is responding to.",default=None,examples=["call_Jja7J89XsjrOLA5r!MEOW!SL"],)
run_id:str|None=Field(description="Run ID of the message.",default=None,examples=["847c6285-8fc9-4560-a83f-4e6285809254"],)
response_metadata:dict[str,Any]=Field(description="Response metadata. For example: response headers, logprobs, token counts.",default={},)
custom_data:dict[str,Any]=Field(description="Custom message data.",default={},)
defpretty_repr(self)->str:
        """Get a pretty representation of the message."""
base_title=self.type.title()+" Message"
padded=" "+base_title+" "
sep_len=(80-len(padded))//2
sep="="*sep_len
second_sep=sep+"="iflen(padded)%2elsesep
title=f"{sep}{padded}{second_sep}"
returnf"{title}\n\n{self.content}"
defpretty_print(self)->None:
        print(self.pretty_repr())
classFeedback(BaseModel):
    """Feedback for a run, to record to LangSmith."""
run_id:str=Field(description="Run ID to record feedback for.",examples=["847c6285-8fc9-4560-a83f-4e6285809254"],)
key:str=Field(description="Feedback key.",examples=["human-feedback-stars"],)
score:float=Field(description="Feedback score.",examples=[0.8],)
kwargs:dict[str,Any]=Field(description="Additional feedback kwargs, passed to LangSmith.",default={},examples=[{"comment":"In-line human feedback"}],)
classFeedbackResponse(BaseModel):
    status:Literal["success"]="success"
classChatHistoryInput(BaseModel):
    """Input for retrieving chat history."""
thread_id:str=Field(description="Thread ID to persist and continue a multi-turn conversation.",examples=["847c6285-8fc9-4560-a83f-4e6285809254"],)
classChatHistory(BaseModel):
    messages:list[ChatMessage]


============================================================
文件: backend\app\agent\schema\task_data.py
============================================================

fromtypingimportAny,Literal
frompydanticimportBaseModel,Field
classTaskData(BaseModel):
    name:str|None=Field(description="Name of the task.",default=None,examples=["Check input safety"])
run_id:str=Field(description="ID of the task run to pair state updates to.",default="",examples=["847c6285-8fc9-4560-a83f-4e6285809254"],)
state:Literal["new","running","complete"]|None=Field(description="Current state of given task instance.",default=None,examples=["running"],)
result:Literal["success","error"]|None=Field(description="Result of given task instance.",default=None,examples=["running"],)
data:dict[str,Any]=Field(description="Additional data generated by the task.",default={},)
defcompleted(self)->bool:
        returnself.state=="complete"
defcompleted_with_error(self)->bool:
        returnself.state=="complete"andself.result=="error"
classTaskDataStatus:
    def__init__(self)->None:
        importstreamlitasst
self.status=st.status("")
self.current_task_data:dict[str,TaskData]={}
defadd_and_draw_task_data(self,task_data:TaskData)->None:
        status=self.status
status_str=f"Task **{task_data.name}** "
matchtask_data.state:
            case"new":
                status_str+="has :blue[started]. Input:"
case"running":
                status_str+="wrote:"
case"complete":
                iftask_data.result=="success":
                    status_str+=":green[completed successfully]. Output:"
else:
                    status_str+=":red[ended with error]. Output:"
status.write(status_str)
status.write(task_data.data)
status.write("---")
iftask_data.run_idnotinself.current_task_data:
            status.update(label=f"""Task: {task_data.name}""")
self.current_task_data[task_data.run_id]=task_data
ifall(entry.completed()forentryinself.current_task_data.values()):
            ifany(entry.completed_with_error()forentryinself.current_task_data.values()):
                state="error"
else:
                state="complete"
else:
            state="running"
status.update(state=state)


============================================================
文件: backend\app\agent\schema\__init__.py
============================================================

from.schemaimport(AgentInfo,ChatHistory,ChatHistoryInput,ChatMessage,Feedback,FeedbackResponse,ServiceMetadata,StreamInput,UserInput)
__all__=["AgentInfo","UserInput","ChatMessage","ServiceMetadata","StreamInput","Feedback","FeedbackResponse","ChatHistoryInput","ChatHistory",]


============================================================
文件: funTest\rag_01\backend\app\config.py
============================================================

DATA_DIR="data"
