"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[456],{85175:function(e,t,n){var r=n(1413),a=n(67294),s=n(48820),i=n(91146),o=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s.Z}))},c=a.forwardRef(o);t.Z=c},37446:function(e,t,n){n.d(t,{Z:function(){return c}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},i=n(91146),o=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var c=a.forwardRef(o)},51042:function(e,t,n){var r=n(1413),a=n(67294),s=n(42110),i=n(91146),o=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s.Z}))},c=a.forwardRef(o);t.Z=c},82867:function(e,t,n){n.r(t),n.d(t,{default:function(){return Y}});var r=n(97857),a=n.n(r),s=n(15009),i=n.n(s),o=n(19632),c=n.n(o),l=n(99289),u=n.n(l),p=n(5574),d=n.n(p),f=n(67294),x=n(71471),h=n(26058),y=n(55102),m=n(17788),g=n(8232),v=n(2453),j=n(4393),b=n(85418),Z=n(83062),k=n(83622),w=n(42075),S=n(34041),C=n(50136),z=n(11941),P=n(74330),T=n(66309),B=n(96074),E=n(41156),N=n(85175),_=n(47389),L=n(82061),I=n(37446),R=n(49354),A=n(87547),F=n(51042),q=n(77880),W=n(85893),O=x.Z.Title,M=x.Z.Text,V=h.Z.Sider,G=h.Z.Content,H=y.Z.TextArea,D=m.Z.confirm,U=y.Z.Search,Y=function(){var e=(0,f.useState)(!1),t=d()(e,2),n=t[0],r=t[1],s=g.Z.useForm(),o=d()(s,1)[0],l=(0,f.useState)(null),p=d()(l,2),Y=p[0],J=p[1],K=(0,f.useState)("all"),$=d()(K,2),Q=$[0],X=$[1],ee=(0,f.useState)(""),te=d()(ee,2),ne=te[0],re=te[1],ae=(0,f.useState)("all"),se=d()(ae,2),ie=se[0],oe=se[1],ce=(0,f.useState)(!1),le=d()(ce,2),ue=le[0],pe=le[1],de=(0,f.useState)(null),fe=d()(de,2),xe=fe[0],he=fe[1],ye=(0,f.useState)("user"),me=d()(ye,2),ge=me[0],ve=me[1],je=(0,f.useState)({user:!1,system:!1}),be=d()(je,2),Ze=be[0],ke=be[1],we=(0,f.useState)([]),Se=d()(we,2),Ce=Se[0],ze=Se[1],Pe=(0,f.useState)([]),Te=d()(Pe,2),Be=Te[0],Ee=Te[1],Ne=(0,f.useState)([]),_e=d()(Ne,2),Le=_e[0],Ie=_e[1],Re=(0,f.useState)({current:1,pageSize:10,total:0}),Ae=d()(Re,2),Fe=Ae[0],qe=Ae[1],We=(0,f.useState)({current:1,pageSize:10,total:0}),Oe=d()(We,2),Me=Oe[0],Ve=Oe[1],Ge=[{label:"全部",desc:"所有模型",key:"all"},{label:"GPT-4",desc:"LLM",key:"gpt4"},{label:"Claude 2",desc:"LLM",key:"claude2"},{label:"LLaMa 2",desc:"LLM",key:"llama2"},{label:"FinBERT",desc:"金融NLP",key:"finbert"}],He=(0,f.useState)([{label:"全部提示词",key:"all",icon:(0,W.jsx)(E.Z,{})}]),De=d()(He,2),Ue=De[0],Ye=De[1];(0,f.useEffect)((function(){var e=function(){var e=u()(i()().mark((function e(){var t,n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,q.qB)();case 3:(t=e.sent).success&&t.data&&(n=t.data.map((function(e){return{label:e,key:e}})),Ie(t.data),Ye([{label:"全部提示词",key:"all",icon:(0,W.jsx)(E.Z,{})}].concat(c()(n)))),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),v.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Je=function(){var e=u()(i()().mark((function e(t){var n,r,s,o=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]&&o[1],ke((function(e){return a()(a()({},e),{},{user:!0})})),e.prev=2,r={current:t,pageSize:Fe.pageSize,title:ne||void 0,category:"all"!==Q?Q:void 0,model:"all"!==ie?ie:void 0},e.next=6,(0,q.He)(r);case 6:(s=e.sent).success&&s.data&&(ze((function(e){return n?[].concat(c()(e),c()(s.data)):s.data})),qe(a()(a()({},Fe),{},{current:t,total:s.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取个人提示词失败",e.t0),v.ZP.error("获取个人提示词失败");case 14:return e.prev=14,ke((function(e){return a()(a()({},e),{},{user:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),Ke=function(){var e=u()(i()().mark((function e(t){var n,r,s,o=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]&&o[1],ke((function(e){return a()(a()({},e),{},{system:!0})})),e.prev=2,r={current:t,pageSize:Me.pageSize,title:ne||void 0,category:"all"!==Q?Q:void 0,model:"all"!==ie?ie:void 0},e.next=6,(0,q.V7)(r);case 6:(s=e.sent).success&&s.data&&(Ee((function(e){return n?[].concat(c()(e),c()(s.data)):s.data})),Ve(a()(a()({},Me),{},{current:t,total:s.total||0}))),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取系统提示词失败",e.t0),v.ZP.error("获取系统提示词失败");case 14:return e.prev=14,ke((function(e){return a()(a()({},e),{},{system:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(t){return e.apply(this,arguments)}}();(0,f.useEffect)((function(){Je(1),Ke(1)}),[Q,ne,ie]);var $e=function(e){J(e||null),e?o.setFieldsValue(a()(a()({},e),{},{title:e.title,content:e.content,category:Array.isArray(e.category)?e.category:[e.category],positions:e.positions,models:e.models,language:e.language||"zh-CN"})):(o.resetFields(),o.setFieldsValue({models:"all"!==ie?[ie]:["gpt4"],category:"all"!==Q?[Q]:Le.length>0?[Le[0]]:[],positions:["analyst"],language:"zh-CN"})),r(!0)},Qe=function(){var e=u()(i()().mark((function e(t){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,q.WJ)(t.id);case 3:(n=e.sent).success&&n.data&&(he(n.data),pe(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),v.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Xe=function(){var e=u()(i()().mark((function e(){var t,n,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,o.validateFields();case 3:if(t=e.sent,ke((function(e){return a()(a()({},e),{},{user:!0})})),n=a()(a()({},t),{},{category:Array.isArray(t.category)&&t.category.length>0?t.category.join(","):t.category}),!Y){e.next=12;break}return e.next=9,(0,q.Fu)(Y.id,n);case 9:s=e.sent,e.next=15;break;case 12:return e.next=14,(0,q.Fh)(n);case 14:s=e.sent;case 15:s.success?(v.ZP.success("".concat(Y?"更新":"创建","提示词成功")),r(!1),o.resetFields(),Y||qe((function(e){return a()(a()({},e),{},{current:1})})),Je(1)):v.ZP.error(s.message||"".concat(Y?"更新":"创建","提示词失败")),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("表单验证或提交失败:",e.t0),v.ZP.error("".concat(Y?"更新":"创建","提示词失败"));case 22:return e.prev=22,ke((function(e){return a()(a()({},e),{},{user:!1})})),e.finish(22);case 25:case"end":return e.stop()}}),e,null,[[0,18,22,25]])})));return function(){return e.apply(this,arguments)}}(),et=function(){var e=u()(i()().mark((function e(t){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke((function(e){return a()(a()({},e),{},{user:!0})})),e.next=4,(0,q.Yb)(t);case 4:(n=e.sent).success?(v.ZP.success("复制提示词成功"),qe((function(e){return a()(a()({},e),{},{current:1})})),Je(1)):v.ZP.error(n.message||"复制提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("复制失败:",e.t0),v.ZP.error("复制提示词失败");case 12:return e.prev=12,ke((function(e){return a()(a()({},e),{},{user:!1})})),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}(),tt=function(e,t){var n=e.is_system,r=[{key:"copy",icon:(0,W.jsx)(N.Z,{}),label:"复制",onClick:function(t){t.domEvent.stopPropagation(),et(e.id)}}];return n||t||r.unshift({key:"edit",icon:(0,W.jsx)(_.Z,{}),label:"编辑",onClick:function(t){t.domEvent.stopPropagation(),$e(e)}},{key:"delete",icon:(0,W.jsx)(L.Z,{}),label:"删除",danger:!0,onClick:function(t){var n,r;t.domEvent.stopPropagation(),n=e.id,D({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(r=u()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke((function(e){return a()(a()({},e),{},{user:!0})})),e.next=4,(0,q.$j)(n);case 4:(t=e.sent).success?(v.ZP.success("删除提示词成功"),1===Ce.length&&Fe.current>1?qe((function(e){return a()(a()({},e),{},{current:e.current-1})})):Je(1)):v.ZP.error(t.message||"删除提示词失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除失败:",e.t0),v.ZP.error("删除提示词失败");case 12:return e.prev=12,ke((function(e){return a()(a()({},e),{},{user:!1})})),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])}))),function(){return r.apply(this,arguments)})})}}),(0,W.jsxs)(j.Z,{hoverable:!0,style:{height:"100%",position:"relative",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0,0,0,0.06)",border:t?"1px solid #b7eb8f":"1px solid #adc6ff",backgroundColor:t?"#f6ffed":"#f0f5ff",transition:"all 0.3s ease"},bodyStyle:{padding:"16px",display:"flex",flexDirection:"column",height:"100%",paddingRight:"40px"},onClick:function(){return Qe(e)},className:t?"system-prompt-card":"user-prompt-card",children:[(0,W.jsx)("div",{style:{position:"absolute",top:"12px",right:"12px",zIndex:10},children:(0,W.jsx)(b.Z,{menu:{items:r},trigger:["click"],placement:"bottomRight",children:(0,W.jsx)(Z.Z,{title:"更多操作",children:(0,W.jsx)(k.ZP,{type:"text",shape:"circle",icon:(0,W.jsx)(I.Z,{style:{color:"#8c8c8c"}}),onClick:function(e){return e.stopPropagation()}})})})}),(0,W.jsxs)("div",{style:{marginBottom:"12px",display:"flex",alignItems:"center"},children:[(0,W.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"8px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:t?"#b7eb8f":"#adc6ff",color:t?"#52c41a":"#1890ff",fontSize:"20px",marginRight:"10px"},children:(0,W.jsx)(R.Z,{})}),(0,W.jsx)(O,{level:5,style:{margin:0,fontWeight:500},children:e.title})]}),(0,W.jsx)(M,{type:"secondary",style:{display:"-webkit-box",marginBottom:"12px",flex:1,overflow:"hidden",textOverflow:"ellipsis",WebkitLineClamp:3,WebkitBoxOrient:"vertical",fontSize:"13px",lineHeight:"1.6"},children:e.content}),(0,W.jsxs)("div",{style:{marginTop:"auto",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,W.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,W.jsx)(A.Z,{style:{color:"#8c8c8c",marginRight:"4px",fontSize:"12px"}}),(0,W.jsx)(M,{style:{fontSize:"12px",color:"#8c8c8c"},children:e.user})]}),(0,W.jsx)(Z.Z,{title:"复制提示词内容",children:(0,W.jsx)(k.ZP,{type:"text",icon:(0,W.jsx)(N.Z,{}),size:"small",onClick:function(t){t.stopPropagation(),navigator.clipboard.writeText(e.content),v.ZP.success("提示词内容已复制到剪贴板")}})})]})]},e.id)};return(0,W.jsxs)(h.Z,{style:{height:"100vh",overflow:"hidden",background:"#fff"},children:[(0,W.jsxs)("div",{style:{padding:"12px 24px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fff",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,W.jsx)(O,{level:4,style:{margin:0},children:"提示词模版"}),(0,W.jsxs)(w.Z,{size:"middle",children:[(0,W.jsx)(M,{type:"secondary",children:"当前模型:"}),(0,W.jsx)(S.default,{value:ie,onChange:oe,bordered:!1,style:{width:150},dropdownMatchSelectWidth:!1,children:Ge.map((function(e){return(0,W.jsx)(S.default.Option,{value:e.key,children:(0,W.jsxs)(w.Z,{children:[e.label,(0,W.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["(",e.desc,")"]})]})},e.key)}))}),(0,W.jsx)(M,{type:"secondary",children:"|"}),(0,W.jsxs)(M,{type:"secondary",children:[Ce.length+Be.length," 个提示词"]})]})]}),(0,W.jsxs)(h.Z,{style:{height:"calc(100vh - 65px)",background:"#fff"},children:[(0,W.jsx)(V,{width:200,style:{background:"#fff",overflow:"auto",height:"100%",paddingTop:"16px"},children:(0,W.jsx)(C.Z,{mode:"inline",selectedKeys:[Q],style:{height:"calc(100% - 16px)",borderRight:0,background:"transparent"},items:Ue.map((function(e){return a()(a()({},e),{},{label:(0,W.jsx)("span",{style:{fontWeight:Q===e.key?500:void 0},children:e.label})})})),onClick:function(e){var t=e.key;X(t)}})}),(0,W.jsx)(G,{style:{padding:"20px",overflow:"auto",height:"100%",background:"#fff"},children:(0,W.jsxs)(z.Z,{activeKey:ge,onChange:ve,tabBarExtraContent:(0,W.jsxs)(w.Z,{children:[(0,W.jsx)(U,{placeholder:"搜索提示词名称或描述",onSearch:function(e){re(e)},onChange:function(e){return re(e.target.value)},style:{width:300},allowClear:!0,enterButton:!0}),(0,W.jsx)(S.default,{defaultValue:"zh-CN",style:{width:100},options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}]}),"user"===ge&&(0,W.jsx)(k.ZP,{type:"primary",icon:(0,W.jsx)(F.Z,{}),onClick:function(){return $e()},children:"添加提示词"})]}),children:[(0,W.jsxs)(z.Z.TabPane,{tab:(0,W.jsxs)("span",{style:{fontSize:"16px",color:"#1890ff"},children:["我的提示词 (",Fe.total,")"]}),children:[(0,W.jsx)("div",{style:{marginBottom:"16px"},children:(0,W.jsx)(M,{type:"secondary",style:{fontSize:"12px",display:"block",marginTop:"4px"},children:"您可以创建、编辑和管理个人提示词，根据业务需求自定义提示词内容"})}),(0,W.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:"16px",marginBottom:"16px"},children:Ce.map((function(e){return tt(e,!1)}))}),Ze.user&&(0,W.jsx)("div",{style:{textAlign:"center"},children:(0,W.jsx)(P.Z,{})}),!Ze.user&&Fe.current*Fe.pageSize<Fe.total&&(0,W.jsx)("div",{style:{textAlign:"center",marginTop:"20px"},children:(0,W.jsx)(k.ZP,{onClick:function(){var e=Fe.current+1;Je(e,!0)},loading:Ze.user,children:"加载更多"})})]},"user"),(0,W.jsxs)(z.Z.TabPane,{tab:(0,W.jsxs)("span",{style:{fontSize:"16px",color:"#52c41a"},children:["系统提示词 (",Me.total,")"]}),children:[(0,W.jsx)("div",{style:{marginBottom:"16px"},children:(0,W.jsx)(M,{type:"secondary",style:{fontSize:"12px",display:"block",marginTop:"4px"},children:"系统提供的标准提示词模板，您可以查看和复制这些提示词作为参考"})}),(0,W.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:"16px",marginBottom:"16px"},children:Be.map((function(e){return tt(e,!0)}))}),Ze.system&&(0,W.jsx)("div",{style:{textAlign:"center"},children:(0,W.jsx)(P.Z,{})}),!Ze.system&&Me.current*Me.pageSize<Me.total&&(0,W.jsx)("div",{style:{textAlign:"center",marginTop:"20px"},children:(0,W.jsx)(k.ZP,{onClick:function(){var e=Me.current+1;Ke(e,!0)},loading:Ze.system,children:"加载更多"})})]},"system")]})})]}),(0,W.jsx)(m.Z,{title:Y?"编辑提示词":"新建提示词",open:n,onCancel:function(){return r(!1)},destroyOnClose:!0,footer:[(0,W.jsx)(k.ZP,{onClick:function(){return r(!1)},children:"取消"},"cancel"),(0,W.jsx)(k.ZP,{type:"primary",onClick:Xe,loading:Ze.user,children:"确定"},"submit")],width:600,children:(0,W.jsxs)(g.Z,{form:o,layout:"vertical",initialValues:{models:"all"!==ie?[ie]:["gpt4"],category:"all"!==Q?[Q]:Le.length>0?[Le[0]]:[],positions:["analyst"],language:"zh-CN"},children:[(0,W.jsx)(g.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,W.jsx)(y.Z,{placeholder:"请输入提示词名称"})}),(0,W.jsx)(g.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,W.jsx)(H,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,W.jsx)(g.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,W.jsx)(S.default,{mode:"tags",options:Le.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,W.jsx)(g.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,W.jsx)(S.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,W.jsx)(g.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,W.jsx)(S.default,{mode:"multiple",options:Ge.map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,W.jsx)(g.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,W.jsx)(S.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})})]})}),xe&&(0,W.jsxs)(m.Z,{title:(0,W.jsx)(O,{level:5,style:{margin:0},children:xe.title}),open:ue,onCancel:function(){pe(!1),he(null)},width:700,footer:[(0,W.jsx)(k.ZP,{icon:(0,W.jsx)(N.Z,{}),onClick:function(){navigator.clipboard.writeText(xe.content),v.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,W.jsx)(k.ZP,{type:"primary",onClick:function(){pe(!1),he(null)},children:"关闭"},"close")],children:[(0,W.jsx)("div",{style:{marginBottom:"20px"},children:(0,W.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,W.jsx)("div",{children:("string"==typeof xe.category?xe.category.split(","):Array.isArray(xe.category)?xe.category:[]).map((function(e){return(0,W.jsx)(T.Z,{color:"blue",children:e.trim()},e)}))})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,W.jsx)("div",{children:xe.models&&xe.models.map((function(e){var t;return(0,W.jsx)(T.Z,{color:"green",children:(null===(t=Ge.find((function(t){return t.key===e})))||void 0===t?void 0:t.label)||e},e)}))})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,W.jsx)("div",{children:xe.positions&&xe.positions.map((function(e){return(0,W.jsx)(T.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,W.jsx)("div",{children:(0,W.jsx)(T.Z,{color:"purple",children:"zh-CN"===xe.language?"中文":"en-US"===xe.language?"英文":xe.language})})]})]})}),(0,W.jsx)(B.Z,{orientation:"left",children:"提示词内容"}),(0,W.jsx)(x.Z.Paragraph,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:xe.content}),(0,W.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,W.jsxs)("div",{children:[(0,W.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(xe.created_at).toLocaleString()]}),xe.updated_at&&xe.updated_at!==xe.created_at&&(0,W.jsxs)(M,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(xe.updated_at).toLocaleString()]})]}),(0,W.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",xe.user]})]})]})]})}},77880:function(e,t,n){n.d(t,{$j:function(){return g},Fh:function(){return x},Fu:function(){return y},He:function(){return c},V7:function(){return u},WJ:function(){return d},Yb:function(){return j},qB:function(){return Z}});var r=n(15009),a=n.n(r),s=n(99289),i=n.n(s),o=n(78158);function c(e){return l.apply(this,arguments)}function l(){return(l=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/user-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return p.apply(this,arguments)}function p(){return(p=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/system-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return f.apply(this,arguments)}function f(){return(f=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return h.apply(this,arguments)}function h(){return(h=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,t){return m.apply(this,arguments)}function m(){return(m=i()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompts/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return v.apply(this,arguments)}function v(){return(v=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return b.apply(this,arguments)}function b(){return(b=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(){return k.apply(this,arguments)}function k(){return(k=i()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/prompt_categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);