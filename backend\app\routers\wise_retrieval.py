"""
检索测试API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from ..engines.retrieval.knowledge_retriever import (
    RetrievalTestRequest, 
    RetrievalTestResponse, 
    test_knowledge_retrieval
)
from ..utils.auth import verify_token
from ..utils.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/retrieval", tags=["检索测试"])

@router.post("/search", response_model=RetrievalTestResponse)
async def retrieval_test_api(
    request: RetrievalTestRequest,
    current_user: Dict[str, Any] = Depends(verify_token)
) -> RetrievalTestResponse:
    """
    检索测试接口
    
    支持三种检索模式：
    - semantic: 语义检索（向量检索）
    - fulltext: 全文检索
    - hybrid: 混合检索（语义+全文，使用RRF算法合并）
    
    可选重排序功能：
    - 传入rerank_id参数启用重排序
    """
    try:
        logger.info(f"用户 {current_user.get('user_id')} 发起检索测试 - 模式: {request.search_mode}")
        
        # 调用检索测试主函数
        result = await test_knowledge_retrieval(request)
        
        logger.info(f"检索测试完成 - 成功: {result.success}, 结果数: {result.total_results}")
        return result
        
    except Exception as e:
        logger.error(f"检索测试API失败: {e}")
        raise HTTPException(status_code=500, detail=f"检索测试失败: {str(e)}")

@router.get("/modes")
async def get_search_modes(
    current_user: Dict[str, Any] = Depends(verify_token)
) -> Dict[str, Any]:
    """
    获取支持的检索模式列表
    """
    return {
        "search_modes": [
            {
                "mode": "semantic",
                "name": "语义检索",
                "description": "基于向量相似度的语义检索"
            },
            {
                "mode": "fulltext", 
                "name": "全文检索",
                "description": "基于关键词匹配的全文检索"
            },
            {
                "mode": "hybrid",
                "name": "混合检索", 
                "description": "语义检索+全文检索，使用RRF算法合并结果"
            }
        ],
        "parameters": {
            "topk": {
                "type": "integer",
                "default": 10,
                "min": 1,
                "max": 100,
                "description": "返回结果数量"
            },
            "min_similarity": {
                "type": "float",
                "default": 0.7,
                "min": 0.0,
                "max": 1.0,
                "description": "最小相似度阈值"
            },
            "rerank_id": {
                "type": "string",
                "optional": True,
                "description": "重排序模型ID（可选）"
            }
        }
    }
