from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime  # 新增导入
from ..models.group import Group, GroupCreate, GroupResponse
from ..db.mongodb import db
from ..utils.auth import verify_token

router = APIRouter()


# 获取所有组，支持分页
@router.get("/api/groups", response_model=Dict[str, Any])
async def get_groups(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,  # 支持按名称检索
    description: Optional[str] = None,  # 支持按描述检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}

    if name:
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配，忽略大小写
    if description:
        query["description"] = {"$regex": description, "$options": "i"}  # 支持描述的模糊匹配

    groups = await db["groups"].find(query, {"_id": 0, "id": 1, "parent_id":1,"name": 1, "description": 1, "created_at": 1, "deletable": 1, "created_by": 1}).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    
    total = await db["groups"].count_documents(query)
    return {
        "data": groups,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新组
@router.post("/api/groups", response_model=GroupResponse)
async def add_group(group: GroupCreate, current_user: dict = Depends(verify_token)):
    last_group = await db["groups"].find_one(sort=[("id", -1)])
    new_id = (last_group["id"] + 1) if last_group else 1

    new_group_data = group.dict()
    new_group = {
        "id": new_id,
        "created_by": current_user["id"],
        "created_at": datetime.now(),
        "deletable": True,  # 默认可删除
        **new_group_data
    }
    await db["groups"].insert_one(new_group)
    created_group = await db["groups"].find_one({"id": new_id})
    return GroupResponse(**created_group)

# 更新组
@router.put("/api/groups/{group_id}", response_model=GroupResponse)
async def update_group(group_id: int, group: GroupCreate, current_user: dict = Depends(verify_token)):
    update_data = group.dict(exclude_unset=True)
    result = await db["groups"].update_one({"id": group_id}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Group not found")
    updated_group = await db["groups"].find_one({"id": group_id})
    return GroupResponse(**updated_group)

# 导入正确的响应模型类型
from typing import Dict, Union, Any, Optional
from pydantic import BaseModel

# 定义响应模型
class GroupDeleteResponse(BaseModel):
    success: bool
    message: str
    id: Optional[int] = None

# 删除组
@router.delete("/api/groups/{group_id}", response_model=GroupDeleteResponse)
async def delete_group(group_id: int, current_user: dict = Depends(verify_token)):
    # 检查组是否存在
    group = await db["groups"].find_one({"id": group_id})
    if not group:
        raise HTTPException(status_code=404, detail="分组不存在")
    
    # 检查是否可删除
    if not group.get("deletable", True):
        raise HTTPException(status_code=403, detail="该分组不允许删除")
    
    # 检查是否有子分组
    child_groups = await db["groups"].find({"parent_id": group_id}).to_list(length=1)
    if child_groups:
        return GroupDeleteResponse(success=False, message="该分组下有子分组，请先删除子分组")
    
    # 检查分组下是否有用户
    users = await db["users"].find({"group_id": group_id}).to_list(length=1)
    if users:
        return GroupDeleteResponse(success=False, message="该分组下有用户，请先移除用户")
    
    # 执行删除操作
    result = await db["groups"].delete_one({"id": group_id})
    if result.deleted_count == 0:
        return GroupDeleteResponse(success=False, message="分组删除失败")
    
    return GroupDeleteResponse(success=True, message="分组删除成功", id=group_id)