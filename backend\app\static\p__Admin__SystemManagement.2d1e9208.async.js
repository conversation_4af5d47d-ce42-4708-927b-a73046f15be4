"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7379],{79090:function(e,r,n){var t=n(1413),o=n(67294),a=n(15294),i=n(91146),u=function(e,r){return o.createElement(i.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(u);r.Z=s},51042:function(e,r,n){var t=n(1413),o=n(67294),a=n(42110),i=n(91146),u=function(e,r){return o.createElement(i.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(u);r.Z=s},10149:function(e,r,n){n.d(r,{Z:function(){return s}});var t=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"},i=n(91146),u=function(e,r){return o.createElement(i.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a}))};var s=o.forwardRef(u)},64317:function(e,r,n){var t=n(1413),o=n(91),a=n(22270),i=n(67294),u=n(66758),s=n(62633),c=n(85893),p=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],l=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],d=function(e,r){var n=e.fieldProps,l=e.children,d=e.params,f=e.proFieldProps,h=e.mode,m=e.valueEnum,v=e.request,P=e.showSearch,Z=e.options,g=(0,o.Z)(e,p),y=(0,i.useContext)(u.Z);return(0,c.jsx)(s.Z,(0,t.Z)((0,t.Z)({valueEnum:(0,a.h)(m),request:v,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,t.Z)({options:Z,mode:h,showSearch:P,getPopupContainer:y.getPopupContainer},n),ref:r,proFieldProps:f},g),{},{children:l}))},f=i.forwardRef((function(e,r){var n=e.fieldProps,p=e.children,d=e.params,f=e.proFieldProps,h=e.mode,m=e.valueEnum,v=e.request,P=e.options,Z=(0,o.Z)(e,l),g=(0,t.Z)({options:P,mode:h||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},n),y=(0,i.useContext)(u.Z);return(0,c.jsx)(s.Z,(0,t.Z)((0,t.Z)({valueEnum:(0,a.h)(m),request:v,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,t.Z)({getPopupContainer:y.getPopupContainer},g),ref:r,proFieldProps:f},Z),{},{children:p}))})),h=i.forwardRef(d);h.SearchSelect=f,h.displayName="ProFormComponent",r.Z=h},52688:function(e,r,n){var t=n(1413),o=n(91),a=n(67294),i=n(62633),u=n(85893),s=["fieldProps","unCheckedChildren","checkedChildren","proFieldProps"],c=a.forwardRef((function(e,r){var n=e.fieldProps,a=e.unCheckedChildren,c=e.checkedChildren,p=e.proFieldProps,l=(0,o.Z)(e,s);return(0,u.jsx)(i.Z,(0,t.Z)({valueType:"switch",fieldProps:(0,t.Z)({unCheckedChildren:a,checkedChildren:c},n),ref:r,valuePropName:"checked",proFieldProps:p,filedConfig:{valuePropName:"checked",ignoreWidth:!0,customLightMode:!0}},l))}));r.Z=c},5966:function(e,r,n){var t=n(97685),o=n(1413),a=n(91),i=n(21770),u=n(8232),s=n(55241),c=n(98423),p=n(67294),l=n(62633),d=n(85893),f=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],m="text",v=function(e){var r=(0,i.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),n=(0,t.Z)(r,2),a=n[0],c=n[1];return(0,d.jsx)(u.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(r){var n,t=r.getFieldValue(e.name||[]);return(0,d.jsx)(s.Z,(0,o.Z)((0,o.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return c(e)},content:(0,d.jsxs)("div",{style:{padding:"4px 0"},children:[null===(n=e.statusRender)||void 0===n?void 0:n.call(e,t),e.strengthText?(0,d.jsx)("div",{style:{marginTop:10},children:(0,d.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:a,children:e.children}))}})},P=function(e){var r=e.fieldProps,n=e.proFieldProps,t=(0,a.Z)(e,f);return(0,d.jsx)(l.Z,(0,o.Z)({valueType:m,fieldProps:r,filedConfig:{valueType:m},proFieldProps:n},t))};P.Password=function(e){var r=e.fieldProps,n=e.proFieldProps,i=(0,a.Z)(e,h),u=(0,p.useState)(!1),s=(0,t.Z)(u,2),f=s[0],P=s[1];return null!=r&&r.statusRender&&i.name?(0,d.jsx)(v,{name:i.name,statusRender:null==r?void 0:r.statusRender,popoverProps:null==r?void 0:r.popoverProps,strengthText:null==r?void 0:r.strengthText,open:f,onOpenChange:P,children:(0,d.jsx)("div",{children:(0,d.jsx)(l.Z,(0,o.Z)({valueType:"password",fieldProps:(0,o.Z)((0,o.Z)({},(0,c.Z)(r,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var n;null==r||null===(n=r.onBlur)||void 0===n||n.call(r,e),P(!1)},onClick:function(e){var n;null==r||null===(n=r.onClick)||void 0===n||n.call(r,e),P(!0)}}),proFieldProps:n,filedConfig:{valueType:m}},i))})}):(0,d.jsx)(l.Z,(0,o.Z)({valueType:"password",fieldProps:r,proFieldProps:n,filedConfig:{valueType:m}},i))},P.displayName="ProFormComponent",r.Z=P},83624:function(e,r,n){n.r(r),n.d(r,{default:function(){return _}});var t=n(52677),o=n.n(t),a=n(15009),i=n.n(a),u=n(99289),s=n.n(u),c=n(5574),p=n.n(c),l=n(67294),d=n(4393),f=n(34994),h=n(97131),m=n(5966),v=n(1413),P=n(91),Z=n(62633),g=n(85893),y=["fieldProps","popoverProps","proFieldProps","colors"],w=function(e,r){var n=e.fieldProps,t=e.popoverProps,o=e.proFieldProps,a=e.colors,i=(0,P.Z)(e,y);return(0,g.jsx)(Z.Z,(0,v.Z)({valueType:"color",fieldProps:(0,v.Z)({popoverProps:t,colors:a},n),ref:r,proFieldProps:o,filedConfig:{defaultProps:{width:"100%"}}},i))},x=l.forwardRef(w),b=n(64317),k=n(52688),T=n(2453),C=n(83622),j=n(11550),F=n(79090),E=n(51042),N=n(10149),S=n(78404),L=n(69044),R=n(67610),_=function(){var e=f.A.useForm(),r=p()(e,1)[0],n=(0,l.useState)(!1),t=p()(n,2),a=t[0],u=t[1];(0,l.useEffect)((function(){var e=(0,S.kH)();r.setFieldsValue({title:e.title,colorPrimary:e.colorPrimary,logo:e.logo,navTheme:e.navTheme||"light",enable_coe:e.enable_coe||!1})}),[r]);var c=function(){var e=s()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n={logo:R.Z.logo,title:R.Z.title,colorPrimary:R.Z.colorPrimary,navTheme:R.Z.navTheme||"light",enable_coe:R.Z.enable_coe},e.next=4,(0,L.w1)(n);case 4:(t=e.sent).success&&((0,S.IZ)(t.data||{}),r.setFieldsValue(n),T.ZP.success("已重置为默认配置")),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),T.ZP.error("重置失败，请重试");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}(),v=function(){var e=s()(i()().mark((function e(r){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n="object"===o()(r.colorPrimary)?r.colorPrimary.toHexString():r.colorPrimary,e.next=4,(0,L.w1)({logo:r.logo||R.Z.logo,title:r.title||R.Z.title,colorPrimary:n||R.Z.colorPrimary,navTheme:r.navTheme||"light",enable_coe:r.enable_coe||R.Z.enable_coe});case 4:(t=e.sent).success&&((0,S.IZ)(t.data||{}),T.ZP.success("系统设置更新成功"),window.location.reload()),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),T.ZP.error("更新失败，请重试");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(r){return e.apply(this,arguments)}}(),P=function(){var e=s()(i()().mark((function e(n){var t,a,s,c;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("uploading"!==n.file.status){e.next=3;break}return u(!0),e.abrupt("return");case 3:if("done"!==n.file.status){e.next=23;break}return e.prev=4,t=n.file.response.url,r.setFieldsValue({logo:t}),e.next=9,r.validateFields();case 9:return a=e.sent,s="object"===o()(a.colorPrimary)?a.colorPrimary.toHexString():a.colorPrimary,e.next=13,(0,L.w1)({logo:t,title:a.title||R.Z.title,colorPrimary:s||R.Z.colorPrimary,navTheme:a.navTheme||"light",enable_coe:a.enable_coe});case 13:(c=e.sent).success&&((0,S.IZ)(c.data||{}),T.ZP.success("Logo更新成功")),u(!1),e.next=23;break;case 18:e.prev=18,e.t0=e.catch(4),console.error("Error updating logo:",e.t0),T.ZP.error("Logo更新失败"),u(!1);case 23:"error"===n.file.status&&(T.ZP.error("Logo上传失败"),u(!1));case 24:case"end":return e.stop()}}),e,null,[[4,18]])})));return function(r){return e.apply(this,arguments)}}(),Z=(0,g.jsxs)("div",{children:[a?(0,g.jsx)(F.Z,{}):(0,g.jsx)(E.Z,{}),(0,g.jsx)("div",{style:{marginTop:8},children:"上传Logo"})]}),y=function(){return(0,S.kH)().logo||"/static/logo.png"};return(0,g.jsx)(h._z,{children:(0,g.jsx)(d.Z,{children:(0,g.jsxs)(f.A,{form:r,layout:"vertical",onFinish:v,submitter:{render:function(e){return[(0,g.jsx)(C.ZP,{icon:(0,g.jsx)(N.Z,{}),onClick:c,style:{marginRight:8},children:"重置默认"},"reset"),(0,g.jsx)(C.ZP,{type:"primary",onClick:function(){var r;return null===(r=e.form)||void 0===r?void 0:r.submit()},children:"保存设置"},"submit")]}},children:[(0,g.jsx)(m.Z,{width:"md",name:"title",label:"系统名称",disabled:(0,S.kH)().name_locked,tooltip:"如果锁定说明系统名称被强制设置",rules:[{required:!0,message:"请输入系统名称!"}]}),(0,g.jsx)(x,{width:"md",name:"colorPrimary",label:"系统主题色",rules:[{required:!0,message:"请选择系统主题色!"}]}),(0,g.jsx)(b.Z,{width:"md",name:"navTheme",label:"导航主题",initialValue:"light",options:[{label:"亮色主题",value:"light"},{label:"暗色主题",value:"realDark"}],rules:[{required:!0,message:"请选择导航主题!"}]}),(0,g.jsx)(k.Z,{width:"md",name:"enable_coe",label:"是否启用COE"}),(0,g.jsx)(f.A.Item,{label:"系统Logo",name:"logo",children:(0,g.jsx)(j.Z,{name:"files",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,action:"/api/uploadLogo",beforeUpload:function(e){return"image/jpeg"===e.type||"image/png"===e.type?!!(e.size/1024/1024<2)||(T.ZP.error("图片大小不能超过 2MB!"),!1):(T.ZP.error("只能上传 JPG/PNG 格式的图片!"),!1)},onChange:P,children:y()?(0,g.jsx)("img",{src:y(),alt:"logo",style:{width:"100%",height:"100%",objectFit:"contain"}}):Z})})]})})})}},69044:function(e,r,n){n.d(r,{CW:function(){return I},F3:function(){return N},Nq:function(){return v},Rd:function(){return O},Rf:function(){return d},Rp:function(){return k},_d:function(){return L},az:function(){return y},cY:function(){return V},cn:function(){return h},h8:function(){return Z},iE:function(){return F},jA:function(){return x},mD:function(){return C},ul:function(){return _},w1:function(){return D},wG:function(){return M}});var t=n(5574),o=n.n(t),a=n(97857),i=n.n(a),u=n(15009),s=n.n(u),c=n(99289),p=n.n(c),l=n(78158);function d(e){return f.apply(this,arguments)}function f(){return(f=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return m.apply(this,arguments)}function m(){return(m=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return P.apply(this,arguments)}function P(){return(P=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return g.apply(this,arguments)}function g(){return(g=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,r){return w.apply(this,arguments)}function w(){return(w=p()(s()().mark((function e(r,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/users/changeStatus",{method:"POST",data:{id:r,status:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return b.apply(this,arguments)}function b(){return(b=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return T.apply(this,arguments)}function T(){return(T=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e){return j.apply(this,arguments)}function j(){return(j=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(e,r){return E.apply(this,arguments)}function E(){return(E=p()(s()().mark((function e(r,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/groups/".concat(r),i()({method:"DELETE"},n)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return S.apply(this,arguments)}function S(){return(S=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return R.apply(this,arguments)}function R(){return(R=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e,r){return q.apply(this,arguments)}function q(){return(q=p()(s()().mark((function e(r,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return G.apply(this,arguments)}function G(){return(G=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return U.apply(this,arguments)}function U(){return(U=p()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return A.apply(this,arguments)}function A(){return(A=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/roles/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return H.apply(this,arguments)}function H(){return(H=p()(s()().mark((function e(r){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new URLSearchParams,Object.entries(r).forEach((function(e){var r=o()(e,2),t=r[0],a=r[1];n.append(t,String(a))})),e.abrupt("return",(0,l.N)("/api/system/config?".concat(n.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return z.apply(this,arguments)}function z(){return(z=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,l.N)("/api/useActiveCases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);