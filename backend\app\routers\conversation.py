from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.conversation import ConversationCreate, ConversationUpdate, ConversationResponse, ConversationMessageResponse
from ..models.message import MessageResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from bson import ObjectId
from pydantic import BaseModel


from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["conversations"]
)

# 创建新对话
@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(conversation: ConversationCreate, current_user: dict = Depends(verify_token)):
    new_conversation = conversation.dict()
    new_conversation.update({
        "created_at": datetime.now(),
        "active_at": datetime.now(),
        "user_id": current_user["id"],
        "user_name": current_user["name"],
        "pinned_at": None,
        "knowledge_ids": new_conversation.get("knowledge_ids", [])  # 确保知识库字段被初始化
    })

    result = await db["conversations"].insert_one(new_conversation)
    created_conversation = await db["conversations"].find_one({"_id": result.inserted_id})
    created_conversation["id"] = str(created_conversation["_id"])  # 转换 ObjectId 为字符串
    created_conversation["active_at"] = created_conversation["active_at"].strftime("%Y-%m-%d %H:%M:%S") if created_conversation["active_at"] else None
    created_conversation["created_at"] = created_conversation["created_at"].strftime("%Y-%m-%d %H:%M:%S") if created_conversation["created_at"] else None
    created_conversation["pinned_at"] = created_conversation["pinned_at"].strftime("%Y-%m-%d %H:%M:%S") if created_conversation["pinned_at"] else None
    del created_conversation["_id"]  # 删除原始 _id
    return ConversationResponse(**created_conversation)

# 获取对话列表
@router.get("/conversations", response_model=Dict[str, Any])
async def read_conversations(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    user_name: Optional[str] = None,
    app_info: Optional[str] = None,
    conversation_name: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"user_id": current_user["id"]}
    if user_name:
        query["user_name"] = {"$regex": user_name, "$options": "i"}  # 
    if app_info:
        query["app_info"] = app_info
    if conversation_name:
        query["conversation_name"] = {"$regex": conversation_name, "$options": "i"}  # 支持描述的模糊匹配

    conversations = await db["conversations"].find(query,{
        "conversation_name": 1,
        "user_id": 1,
        "user_name": 1,
        "app_info": 1,
        "created_at": 1,
        "pinned_at": 1,
        "pinned": 1,
        "active_at": 1
    }).sort("pinned_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["conversations"].count_documents(query)

    for conv in conversations:
        conv["id"] = str(conv["_id"])
        conv["active_at"] = conv["active_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["active_at"] else None
        conv["created_at"] = conv["created_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["created_at"] else None
        conv["pinned_at"] = conv["pinned_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["pinned_at"] else None
        del conv["_id"]

    return {
        "data": conversations,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个对话及其消息
@router.get("/conversations/{conversation_id}", response_model=Dict[str, Any])
async def read_conversation(conversation_id: str, current_user: dict = Depends(verify_token)):
    # 获取对话信息
    conversation = await db["conversations"].find_one({"_id": ObjectId(conversation_id)})
    if conversation is None:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    # 确保所有必需字段都存在
    conversation["id"] = str(conversation["_id"])
    del conversation["_id"]

    # 获取与该对话相关的所有消息
    messages = await db["messages"].find({"conversation_id": conversation_id}).to_list(None)
    for msg in messages:
        msg["id"] = str(msg["_id"])
        del msg["_id"]
        # msg["active_at"] = msg["active_at"].strftime("%Y-%m-%d %H:%M:%S") if msg["active_at"] else None
        # msg["pinned_at"] = msg["pinned_at"].strftime("%Y-%m-%d %H:%M:%S") if msg["pinned_at"] else None
        # msg["created_at"] = msg["created_at"].strftime("%Y-%m-%d %H:%M:%S") if msg["created_at"] else None


    return {
        "success": True,
        "conversation": ConversationResponse(**conversation),
        "messages": [MessageResponse(**msg) for msg in messages]
    }

# 更新对话
@router.put("/conversations/{conversation_id}",   response_model=Dict[str, Any])
async def update_conversation(conversation_id: str, conversation: ConversationUpdate, current_user: dict = Depends(verify_token)):
    update_data = conversation.dict(exclude_unset=True)
    update_data_set = {}

    # 检查 active_at 是否存在且不为 None
    if "active_at" in update_data and update_data["active_at"] is not None:
        update_data_set["active_at"] = datetime.strptime(update_data["active_at"], "%Y-%m-%d %H:%M:%S")
    else:
        update_data_set["active_at"] = datetime.now()

    # 检查 pinned_at 是否存在且不为 None
    if "pinned_at" in update_data and update_data["pinned_at"] is not None:
        update_data_set["pinned_at"] = datetime.strptime(update_data["pinned_at"], "%Y-%m-%d %H:%M:%S")
    else:
        update_data_set["pinned_at"] = None

    if "conversation_name" in update_data and update_data["conversation_name"] is not None:
        update_data_set["conversation_name"] = update_data["conversation_name"]

    if "pinned" in update_data and update_data["pinned"] is not None:
        update_data_set["pinned"] = update_data["pinned"]

    result = await db["conversations"].update_one({"_id": ObjectId(conversation_id)}, {"$set": update_data_set})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    updated_conversation = await db["conversations"].find_one({"_id": ObjectId(conversation_id)})
    updated_conversation["id"] = str(updated_conversation["_id"])
    del updated_conversation["_id"]
    # return ConversationResponse(**updated_conversation)
    return {
        "success": True,
        "conversation": updated_conversation
    }

# 清空对话
@router.put("/clearConversation/{conversation_id}", response_model=Dict[str, Any])
async def clear_conversation(conversation_id: str, current_user: dict = Depends(verify_token)):
    try:
        result = await db["messages"].delete_many({"conversation_id": conversation_id})
        return {"success": True, "message": f"Cleared {result.deleted_count} messages from conversation {conversation_id}"}
    except Exception as e:
        return {"success": False, "message": str(e)}

@router.put("/conversations/{conversation_id}/active", response_model=Dict[str, Any])
async def conversation_active(
    conversation_id: str, 
    update_data: ConversationUpdate, 
    current_user: dict = Depends(verify_token)
):
    """
    将对话设为活跃并返回消息列表，同时返回knowledge_ids和对话上传的文件信息
    """
    try:
        # 处理可能为None的字段
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}
        
        # 更新active_at
        if "active_at" in update_dict:
            update_dict["active_at"] = datetime.now()
            
        # 更新数据库
        if update_dict:
            result = await db["conversations"].update_one(
                {"_id": ObjectId(conversation_id)},
                {"$set": update_dict}
            )
            
        # 获取更新后的对话
        conversation = await db["conversations"].find_one({"_id": ObjectId(conversation_id)})
        
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
            
        # 获取对话的消息
        messages = await db["messages"].find(
            {"conversation_id": conversation_id}
        ).sort("created_at", 1).to_list(1000)
        
        # 添加id字段，并转换_id为字符串
        for message in messages:
            message["id"] = str(message["_id"])
            message["_id"] = str(message["_id"])
            
        # 获取对话的knowledge_ids
        knowledge_ids = conversation.get("knowledge_ids", [])
        
        # 获取对话上传的文件信息
        uploaded_files = await db["source_files"].find({
            "knowledge_base_id": ObjectId(conversation_id),
            # "forbidden":False
            # "files_source_type": "chat"
        }).to_list(1000)
        logger.info(uploaded_files) # 685ab9c63063a25a51fd02ae
        
        # 处理文件信息
        files_info = []
        for file in uploaded_files:
            file_info = {
                "id": str(file["_id"]),
                "filename": file.get("filename", "未命名文件"),
                "data_type": file.get("data_type", "unknown"),
                "size": file.get("size", 0),
                "processing_status": file.get("processing_status", "pending"),
                "created_at": file["created_at"].strftime("%Y-%m-%d %H:%M:%S") if file.get("created_at") else None,
                "chunk_count": file.get("chunk_count", 0),
                "url": file.get("url", "")
            }
            files_info.append(file_info)
        
            
        return {
            "success": True, 
            "data": {
                "messages": messages,
                "knowledge_ids": knowledge_ids,
                "uploaded_files": files_info
            }
        }
    except Exception as e:
        logger.error(f"Error in conversation_active: {str(e)}")
        return {"success": False, "detail": str(e)}

# 删除对话
@router.delete("/conversations/{conversation_id}", response_model=Dict[str, Any])
async def delete_conversation(conversation_id: str, current_user: dict = Depends(verify_token)):
    result = await db["conversations"].delete_one({"_id": ObjectId(conversation_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Conversation not found")
    return {"success": True, "id": conversation_id}

@router.get("/myConversations", response_model=Dict[str, Any])
async def read_conversations(
    user_id: int,
    app_info: str,
    current_user: dict = Depends(verify_token)
):
    query = {"user_id": user_id, "app_info": app_info}
    conversations = await db["conversations"].find(query).to_list(length=None)
    total = await db["conversations"].count_documents(query)

    # 将 ObjectId 转换为字符串
    for conv in conversations:
        conv["id"] = str(conv["_id"])
        del conv["_id"]  # 删除原始 _id
        conv["active_at"] = conv["active_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["active_at"] else None
        conv["pinned_at"] = conv["pinned_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["pinned_at"] else None
        conv["created_at"] = conv["created_at"].strftime("%Y-%m-%d %H:%M:%S") if conv["created_at"] else None

    return {
        "data": conversations,
        "total": total,
        "success": True,
    }

# 新增模型用于更新知识库IDs
class KnowledgeIdsUpdate(BaseModel):
    knowledge_ids: List[str]

# 专门用于更新对话的知识库IDs
@router.put("/conversations/{conversation_id}/knowledge_ids", response_model=Dict[str, Any])
async def update_conversation_knowledge_ids(
    conversation_id: str, 
    knowledge_ids_update: KnowledgeIdsUpdate, 
    current_user: dict = Depends(verify_token)
):
    try:
        # 更新对话的知识库IDs
        result = await db["conversations"].update_one(
            {"_id": ObjectId(conversation_id)}, 
            {"$set": {"knowledge_ids": knowledge_ids_update.knowledge_ids}}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Conversation not found")
            
        # 获取更新后的对话信息
        updated_conversation = await db["conversations"].find_one({"_id": ObjectId(conversation_id)})
        if updated_conversation is None:
            raise HTTPException(status_code=404, detail="Conversation not found after update")
            
        # 格式化返回数据
        updated_conversation["id"] = str(updated_conversation["_id"])
        del updated_conversation["_id"]
        
        # 时间格式化
        updated_conversation["active_at"] = updated_conversation["active_at"].strftime("%Y-%m-%d %H:%M:%S") if updated_conversation.get("active_at") else None
        updated_conversation["pinned_at"] = updated_conversation["pinned_at"].strftime("%Y-%m-%d %H:%M:%S") if updated_conversation.get("pinned_at") else None
        updated_conversation["created_at"] = updated_conversation["created_at"].strftime("%Y-%m-%d %H:%M:%S") if updated_conversation.get("created_at") else None
        
        return {
            "success": True,
            "message": "Knowledge IDs updated successfully",
            "conversation": updated_conversation
        }
    except Exception as e:
        logger.error(f"Error updating knowledge IDs: {str(e)}")
        return {"success": False, "message": str(e)}
