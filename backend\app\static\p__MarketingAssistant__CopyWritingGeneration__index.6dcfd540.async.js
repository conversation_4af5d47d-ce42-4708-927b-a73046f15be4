"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3896],{83518:function(e,o,r){r.r(o);var n=r(15009),t=r.n(n),l=r(99289),a=r.n(l),c=r(19632),i=r.n(c),s=r(5574),d=r.n(s),u=r(67294),p=r(55102),g=r(2453),f=r(4393),b=r(66309),h=r(83622),m=r(32983),x=r(97131),C=r(85893),y=p.Z.TextArea;o.default=function(){var e=(0,u.useState)(""),o=d()(e,2),r=o[0],n=o[1],l=(0,u.useState)(10),c=d()(l,2),s=c[0],v=c[1],k=(0,u.useState)([]),S=d()(k,2),$=S[0],j=S[1],O=(0,u.useState)(""),w=d()(O,2),B=w[0],P=w[1],I=(0,u.useState)(!1),E=d()(I,2),N=E[0],T=E[1],Z=function(){var e=a()(t()().mark((function e(){return t()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.trim()){e.next=3;break}return g.ZP.warning("请输入文案要求"),e.abrupt("return");case 3:return T(!0),e.prev=4,e.next=7,new Promise((function(e){return setTimeout(e,2e3)}));case 7:P('基于您的要求"'.concat(r,'"生成的').concat(s,"字文案内容。这是一个示例文案，展示了如何根据用户输入的要求和选择的标签来生成相应的营销文案内容。")),g.ZP.success("文案生成成功"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),g.ZP.error("生成失败，请重试");case 14:return e.prev=14,T(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])})));return function(){return e.apply(this,arguments)}}();return(0,C.jsx)(x._z,{children:(0,C.jsxs)("div",{style:{maxWidth:1200,margin:"0 auto",padding:"20px"},children:[(0,C.jsxs)(f.Z,{title:"输入要求内容",style:{marginBottom:"24px"},headStyle:{backgroundColor:"#fafafa",borderBottom:"1px solid #f0f0f0",fontSize:"16px",fontWeight:"bold"},children:[(0,C.jsx)("div",{style:{marginBottom:"16px"},children:(0,C.jsx)("p",{style:{margin:"0 0 8px 0",color:"#333"},children:"文案生成是通过用户发布需求来生成对应的营销文案，它能够帮助您快速生成，清晰的文本，希望这样开发效率，同时保持内容的一致性和生成。"})}),(0,C.jsx)(y,{placeholder:"请输入您的文案要求...",value:r,onChange:function(e){return n(e.target.value)},rows:6,style:{marginBottom:"16px",border:"2px solid #ff7875",borderRadius:"6px"}}),(0,C.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"16px",marginBottom:"16px"},children:(0,C.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,C.jsx)("span",{style:{color:"#333",fontWeight:"bold"},children:"* 输出数量"}),(0,C.jsx)(p.Z,{type:"number",value:s,onChange:function(e){return v(Number(e.target.value))},style:{width:"80px"},min:1,max:1e3}),(0,C.jsx)("span",{style:{color:"#666"},children:"篇"})]})}),(0,C.jsx)("div",{style:{display:"flex",gap:"8px",marginBottom:"16px"},children:["输入内容","产品推广","社交媒体","营销宣传"].map((function(e){return(0,C.jsx)(b.Z,{onClick:function(){return function(e){$.includes(e)?j($.filter((function(o){return o!==e}))):j([].concat(i()($),[e]))}(e)},style:{cursor:"pointer",padding:"4px 12px",border:$.includes(e)?"1px solid #1890ff":"1px solid #d9d9d9",backgroundColor:$.includes(e)?"#e6f7ff":"#fff",color:$.includes(e)?"#1890ff":"#666"},children:e},e)}))}),(0,C.jsx)(h.ZP,{type:"primary",onClick:Z,loading:N,style:{backgroundColor:"#ff4d4f",borderColor:"#ff4d4f",height:"40px",fontSize:"16px",fontWeight:"bold"},children:"开始生成"})]}),(0,C.jsx)(f.Z,{title:"生成结果",style:{marginBottom:"24px"},headStyle:{backgroundColor:"#fafafa",borderBottom:"1px solid #f0f0f0",fontSize:"16px",fontWeight:"bold"},children:B?(0,C.jsx)("div",{style:{padding:"16px",backgroundColor:"#fafafa",borderRadius:"6px",minHeight:"200px",whiteSpace:"pre-wrap"},children:B}):(0,C.jsx)(m.Z,{description:"No Data",style:{padding:"40px 0",color:"#999"}})})]})})}},66309:function(e,o,r){r.d(o,{Z:function(){return I}});var n=r(67294),t=r(93967),l=r.n(t),a=r(98423),c=r(98787),i=r(69760),s=r(96159),d=r(45353),u=r(53124),p=r(11568),g=r(15063),f=r(14747),b=r(83262),h=r(83559);const m=e=>{const{lineWidth:o,fontSizeIcon:r,calc:n}=e,t=e.fontSizeSM;return(0,b.IX)(e,{tagFontSize:t,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(t).equal()),tagIconSize:n(r).sub(n(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,h.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:n,componentCls:t,calc:l}=e,a=l(n).sub(r).equal(),c=l(o).sub(r).equal();return{[t]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${t}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${t}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${t}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${t}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${t}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),x),y=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const v=n.forwardRef(((e,o)=>{const{prefixCls:r,style:t,className:a,checked:c,onChange:i,onClick:s}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:g}=n.useContext(u.E_),f=p("tag",r),[b,h,m]=C(f),x=l()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:c},null==g?void 0:g.className,a,h,m);return b(n.createElement("span",Object.assign({},d,{ref:o,style:Object.assign(Object.assign({},t),null==g?void 0:g.style),className:x,onClick:e=>{null==i||i(!c),null==s||s(e)}})))}));var k=v,S=r(98719);var $=(0,h.bk)(["Tag","preset"],(e=>(e=>(0,S.Z)(e,((o,r)=>{let{textColor:n,lightBorderColor:t,lightColor:l,darkColor:a}=r;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:n,background:l,borderColor:t,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),x);const j=(e,o,r)=>{const n="string"!=typeof(t=r)?t:t.charAt(0).toUpperCase()+t.slice(1);var t;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,h.bk)(["Tag","status"],(e=>{const o=m(e);return[j(o,"success","Success"),j(o,"processing","Info"),j(o,"error","Error"),j(o,"warning","Warning")]}),x),w=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const B=n.forwardRef(((e,o)=>{const{prefixCls:r,className:t,rootClassName:p,style:g,children:f,icon:b,color:h,onClose:m,bordered:x=!0,visible:y}=e,v=w(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:S,tag:j}=n.useContext(u.E_),[B,P]=n.useState(!0),I=(0,a.Z)(v,["closeIcon","closable"]);n.useEffect((()=>{void 0!==y&&P(y)}),[y]);const E=(0,c.o2)(h),N=(0,c.yT)(h),T=E||N,Z=Object.assign(Object.assign({backgroundColor:h&&!T?h:void 0},null==j?void 0:j.style),g),z=k("tag",r),[H,W,R]=C(z),_=l()(z,null==j?void 0:j.className,{[`${z}-${h}`]:T,[`${z}-has-color`]:h&&!T,[`${z}-hidden`]:!B,[`${z}-rtl`]:"rtl"===S,[`${z}-borderless`]:!x},t,p,W,R),F=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||P(!1)},[,L]=(0,i.Z)((0,i.w)(e),(0,i.w)(j),{closable:!1,closeIconRender:e=>{const o=n.createElement("span",{className:`${z}-close-icon`,onClick:F},e);return(0,s.wm)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),F(o)},className:l()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),M="function"==typeof v.onClick||f&&"a"===f.type,q=b||null,A=q?n.createElement(n.Fragment,null,q,f&&n.createElement("span",null,f)):f,D=n.createElement("span",Object.assign({},I,{ref:o,className:_,style:Z}),A,L,E&&n.createElement($,{key:"preset",prefixCls:z}),N&&n.createElement(O,{key:"status",prefixCls:z}));return H(M?n.createElement(d.Z,{component:"Tag"},D):D)})),P=B;P.CheckableTag=k;var I=P}}]);