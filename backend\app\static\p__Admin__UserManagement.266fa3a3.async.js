"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5340],{51042:function(e,r,n){var t=n(1413),a=n(67294),o=n(42110),c=n(91146),i=function(e,r){return a.createElement(c.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:o.Z}))},u=a.forwardRef(i);r.Z=u},13901:function(e,r,n){n.r(r),n.d(r,{default:function(){return P}});var t=n(97857),a=n.n(t),o=n(15009),c=n.n(o),i=n(19632),u=n.n(i),s=n(99289),l=n.n(s),p=n(5574),d=n.n(p),f=n(67294),h=n(97131),m=n(12453),v=n(8232),g=n(2453),b=n(66309),y=n(83622),x=n(17788),k=n(55102),w=n(34041),C=n(51042),j=n(69044),Z=n(85893),S=function(e){var r=v.Z.useForm(),n=d()(r,1)[0],t=e.modalVisible,o=e.onCancel,i=e.onSubmit,u=e.values,s=(0,f.useState)([]),p=d()(s,2),h=p[0],m=p[1],g=(0,f.useState)([]),b=d()(g,2),y=b[0],C=b[1],S=(0,f.useState)(!0),P=d()(S,2),O=P[0],T=P[1];return(0,f.useEffect)((function(){var e=function(){var e=l()(c()().mark((function e(){var r,n,t,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return T(!0),e.prev=1,e.prev=2,e.next=5,(0,j.F3)();case 5:r=e.sent,(n=r)&&Array.isArray(n)?m(n):n&&n.data&&Array.isArray(n.data)&&m(n.data),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("获取角色失败:",e.t0);case 13:return e.prev=13,e.next=16,(0,j.jA)();case 16:t=e.sent,(a=t)&&Array.isArray(a)?C(a):a&&a.data&&Array.isArray(a.data)&&C(a.data),e.next=24;break;case 21:e.prev=21,e.t1=e.catch(13),console.error("获取组织失败:",e.t1);case 24:e.next=29;break;case 26:e.prev=26,e.t2=e.catch(1),console.error("获取角色或组织失败",e.t2);case 29:return e.prev=29,T(!1),e.finish(29);case 32:case"end":return e.stop()}}),e,null,[[1,26,29,32],[2,10],[13,21]])})));return function(){return e.apply(this,arguments)}}();t&&e()}),[t]),(0,f.useEffect)((function(){t&&u&&!O&&(console.log("设置表单初始值:",{id:u.id,name:u.name,phone:u.phone,role_id:u.role_id,group_id:u.group_id}),n.setFieldsValue({id:u.id,name:u.name,phone:u.phone,role_id:u.role_id,group_id:u.group_id}))}),[t,u,n,O,h,y]),(0,Z.jsx)(x.Z,{destroyOnClose:!0,title:"编辑用户",visible:t,onCancel:function(){return o(!1,u)},onOk:l()(c()().mark((function e(){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.validateFields();case 2:r=e.sent,console.log("提交表单数据:",r),i(a()(a()({},u),r));case 5:case"end":return e.stop()}}),e)}))),confirmLoading:O,children:(0,Z.jsxs)(v.Z,{form:n,layout:"vertical",initialValues:{id:null==u?void 0:u.id,name:null==u?void 0:u.name,phone:null==u?void 0:u.phone,role_id:null==u?void 0:u.role_id,group_id:null==u?void 0:u.group_id},children:[(0,Z.jsx)(v.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:(0,Z.jsx)(k.Z,{placeholder:"请输入姓名"})}),(0,Z.jsx)(v.Z.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"}],children:(0,Z.jsx)(k.Z,{placeholder:"请输入手机号"})}),(0,Z.jsx)(v.Z.Item,{name:"role_id",label:"选择权限",rules:[{required:!0,message:"请选择权限"}],children:(0,Z.jsx)(w.default,{placeholder:"请选择权限",loading:O,children:h.map((function(e){return(0,Z.jsx)(w.default.Option,{value:e.id,children:e.name},e.id)}))})}),(0,Z.jsx)(v.Z.Item,{name:"group_id",label:"选择组织",rules:[{required:!0,message:"请选择组织"}],children:(0,Z.jsx)(w.default,{placeholder:"请选择组织",loading:O,children:y.map((function(e){return(0,Z.jsx)(w.default.Option,{value:e.id,children:e.name},e.id)}))})})]})})},P=function(){var e=(0,f.useState)(!1),r=d()(e,2),n=r[0],t=r[1],o=(0,f.useState)(!1),i=d()(o,2),s=i[0],p=i[1],P=(0,f.useState)(),O=d()(P,2),T=O[0],E=O[1],$=(0,f.useRef)(),_=v.Z.useForm(),N=d()(_,1)[0],I=(0,f.useState)([{id:999,name:"全部"}]),A=d()(I,2),q=A[0],z=A[1],B=(0,f.useState)([{id:999,name:"全部"}]),F=d()(B,2),R=F[0],L=F[1],H=function(){var e=l()(c()().mark((function e(){var r,n,t,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.prev=1,e.next=4,(0,j.F3)();case 4:r=e.sent,(n=r)&&Array.isArray(n)?z([{id:999,name:"全部"}].concat(u()(n))):n&&n.data&&Array.isArray(n.data)&&z([{id:999,name:"全部"}].concat(u()(n.data))),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("获取角色失败:",e.t0);case 12:return e.prev=12,e.next=15,(0,j.jA)();case 15:t=e.sent,(a=t)&&Array.isArray(a)?L([{id:999,name:"全部"}].concat(u()(a))):a&&a.data&&Array.isArray(a.data)&&L([{id:999,name:"全部"}].concat(u()(a.data))),e.next=23;break;case 20:e.prev=20,e.t1=e.catch(12),console.error("获取组织失败:",e.t1);case 23:e.next=28;break;case 25:e.prev=25,e.t2=e.catch(0),g.ZP.error("获取角色或组织失败");case 28:case"end":return e.stop()}}),e,null,[[0,25],[1,9],[12,20]])})));return function(){return e.apply(this,arguments)}}();(0,f.useEffect)((function(){H()}),[]);var D=function(){var e=l()(c()().mark((function e(r){var n,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=g.ZP.loading("正在添加"),e.prev=1,e.next=4,(0,j.cn)(a()({},r));case 4:return n(),g.ZP.success("添加成功"),t(!1),null===(o=$.current)||void 0===o||o.reload(),N.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),n(),g.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),G=function(){var e=l()(c()().mark((function e(r){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=g.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,j.Nq)(r);case 4:return n(),g.ZP.success("更新成功"),p(!1),E(void 0),null===(t=$.current)||void 0===t||t.reload(),N.resetFields(),e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(1),n(),g.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 18:case"end":return e.stop()}}),e,null,[[1,13]])})));return function(r){return e.apply(this,arguments)}}(),W=function(){var e=l()(c()().mark((function e(r){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=g.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,j.h8)(r.id);case 4:return n(),g.ZP.success("删除成功"),null===(t=$.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),g.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(r){return e.apply(this,arguments)}}(),M=function(){var e=l()(c()().mark((function e(r,n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,j.az)(r,n?1:0);case 3:g.ZP.success("用户已".concat(n?"启用":"禁用")),null===(t=$.current)||void 0===t||t.reload(),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),g.ZP.error("操作失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r,n){return e.apply(this,arguments)}}(),U=[{title:"姓名",dataIndex:"name",valueType:"text"},{title:"电话",dataIndex:"phone",valueType:"text"},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"组织",dataIndex:"group_name",valueType:"select",valueEnum:R.reduce((function(e,r){return e[r.id]={text:r.name},e}),{})},{title:"权限",dataIndex:"role_name",valueType:"select",valueEnum:q.reduce((function(e,r){return e[r.id]={text:r.name},e}),{})},{title:"是否激活",dataIndex:"is_active",valueType:"switch",render:function(e,r){var n=Boolean(r.is_active);return(0,Z.jsx)(b.Z,{color:n?"green":"red",children:n?"激活":"禁用"})},search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,Z.jsx)(y.ZP,{type:"link",onClick:function(){p(!0),E(r)},children:"编辑"},"edit-".concat(r.id)),(0,Z.jsx)(y.ZP,{type:"link",danger:!0,onClick:function(){return W(r)},children:"删除"},"delete-".concat(r.id)),(0,Z.jsx)(y.ZP,{type:"link",onClick:function(){return M(r.id,!r.is_active)},children:r.is_active?"禁用":"启用"},"status-".concat(r.id))]}}];return(0,Z.jsxs)(h._z,{children:[(0,Z.jsx)(m.Z,{headerTitle:"用户管理",actionRef:$,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!1},form:{initialValues:{group_id:999,role_id:999}},toolBarRender:function(){return[(0,Z.jsxs)(y.ZP,{type:"primary",onClick:function(){t(!0)},children:[(0,Z.jsx)(C.Z,{})," 新建"]},"primary")]},request:function(){var e=l()(c()().mark((function e(r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,j.Rf)(a()({current:r.current||1,pageSize:r.pageSize||10},r));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:U}),(0,Z.jsx)(x.Z,{visible:n,title:"新建用户",onCancel:function(){return t(!1)},onOk:function(){return N.submit()},children:(0,Z.jsxs)(v.Z,{form:N,layout:"vertical",onFinish:D,children:[(0,Z.jsx)(v.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:(0,Z.jsx)(k.Z,{})}),(0,Z.jsx)(v.Z.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"}],children:(0,Z.jsx)(k.Z,{})}),(0,Z.jsx)(v.Z.Item,{name:"password",label:"初始密码",rules:[{required:!0,message:"请输入初始密码"}],children:(0,Z.jsx)(k.Z.Password,{})}),(0,Z.jsx)(v.Z.Item,{name:"role_id",label:"选择权限",rules:[{required:!0,message:"请选择权限"}],children:(0,Z.jsx)(w.default,{children:q.filter((function(e){return 999!==e.id})).map((function(e){return(0,Z.jsx)(w.default.Option,{value:e.id,children:e.name},e.id)}))})}),(0,Z.jsx)(v.Z.Item,{name:"group_id",label:"选择组织",rules:[{required:!0,message:"请选择组织"}],children:(0,Z.jsx)(w.default,{children:R.filter((function(e){return 999!==e.id})).map((function(e){return(0,Z.jsx)(w.default.Option,{value:e.id,children:e.name},e.id)}))})})]})}),T&&(0,Z.jsx)(S,{onSubmit:function(){var e=l()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,G(a()(a()({},T),r));case 2:e.sent&&(p(!1),E(void 0));case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),onCancel:function(){p(!1),E(void 0)},modalVisible:s,values:T})]})}},69044:function(e,r,n){n.d(r,{CW:function(){return B},F3:function(){return E},Nq:function(){return v},Rd:function(){return q},Rf:function(){return d},Rp:function(){return j},_d:function(){return _},az:function(){return x},cY:function(){return R},cn:function(){return h},h8:function(){return b},iE:function(){return O},jA:function(){return w},mD:function(){return S},ul:function(){return I},w1:function(){return H},wG:function(){return G}});var t=n(5574),a=n.n(t),o=n(97857),c=n.n(o),i=n(15009),u=n.n(i),s=n(99289),l=n.n(s),p=n(78158);function d(e){return f.apply(this,arguments)}function f(){return(f=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return m.apply(this,arguments)}function m(){return(m=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return g.apply(this,arguments)}function g(){return(g=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return y.apply(this,arguments)}function y(){return(y=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e,r){return k.apply(this,arguments)}function k(){return(k=l()(u()().mark((function e(r,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/users/changeStatus",{method:"POST",data:{id:r,status:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return C.apply(this,arguments)}function C(){return(C=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return Z.apply(this,arguments)}function Z(){return(Z=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return P.apply(this,arguments)}function P(){return(P=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e,r){return T.apply(this,arguments)}function T(){return(T=l()(u()().mark((function e(r,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/groups/".concat(r),c()({method:"DELETE"},n)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return $.apply(this,arguments)}function $(){return($=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return N.apply(this,arguments)}function N(){return(N=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(e,r){return A.apply(this,arguments)}function A(){return(A=l()(u()().mark((function e(r,n){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return z.apply(this,arguments)}function z(){return(z=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(){return F.apply(this,arguments)}function F(){return(F=l()(u()().mark((function e(){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e){return L.apply(this,arguments)}function L(){return(L=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/roles/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function H(e){return D.apply(this,arguments)}function D(){return(D=l()(u()().mark((function e(r){var n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new URLSearchParams,Object.entries(r).forEach((function(e){var r=a()(e,2),t=r[0],o=r[1];n.append(t,String(o))})),e.abrupt("return",(0,p.N)("/api/system/config?".concat(n.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return W.apply(this,arguments)}function W(){return(W=l()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.N)("/api/useActiveCases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,r,n){n.d(r,{Z:function(){return E}});var t=n(67294),a=n(93967),o=n.n(a),c=n(98423),i=n(98787),u=n(69760),s=n(96159),l=n(45353),p=n(53124),d=n(11568),f=n(15063),h=n(14747),m=n(83262),v=n(83559);const g=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,a=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:a,tagLineHeight:(0,d.bf)(t(e.lineHeightSM).mul(a).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,v.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:a,calc:o}=e,c=o(t).sub(n).equal(),i=o(r).sub(n).equal();return{[a]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(g(e))),b),x=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(t=Object.getOwnPropertySymbols(e);a<t.length;a++)r.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(n[t[a]]=e[t[a]])}return n};const k=t.forwardRef(((e,r)=>{const{prefixCls:n,style:a,className:c,checked:i,onChange:u,onClick:s}=e,l=x(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:f}=t.useContext(p.E_),h=d("tag",n),[m,v,g]=y(h),b=o()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:i},null==f?void 0:f.className,c,v,g);return m(t.createElement("span",Object.assign({},l,{ref:r,style:Object.assign(Object.assign({},a),null==f?void 0:f.style),className:b,onClick:e=>{null==u||u(!i),null==s||s(e)}})))}));var w=k,C=n(98719);var j=(0,v.bk)(["Tag","preset"],(e=>(e=>(0,C.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:a,lightColor:o,darkColor:c}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:o,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(g(e))),b);const Z=(e,r,n)=>{const t="string"!=typeof(a=n)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,v.bk)(["Tag","status"],(e=>{const r=g(e);return[Z(r,"success","Success"),Z(r,"processing","Info"),Z(r,"error","Error"),Z(r,"warning","Warning")]}),b),P=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(t=Object.getOwnPropertySymbols(e);a<t.length;a++)r.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(n[t[a]]=e[t[a]])}return n};const O=t.forwardRef(((e,r)=>{const{prefixCls:n,className:a,rootClassName:d,style:f,children:h,icon:m,color:v,onClose:g,bordered:b=!0,visible:x}=e,k=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:C,tag:Z}=t.useContext(p.E_),[O,T]=t.useState(!0),E=(0,c.Z)(k,["closeIcon","closable"]);t.useEffect((()=>{void 0!==x&&T(x)}),[x]);const $=(0,i.o2)(v),_=(0,i.yT)(v),N=$||_,I=Object.assign(Object.assign({backgroundColor:v&&!N?v:void 0},null==Z?void 0:Z.style),f),A=w("tag",n),[q,z,B]=y(A),F=o()(A,null==Z?void 0:Z.className,{[`${A}-${v}`]:N,[`${A}-has-color`]:v&&!N,[`${A}-hidden`]:!O,[`${A}-rtl`]:"rtl"===C,[`${A}-borderless`]:!b},a,d,z,B),R=e=>{e.stopPropagation(),null==g||g(e),e.defaultPrevented||T(!1)},[,L]=(0,u.Z)((0,u.w)(e),(0,u.w)(Z),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${A}-close-icon`,onClick:R},e);return(0,s.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),R(r)},className:o()(null==e?void 0:e.className,`${A}-close-icon`)})))}}),H="function"==typeof k.onClick||h&&"a"===h.type,D=m||null,G=D?t.createElement(t.Fragment,null,D,h&&t.createElement("span",null,h)):h,W=t.createElement("span",Object.assign({},E,{ref:r,className:F,style:I}),G,L,$&&t.createElement(j,{key:"preset",prefixCls:A}),_&&t.createElement(S,{key:"status",prefixCls:A}));return q(H?t.createElement(l.Z,{component:"Tag"},W):W)})),T=O;T.CheckableTag=w;var E=T}}]);