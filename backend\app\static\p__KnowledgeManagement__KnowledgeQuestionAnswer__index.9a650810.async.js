"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9440],{10964:function(e,n,t){t.r(n),t.d(n,{default:function(){return Te}});var r=t(64599),o=t.n(r),s=t(19632),i=t.n(s),a=t(15009),c=t.n(a),l=t(99289),u=t.n(l),d=t(97857),p=t.n(d),f=t(9783),x=t.n(f),g=t(13769),h=t.n(g),m=t(5574),v=t.n(m),y=t(93461),b=t(34114),k=t(78205),j=t(78919),w=t(4628),_=t(24495),Z=t(9502),S=t(76654),C=t(26058),P=t(83622),z=t(2453),I=t(17788),B=t(42075),T=t(86250),D=t(74330),R=t(83062),W=t(55102),E=t(85265),K=t(2487),M=t(38703),F=t(67294),H=t(10981),Y=t(78404),O=t(16596),N=t(14079),A=t(29158),q=t(38545),L=t(51042),G=t(82061),J=t(43425),U=t(64029),V=t(34804),X=t(47389),$=t(87784),Q=t(25820),ee=t(75750),ne=t(12906),te=t(85175),re=t(43471),oe=t(28508),se=t(98165),ie=t(27484),ae=t.n(ie),ce=(0,t(24444).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"50%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"60%",maxWidth:"900px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),le=t(93933),ue=t(58258),de=t(13973),pe=t(45435),fe=t(78158);function xe(e,n){return ge.apply(this,arguments)}function ge(){return(ge=u()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,fe.N)("/api/conversations/".concat(n,"/knowledge_ids"),{method:"PUT",data:{knowledge_ids:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function he(e){return me.apply(this,arguments)}function me(){return(me=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,fe.N)("/api/conversation_source_files",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ve=t(85893),ye=["href","children"],be=["href","children"],ke=C.Z.Sider;function je(e){return e+"-"+Date.now()}var we=F.createContext({expandedRefs:{},setExpandedRefs:function(){}}),_e={key:"1",label:"热门合规主题",children:[{key:"1-1",description:"如何确保金融产品合规销售？",icon:(0,ve.jsx)("span",{style:{color:"#f93a4a",fontWeight:700},children:"1"})},{key:"1-2",description:"反洗钱相关规定有哪些？",icon:(0,ve.jsx)("span",{style:{color:"#ff6565",fontWeight:700},children:"2"})},{key:"1-3",description:"客户信息保护要求有哪些？",icon:(0,ve.jsx)("span",{style:{color:"#ff8f1f",fontWeight:700},children:"3"})},{key:"1-4",description:"合规风险管控的主要措施有哪些？",icon:(0,ve.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"4"})},{key:"1-5",description:"如何应对监管检查？",icon:(0,ve.jsx)("span",{style:{color:"#00000040",fontWeight:700},children:"5"})}]},Ze={key:"2",label:"知识库对话指南",children:[{key:"2-1",icon:(0,ve.jsx)(O.Z,{}),label:"文件上传",description:"上传文件到知识库，支持PDF、Word、Excel等多种格式"},{key:"2-2",icon:(0,ve.jsx)(N.Z,{}),label:"知识库选择",description:"选择一个或多个知识库进行精准问答"},{key:"2-3",icon:(0,ve.jsx)(A.Z,{}),label:"引用查看",description:"查看回答的知识来源，点击引用可查看原文详情"},{key:"2-4",icon:(0,ve.jsx)(q.Z,{}),label:"对话交互",description:"与知识库进行多轮对话，获取专业准确的回答"}]},Se=[{key:"historyConversation",description:"历史对话",icon:(0,ve.jsx)(N.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,ve.jsx)(L.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,ve.jsx)(G.Z,{style:{color:"#1890FF"}})},{key:"knowledgeBaseSetting",description:"知识库设置",icon:(0,ve.jsx)(J.Z,{style:{color:"#1890FF"}})}],Ce=(0,H.bG)(),Pe=(0,Y.kH)(),ze="chat2kb",Ie=function(e){return"string"!=typeof e?"":e.replace(/\[\[citation:(\d+)\]\]/g,(function(e,n){return"[".concat(n,"](#citation-").concat(n,")")}))},Be=function(e){var n=e.content,t=e.messageId,r=(0,F.useState)(!1),o=v()(r,2),s=o[0],i=o[1],a=function(e){if("string"!=typeof e)return{processedContent:"",thinkBlocks:[]};var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),c=a.processedContent,l=a.thinkBlocks,u=F.useContext(we).setExpandedRefs;return(0,ve.jsxs)("div",{style:{position:"relative"},children:[function(){try{if(!c)return null;var e=Ie(c),n={a:function(e){var n=e.href,r=e.children,o=h()(e,ye);if(n&&n.startsWith("#citation-")){var s=n.replace("#citation-",""),i=parseInt(s)-1;return(0,ve.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(t,"-").concat(i);u((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:r})}return(0,ve.jsx)("a",p()(p()({href:n},o),{},{children:r}))},p:function(e){var n=e.children;return(0,ve.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,ve.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,ve.jsx)(pe.UG,{components:n,children:e})})}catch(e){return console.error("渲染内容时出错:",e),(0,ve.jsx)("div",{style:{color:"red"},children:"内容渲染失败"})}}(),l.length>0&&(0,ve.jsxs)("div",{style:{marginTop:12},children:[(0,ve.jsx)(P.ZP,{type:"text",size:"small",icon:s?(0,ve.jsx)(U.Z,{}):(0,ve.jsx)(V.Z,{}),onClick:function(){return i(!s)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,ve.jsxs)("span",{style:{marginLeft:4},children:[s?"收起":"展开","思考过程 (",l.length,")"]})}),s&&(0,ve.jsx)("div",{style:{marginTop:8,padding:12,backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:6,fontSize:"13px",lineHeight:1.4,color:"#495057"},children:l.map((function(e,n){return(0,ve.jsx)("div",{style:{marginBottom:n<l.length-1?8:0},children:(0,ve.jsx)(pe.UG,{components:{p:function(e){var n=e.children;return(0,ve.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}},children:e})},n)}))})]})]})},Te=function(){var e=(0,F.useState)({}),n=v()(e,2),t=n[0],r=n[1],s=(0,F.useState)(!1),a=v()(s,2),l=a[0],d=a[1],f=(0,F.useState)(null),g=v()(f,2),m=g[0],C=g[1],Y=ce().styles,q=(0,F.useState)(window.innerHeight),U=v()(q,1)[0],V=F.useRef(),ie=F.useState([]),fe=v()(ie,2),ge=fe[0],me=fe[1],ye=F.useRef(null),Te=F.useRef(null),De=F.useState(""),Re=v()(De,2),We=Re[0],Ee=Re[1],Ke=F.useState([]),Me=v()(Ke,2),Fe=Me[0],He=Me[1],Ye=F.useState(),Oe=v()(Ye,2),Ne=Oe[0],Ae=Oe[1],qe=F.useState(void 0),Le=v()(qe,2),Ge=Le[0],Je=Le[1],Ue=(0,F.useState)(!1),Ve=v()(Ue,2),Xe=Ve[0],$e=Ve[1],Qe=(0,F.useState)(!1),en=v()(Qe,2),nn=en[0],tn=en[1],rn=(0,F.useState)(!1),on=v()(rn,2),sn=on[0],an=on[1],cn=(0,F.useState)(""),ln=v()(cn,2),un=ln[0],dn=ln[1],pn=(0,F.useState)(""),fn=v()(pn,2),xn=fn[0],gn=fn[1],hn=(0,F.useState)([]),mn=v()(hn,2),vn=mn[0],yn=mn[1],bn=(0,F.useRef)(null),kn=(0,F.useState)(!1),jn=v()(kn,2),wn=jn[0],_n=jn[1],Zn=(0,F.useState)(""),Sn=v()(Zn,2),Cn=Sn[0],Pn=Sn[1],zn=(0,F.useState)([]),In=v()(zn,2),Bn=In[0],Tn=In[1],Dn=(0,F.useState)([]),Rn=v()(Dn,2),Wn=Rn[0],En=Rn[1],Kn=(0,F.useState)(!1),Mn=v()(Kn,2),Fn=Mn[0],Hn=Mn[1],Yn=F.useState([]),On=v()(Yn,2),Nn=On[0],An=On[1],qn=(0,F.useState)({}),Ln=v()(qn,2),Gn=Ln[0],Jn=Ln[1],Un=function(){var e=u()(c()().mark((function e(n){var t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=3;break}return z.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:return e.prev=3,e.next=6,he({knowledge_base_id:n,pageSize:100});case 6:(t=e.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),me(r)),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("刷新文件状态失败:",e.t0),z.ZP.error("刷新文件状态失败");case 14:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(n){return e.apply(this,arguments)}}(),Vn=function(e){Pn(e),_n(!0)},Xn=function(e){var n=vn.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){z.ZP.success("复制成功")})).catch((function(){z.ZP.error("复制失败")}))},$n=(0,F.useRef)({selectedKnowledgeBaseIds:Wn,availableKnowledgeBases:Bn,activeConversationKey:Ne,conversationKnowledgeBaseId:Ge});(0,F.useEffect)((function(){$n.current={selectedKnowledgeBaseIds:Wn,availableKnowledgeBases:Bn,activeConversationKey:Ne,conversationKnowledgeBaseId:Ge}}),[Wn,Bn,Ne,Ge]);var Qn,et=function(){var e=u()(c()().mark((function e(){var n,t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,H.bG)(),console.log("🚀 ~ fetchKnowledgeBases ~ userInfo:",n),e.prev=2,e.next=5,(0,le.fx)({user_id:parseInt((null==n?void 0:n.id)||"0")});case 5:return t=e.sent,console.log("🚀 ~ fetchKnowledgeBases ~ response:",t),r=t.data||[],Tn(r),Wn.length>0&&(o=Wn.filter((function(e){return r.some((function(n){return n._id===e}))}))).length!==Wn.length&&(console.log("🚀 ~ fetchKnowledgeBases ~ 更新有效的知识库IDs:",o),En(o)),e.abrupt("return",r);case 13:return e.prev=13,e.t0=e.catch(2),console.error("Error fetching knowledge bases:",e.t0),e.abrupt("return",[]);case 17:case"end":return e.stop()}}),e,null,[[2,13]])})));return function(){return e.apply(this,arguments)}}(),nt=(0,y.Z)({request:(Qn=u()(c()().mark((function e(n,t){var r,s,a,l,u,d,p,f,x,g,h,m,v,y,b,k,j,w,_,Z,S,C,P,I,B,T,D,R,W,E,K,M,F,H,Y,O,N,A,q,L,G;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,s=n.message,a=t.onSuccess,l=t.onUpdate,u=t.onError,e.prev=2,p=$n.current,f=p.selectedKnowledgeBaseIds,x=p.availableKnowledgeBases,g=p.conversationKnowledgeBaseId,console.log("🚀 ~ request中的最新状态:",{selectedKnowledgeBaseIds:f,availableKnowledgeBases:x,conversationKnowledgeBaseId:g}),!nn){e.next=8;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 8:if(tn(!0),h=s?s.id:je(V.current),s){e.next=14;break}return a({content:"出现了异常: 消息为空",role:"assistant",id:h,references:[],collected:!1,query:[]}),tn(!1),e.abrupt("return");case 14:if(m=f||[],g&&(m=[].concat(i()(m),[g])),console.log("🚀 ~ 最终的selectedIds:",m),v={conversation_id:V.current||"",message_id:h,meta_data:{},extra:{},role:s?s.role:"user",content:s?s.content:"",app_info:ze,user_id:null==Ce?void 0:Ce.id,user_name:null==Ce?void 0:Ce.name,references:[],token_count:null,price:null,collected:!1,created_at:ae()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m,contextData:[],query:[]},yn((function(e){var n=[].concat(i()(e),[v]);return console.log("更新后的消息列表:",n),n})),V.current){e.next=22;break}throw z.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 22:return console.log("activeKey===>",V.current),y={conversation_id:V.current,app_info:ze,user_id:parseInt(null==Ce?void 0:Ce.id),user_name:null==Ce?void 0:Ce.name,extra:{},messages:r,kb_id:m},b={id:je(V.current),role:"assistant",content:"",references:[],collected:!1,query:[]},k=!1,j="",w=[],l(b),e.next=31,(0,le.zl)(y);case 31:if(_=e.sent,console.log("response===>",_),_.ok){e.next=35;break}throw new Error("HTTP 错误！状态码：".concat(_.status));case 35:if(Z=null===(d=_.body)||void 0===d?void 0:d.getReader()){e.next=38;break}throw new Error("当前浏览器不支持 ReadableStream。");case 38:S=new TextDecoder("utf-8"),console.log("userInfo===>",Ce),C={conversation_id:V.current||"",message_id:b.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:ze,user_id:null==Ce?void 0:Ce.id,user_name:null==Ce?void 0:Ce.name,references:[],token_count:null,price:null,collected:!1,created_at:ae()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m};case 41:if(k){e.next=112;break}return e.next=44,Z.read();case 44:P=e.sent,I=P.value,P.done&&(k=!0),j+=S.decode(I,{stream:!0}),B=j.split("\n\n"),j=B.pop()||"",T=o()(B),e.prev=52,T.s();case 54:if((D=T.n()).done){e.next=102;break}if(""!==(R=D.value).trim()){e.next=58;break}return e.abrupt("continue",100);case 58:W=R.split("\n"),E=null,K=null,M=o()(W);try{for(M.s();!(F=M.n()).done;)(H=F.value).startsWith("event: ")?E=H.substring(7).trim():H.startsWith("data: ")&&(K=H.substring(6))}catch(e){M.e(e)}finally{M.f()}if(!K){e.next=100;break}e.t0=E,e.next="answer"===e.t0?67:"moduleStatus"===e.t0?79:"appStreamResponse"===e.t0?81:"flowResponses"===e.t0?83:"end"===e.t0?85:"error"===e.t0?87:100;break;case 67:if("[DONE]"===K){e.next=78;break}e.prev=68,O=JSON.parse(K),(N=(null===(Y=O.choices[0])||void 0===Y||null===(Y=Y.delta)||void 0===Y?void 0:Y.content)||"")&&(b.content+=N,l(b)),e.next=78;break;case 74:return e.prev=74,e.t1=e.catch(68),console.error("Error parsing answer data:",e.t1),e.abrupt("return",a({content:"出现了异常:"+K,role:"assistant",id:je(V.current),references:[],query:[],collected:!1}));case 78:return e.abrupt("break",100);case 79:try{A=JSON.parse(K),console.log("模块状态：",A)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",100);case 81:try{q=JSON.parse(K),console.log("appStreamData===>",q),console.log("appStreamData[0].context===>",q[0].context),w=q[0].context,b.references=w}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",100);case 83:try{console.log("flowResponsesData",K)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",100);case 85:return k=!0,e.abrupt("break",100);case 87:e.prev=87,k=!0,L=JSON.parse(K),b.content=L.message,C.role="assistant",l(b),e.next=99;break;case 95:throw e.prev=95,e.t2=e.catch(87),console.error("Error event received:",e.t2),e.t2;case 99:return e.abrupt("break",100);case 100:e.next=54;break;case 102:e.next=107;break;case 104:e.prev=104,e.t3=e.catch(52),T.e(e.t3);case 107:return e.prev=107,T.f(),e.finish(107);case 110:e.next=41;break;case 112:if(console.info(b),a(b),!b.content||""===b.content.trim()){e.next=121;break}return C.content=b.content,C.references=w,e.next=119,(0,le.tn)(C);case 119:(G=e.sent).success?(C.message_id=G.data.message_id,console.log("创建消息成功，返回数据:",G.data),yn((function(e){var n=[].concat(i()(e),[G.data]);return console.log("更新后的消息列表:",n),n}))):z.ZP.error("消息上报失败");case 121:e.next=128;break;case 123:e.prev=123,e.t4=e.catch(2),console.log("error===>",e.t4),a({content:"出现了异常，系统正在处理其他对话。请稍后重试",role:"assistant",id:je(V.current),references:[],collected:!1,query:[]}),u(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 128:return e.prev=128,tn(!1),e.finish(128);case 131:case"end":return e.stop()}}),e,null,[[2,123,128,131],[52,104,107,110],[68,74],[87,95]])}))),function(e,n){return Qn.apply(this,arguments)})}),tt=v()(nt,1)[0],rt=(0,b.Z)({agent:tt}),ot=rt.onRequest,st=rt.messages,it=rt.setMessages,at=function(e){console.log("activeKey 设置",e),V.current=e,Ae(e)},ct=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return p()(p()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return p()(p()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));He([].concat(i()(n),i()(t)))},lt=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},ut=function(){var e=u()(c()().mark((function e(n){var t,r,o,s,i,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,tn(!0),console.info("获取对话信息",n),(t=Bn)&&0!==t.length||console.log("🚀 ~ fetchConversationMessages ~ 可用知识库列表为空，但是我们应该在页面加载时已经获取了"),r=ae()().format("YYYY-MM-DD HH:mm:ss"),e.next=8,(0,le.$o)(n,{conversation_name:null,active_at:r,pinned_at:null,pinned:null});case 8:o=e.sent,console.log("🚀 ~ fetchConversationMessages ~ result:",o),o&&o.knowledge_ids&&console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),me([]),o&&o.uploaded_files&&Array.isArray(o.uploaded_files)&&(console.log("🚀 ~ 获取到对话上传的文件:",o.uploaded_files),(s=o.uploaded_files.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}}))).length>0&&me(s)),null!=o&&o.messages?(console.info("设置对话信息",o.messages),i=o.messages.map((function(e){return{id:e.message_id,message:{id:e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1,query:e.query||[]},status:"assistant"===e.role?"success":"local",meta:{avatar:"assistant"===e.role?(null==Pe?void 0:Pe.logo)||"/static/logo.png":(null==Ce?void 0:Ce.avatar)||"/avatar/default.jpeg"}}})),yn(o.messages),it(i),at(n),En([]),Je(void 0),o.knowledge_ids&&Array.isArray(o.knowledge_ids)&&o.knowledge_ids.length>0?(console.log("🚀 ~ 获取到对话绑定的知识库IDs:",o.knowledge_ids),a=o.knowledge_ids.filter((function(e){return t.some((function(n){return n._id===e}))})),console.log("🚀 ~ 筛选后的知识库IDs:",a),a.length>0&&En(a)):console.log("🚀 ~ 对话没有绑定知识库IDs")):z.ZP.error("获取对话信息失败"),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 19:return e.prev=19,tn(!1),Ae(n),e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[0,16,19,23]])})));return function(n){return e.apply(this,arguments)}}(),dt=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,le.Db)(V.current);case 4:e.sent.success?(yn([]),it([]),bn.current&&bn.current.updateReferenceList([])):z.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),pt=function(){var e=u()(c()().mark((function e(){var n,t,r,o,s,a,l,u;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!nn){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,H.bG)())){e.next=27;break}return e.prev=5,En([]),me([]),Jn({}),Je(void 0),t=(new Date).toLocaleString(),r="对话-".concat(t),o=[],e.next=15,(0,le.Xw)({user_id:parseInt(n.id),user_name:n.name,conversation_name:r,app_info:ze,knowledge_ids:o});case 15:s=e.sent,a={key:s.id||"",id:s.id||"",label:s.conversation_name||"",group:"对话",conversation_name:s.conversation_name||"",app_info:ze,active_at:s.active_at||"",pinned_at:s.pinned_at,pinned:s.pinned||!1,messages:[],knowledge_ids:o,uploaded_files:[]},l=Fe.filter((function(e){return!e.pinned})),u=Fe.filter((function(e){return e.pinned})),He([].concat(i()(u),[a],i()(l))),at(s.id||""),dt(),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 27:case"end":return e.stop()}}),e,null,[[5,24]])})));return function(){return e.apply(this,arguments)}}(),ft=function(){var e=u()(c()().mark((function e(n){var t,r,o,s,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=Fe.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,o=!r,e.prev=6,s=ae()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,le.X1)(n,{conversation_name:null,active_at:null,pinned:o,pinned_at:s});case 10:i=Fe.map((function(e){return e.key===n?p()(p()({},e),{},{pinned:o}):e})),ct(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),xt=function(){var e=u()(c()().mark((function e(n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!nn){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(console.info("conversationsItems===>",Fe),t=Fe.some((function(e){return e.key===n}))){e.next=10;break}return z.ZP.error("对话不存在或已被删除"),e.abrupt("return");case 10:console.info("对话存在",t);case 11:return console.log("onConversationClick===>",n),Jn({}),e.next=15,ut(n);case 15:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),gt=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:I.Z.confirm({title:"确认删除",content:"确定要删除这个对话吗？删除后不可恢复。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){return u()(c()().mark((function e(){var t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=null,r=Fe.filter((function(e){return e.key!==n})),console.log("更新 updatedItems===>",r),r.length>0&&(t=r[0].key),console.log(" nextActiveId===>",t),console.log("删除 deleteConversation===>",n),e.next=9,(0,le.SJ)(n);case 9:ct(r),setTimeout((function(){t?(console.log("激活 ===>",t),xt(t)):(console.log("新建 ===>",t),pt())}),0),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0),z.ZP.error("删除对话失败");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),ht=function(){var e=u()(c()().mark((function e(n,t){var r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,Fe.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,le.X1)(n,r);case 7:null!=(o=e.sent)&&o.success?He((function(e){return e.map((function(e){return e.key===n?p()(p()({},e),{},{label:t}):e}))})):z.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}();(0,F.useEffect)((function(){var e=function(){var e=u()(c()().mark((function e(){var n,t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,H.bG)(),e.next=3,et();case 3:if(!n){e.next=32;break}return e.prev=4,tn(!0),e.next=8,(0,le.Mw)({user_id:n.id,app_info:ze});case 8:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=24;break}if(0!==t.data.length){e.next=15;break}return e.next=13,pt();case 13:e.next=24;break;case 15:if(r=lt(t.data),ct(t.data),!r){e.next=22;break}return e.next=20,ut(r.id);case 20:e.next=24;break;case 22:return e.next=24,ut(t.data[0].id||"");case 24:e.next=29;break;case 26:e.prev=26,e.t0=e.catch(4),console.error("初始化对话时出错：",e.t0);case 29:return e.prev=29,tn(!1),e.finish(29);case 32:case"end":return e.stop()}}),e,null,[[4,26,29,32]])})));return function(){return e.apply(this,arguments)}}();e()}),[ze]);var mt=function(){var e=u()(c()().mark((function e(n){var t,r,o,s;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=vn.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return z.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=vn[t],o=vn.slice(t),console.log("将要删除的消息:",o),e.next=12,(0,le.qP)(o.map((function(e){return e.message_id})));case 12:e.sent.success||z.ZP.error("删除消息失败"),yn((function(e){return e.slice(0,t)})),it((function(e){return e.slice(0,t)})),"assistant"===r.role?(s=vn.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&ot({id:n,role:"user",content:s.content,references:[],query:[],collected:!1}):ot({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),z.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),z.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),vt=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:I.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,le.$Z)(n);case 4:e.sent.success?(yn((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",vn),it((function(e){return e.filter((function(e){return e.message.id!==n}))})),z.ZP.success("消息及相关引用已删除")):z.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),z.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),yt=function(){var e=u()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,le.bk)({message_id:n,collected:!t});case 3:e.sent.success?(z.ZP.success(t?"取消收藏成功":"收藏成功"),it((function(e){return e.map((function(e){return e.id===n?p()(p()({},e),{},{message:p()(p()({},e.message),{},{collected:!t})}):e}))})),yn((function(e){return e.map((function(e){return e.message_id===n?p()(p()({},e),{},{collected:!t}):e}))}))):z.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),bt=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:if(t=Wn&&Wn.length>0,r=ge&&ge.length>0,o=!!Ge,t||r||o){e.next=8;break}return z.ZP.warning("您还尚未选择知识库，请先选择知识库！"),e.abrupt("return");case 8:ot({id:je(V.current),role:"user",content:n,references:[],collected:!1,query:[]}),An([]),Ee("");case 11:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),kt=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!nn){e.next=3;break}return z.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,o=t.description,"historyConversation"!==r){e.next=8;break}$e(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,pt();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,dt();case 16:e.next=19;break;case 18:if("knowledgeBaseSetting"===r)try{tn(!0),Hn(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),Hn(!1)}finally{tn(!1)}else ot({id:je(V.current),role:"user",content:o,references:[],collected:!1,query:[]});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),jt=function(e,n){n&&n.stopPropagation(),C(e),d(!0)},wt=(0,ve.jsxs)(B.Z,{direction:"vertical",size:16,style:{paddingInline:"calc(calc(100% - 700px) /2)"},className:Y.placeholder,children:[(0,ve.jsx)(k.Z,{variant:"borderless",icon:(0,ve.jsx)("img",{src:(null==Pe?void 0:Pe.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是知识库对话助手",description:"基于您的知识库内容，为您提供精准的问答服务"}),(0,ve.jsxs)(T.Z,{gap:16,children:[(0,ve.jsx)(j.Z,{items:[_e],styles:{list:{height:"100%"},item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{padding:0,background:"transparent"}},onItemClick:kt}),(0,ve.jsx)(j.Z,{items:[Ze],styles:{item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{background:"#ffffffa6"}}})]})]}),_t=st.length>0?st.map((function(e){var n=e.id,o=e.message,s=e.status;return{key:V.current+"_"+n,loadingRender:function(){return(0,ve.jsxs)(B.Z,{children:[(0,ve.jsx)(D.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===s&&o.content.length<1,content:o.content,messageRender:function(e){return function(e,n){if("string"!=typeof e)return String(e);if(!e.includes("<think>")){var t=Ie(e),o={a:function(e){var t=e.href,o=e.children,s=h()(e,be);if(t&&t.startsWith("#citation-")){var i=t.replace("#citation-",""),a=parseInt(i)-1;return(0,ve.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(n,"-").concat(a);r((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:o})}return(0,ve.jsx)("a",p()(p()({href:t},s),{},{children:o}))},p:function(e){var n=e.children;return(0,ve.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,ve.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,ve.jsx)(pe.UG,{components:o,children:t})})}return(0,ve.jsx)(Be,{content:e,messageId:n})}(e,o.id)},shape:"local"===s?"corner":"round",variant:"local"===s?"filled":"borderless",avatar:"local"===s?{src:(null==Ce?void 0:Ce.avatar)||"/avatar/default.jpeg"}:{src:(null==Pe?void 0:Pe.logo)||"/static/logo.png"},placement:"local"!==s?"start":"end",footer:"local"!==s?(0,ve.jsxs)(T.Z,{children:[o.references&&o.references.length>0&&(0,ve.jsxs)("div",{style:{marginTop:8,width:"100%"},children:[(0,ve.jsxs)("div",{style:{fontWeight:"bold",marginBottom:8,fontSize:13},children:["引用来源 (",o.references.length,")"]}),(0,ve.jsx)("div",{children:o.references.map((function(e,n){return(0,ve.jsxs)("div",{style:{marginBottom:8},"data-ref-index":n,"data-ref-key":"".concat(o.id,"-").concat(n),children:[(0,ve.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px 13px",border:"1px solid #f0f0f0",borderRadius:t["".concat(o.id,"-").concat(n)]?"4px 4px 0 0":4,backgroundColor:"#fafafa",justifyContent:"space-between",width:"100%"},onClick:function(n){n.stopPropagation(),jt(e,n)},children:[(0,ve.jsxs)("div",{style:{fontWeight:"bold",fontSize:12,color:"#666",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"80%"},children:[n+1,". ",e.source_name&&e.source_name.length>25?e.source_name.slice(0,25)+"...":e.source_name||"来源 ".concat(n+1)]}),(0,ve.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8,flexShrink:0},children:[(0,ve.jsx)(P.ZP,{type:"text",size:"small",icon:(0,ve.jsx)(N.Z,{}),onClick:function(n){return jt(e,n)},style:{fontSize:12,color:"#1890ff",padding:"0 4px",height:20,border:"none",flexShrink:0},title:"查看详情"}),(0,ve.jsx)("span",{style:{fontSize:12,color:"#999",cursor:"pointer",flexShrink:0},onClick:function(e){e.stopPropagation();var t="".concat(o.id,"-").concat(n);r((function(e){return e[t]?{}:x()({},t,!0)}))},children:t["".concat(o.id,"-").concat(n)]?"▲":"▼"})]})]}),t["".concat(o.id,"-").concat(n)]&&(0,ve.jsx)("div",{style:{padding:8,border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px",backgroundColor:"#fff",fontSize:12,lineHeight:1.4,color:"#333",width:"100%",wordBreak:"break-word",overflow:"auto",maxHeight:"300px"},children:e.content})]},n)}))})]}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(G.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),vt(o.id)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:o.collected?(0,ve.jsx)(Q.Z,{style:{color:"#FFD700"}}):(0,ve.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),yt(o.id,o.collected)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Vn(o.id)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Xn(o.id)}})]}):(0,ve.jsxs)(T.Z,{children:[(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),mt(o.id)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(G.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),vt(o.id)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:o.collected?(0,ve.jsx)(Q.Z,{style:{color:"#FFD700"}}):(0,ve.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),yt(o.id,o.collected)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Vn(o.id)}}),(0,ve.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ve.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Xn(o.id)}})]})}})):[{content:wt,variant:"borderless"}],Zt=F.useState(!1),St=v()(Zt,2),Ct=St[0],Pt=St[1],zt=(0,ve.jsx)(w.Z.Header,{title:"附件",styles:{content:{padding:0}},open:Ct,onOpenChange:Pt,forceRender:!0,children:(0,ve.jsx)(_.Z,{ref:ye,beforeUpload:function(){return!1},fileList:Nn,onChange:function(e){var n=e.fileList;An(n);var t=n.filter((function(e){return e.originFileObj})).filter((function(e){return!ge.some((function(n){return n.uid===e.uid}))}));if(0!==t.length){var r=t.map((function(e){return{uid:e.uid,name:e.name,status:"uploading",size:e.size,type:e.type,created_at:ae()().format("YYYY-MM-DD HH:mm:ss"),processing_status:"uploading"}}));me((function(e){return[].concat(i()(e),i()(r))})),t.forEach(function(){var e=u()(c()().mark((function e(n){var t,r,o,s,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=V.current){e.next=4;break}return z.ZP.error("请先选择一个对话"),e.abrupt("return");case 4:return r=n.uid,Jn((function(e){return p()(p()({},e),{},x()({},r,{percent:0,status:"uploading"}))})),console.log("开始上传文件:",r,n.name),o=setInterval((function(){Jn((function(e){var n,t=(null===(n=e[r])||void 0===n?void 0:n.percent)||0;if(t<95){var o=t+Math.floor(8*Math.random())+3;return console.log("更新上传进度:",r,o),p()(p()({},e),{},x()({},r,p()(p()({},e[r]),{},{percent:o})))}return e}))}),300),1,s=[n.originFileObj].filter((function(e){return e})),"","","chat",e.prev=13,e.next=16,(0,ue.pj)(t,1,s,"","","chat");case 16:i=e.sent,clearInterval(o),console.log("🚀 ~ newFiles.forEach ~ response:",i),i.detail?(z.ZP.error(i.detail),Jn((function(e){return p()(p()({},e),{},x()({},r,{percent:100,status:"error"}))})),me((function(e){return e.map((function(e){return e.uid===r?p()(p()({},e),{},{processing_status:"error",status:"error"}):e}))})),setTimeout((function(){Jn((function(e){var n=p()({},e);return delete n[r],n}))}),3e3)):(Jn((function(e){return p()(p()({},e),{},x()({},r,{percent:100,status:"done"}))})),console.log("上传完成，更新状态为done:",r),me((function(e){return e.map((function(e){return e.uid===r?p()(p()({},e),{},{processing_status:"pending",status:"done"}):e}))})),setTimeout((function(){Jn((function(e){var n=p()({},e);return delete n[r],n}))}),2e3),z.ZP.success("上传文件成功"),V.current&&(Un(V.current),Je(V.current))),e.next=30;break;case 22:e.prev=22,e.t0=e.catch(13),clearInterval(o),console.error("文件上传失败:",e.t0),Jn((function(e){return p()(p()({},e),{},x()({},r,{percent:100,status:"error"}))})),me((function(e){return e.map((function(e){return e.uid===r?p()(p()({},e),{},{processing_status:"error",status:"error"}):e}))})),setTimeout((function(){Jn((function(e){var n=p()({},e);return delete n[r],n}))}),3e3),z.ZP.error("文件上传失败");case 30:case"end":return e.stop()}}),e,null,[[13,22]])})));return function(n){return e.apply(this,arguments)}}()),setTimeout((function(){An([])}),100)}},placeholder:function(e){return"drop"===e?{title:"将文件拖放到此处"}:{icon:(0,ve.jsx)(O.Z,{}),title:"上传文件",description:"点击或将文件拖拽到此区域上传\n支持PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片、音频等格式"}},getDropContainer:function(){var e;return null===(e=Te.current)||void 0===e?void 0:e.nativeElement}})}),It=(0,ve.jsxs)("div",{className:Y.logo,children:[(0,ve.jsx)("span",{children:"对话记录"}),(0,ve.jsx)(R.Z,{title:"新对话",children:(0,ve.jsx)(P.ZP,{type:"text",icon:(0,ve.jsx)(L.Z,{}),onClick:pt,style:{fontSize:"16px"}})})]}),Bt=(0,ve.jsx)(I.Z,{title:"修改对话标题",open:sn,onOk:function(){xn&&un.trim()&&(ht(xn,un.trim()),an(!1))},onCancel:function(){an(!1),dn(""),gn("")},children:(0,ve.jsx)(W.Z,{value:un,onChange:function(e){return dn(e.target.value)},placeholder:"请输入新的对话标题"})}),Tt=(0,ve.jsx)(E.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return $e(!1)},open:Xe,children:(0,ve.jsxs)("div",{className:Y.menu,children:[It,(0,ve.jsx)(Z.Z,{items:Fe,activeKey:Ne,onActiveChange:xt,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,ve.jsx)(X.Z,{})},{label:"置顶",key:"pin",icon:(0,ve.jsx)($.Z,{})},{label:"删除",key:"delete",icon:(0,ve.jsx)(G.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":gn(e.key),dn(e.label),an(!0);break;case"pin":ft(e.key);break;case"delete":if(nn)return void z.ZP.error("系统正在处理其他对话。请稍😊");gt(e.key)}}}},groupable:!0})]})}),Dt=F.memo((function(e){var n=e.isOpen,t=e.onClose,r=e.availableKnowledgeBases,o=e.selectedKnowledgeBaseIds,s=e.setSelectedKnowledgeBaseIds,a=(0,F.useState)([]),l=v()(a,2),d=l[0],p=l[1],f=(0,F.useState)(!1),x=v()(f,2),g=x[0],h=x[1],m=(0,F.useState)(!1),y=v()(m,2),b=y[0],k=y[1];(0,F.useEffect)((function(){if(n){p(i()(o));var e=setTimeout((function(){k(!0)}),100);return function(){clearTimeout(e),k(!1)}}}),[n,o]);var j=(0,F.useCallback)((function(e,n){n&&(n.stopPropagation(),n.preventDefault()),p((function(n){return n.includes(e)?n.filter((function(n){return n!==e})):[].concat(i()(n),[e])}))}),[]),w=function(){var e=u()(c()().mark((function e(){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,h(!0),!V.current){e.next=19;break}return n=d,e.next=6,xe(V.current,n);case 6:if(!e.sent.success){e.next=16;break}return s(d),z.ZP.success("知识库设置已更新"),h(!1),k(!1),t(),e.abrupt("return");case 16:z.ZP.error("更新知识库设置失败");case 17:e.next=25;break;case 19:return s(d),z.ZP.warning("未选择对话，知识库设置仅保存在本地"),h(!1),k(!1),t(),e.abrupt("return");case 25:e.next=31;break;case 27:e.prev=27,e.t0=e.catch(0),console.error("保存知识库设置时出错:",e.t0),z.ZP.error("保存知识库设置失败");case 31:h(!1);case 32:case"end":return e.stop()}}),e,null,[[0,27]])})));return function(){return e.apply(this,arguments)}}();return(0,ve.jsx)(I.Z,{title:"知识库设置",open:n,onCancel:function(){k(!1),setTimeout((function(){t()}),100)},width:1200,styles:{body:{padding:"12px 16px"},mask:{backgroundColor:"rgba(0, 0, 0, 0.45)"},content:{boxShadow:"0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.12)",transition:"all 0.3s ease"}},destroyOnClose:!0,maskClosable:!1,footer:[(0,ve.jsx)(P.ZP,{onClick:function(){k(!1),setTimeout((function(){t()}),100)},children:"关闭"},"close"),(0,ve.jsx)(P.ZP,{type:"primary",onClick:w,loading:g,children:"保存"},"save")],children:b?(0,ve.jsxs)("div",{style:{display:"flex",height:"500px",opacity:b?1:0,transition:"opacity 0.3s ease"},children:[(0,ve.jsxs)("div",{style:{width:"50%",paddingRight:"12px",overflowY:"auto"},children:[(0,ve.jsx)("h3",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"可选知识库"}),r.length>0?(0,ve.jsx)(K.Z,{grid:{gutter:16,column:2},dataSource:r,renderItem:function(e){var n=d.includes(e._id);return(0,ve.jsx)(K.Z.Item,{onClick:function(n){return j(e._id,n)},style:{border:n?"1px solid #1890ff":"1px solid #e0e0e0",padding:"12px",backgroundColor:n?"#e6f7ff":"#fff",color:n?"#1890ff":"inherit",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:n?"0 2px 8px rgba(24, 144, 255, 0.15)":"none",cursor:"pointer",marginBottom:"8px"},children:(0,ve.jsx)(K.Z.Item.Meta,{title:(0,ve.jsx)("span",{style:{color:n?"#1890ff":"inherit",fontWeight:n?500:400,fontSize:"14px"},children:e.name}),description:(0,ve.jsx)("div",{style:{fontSize:"12px",color:"#999",marginTop:"4px"},children:e.description||"暂无描述"})})})}}):(0,ve.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂无可用知识库"})]}),(0,ve.jsxs)("div",{style:{width:"50%",paddingLeft:"12px",borderLeft:"1px solid #f0f0f0",overflowY:"auto"},children:[(0,ve.jsx)("h4",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"已选知识库"}),d.length>0?(0,ve.jsx)(K.Z,{grid:{gutter:16,column:2},dataSource:d,renderItem:function(e){var n=r.find((function(n){return n._id===e}));return(0,ve.jsx)(K.Z.Item,{onClick:function(n){return j(e,n)},style:{border:"1px solid #1890ff",padding:"12px",backgroundColor:"#e6f7ff",color:"#1890ff",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(24, 144, 255, 0.15)",cursor:"pointer",marginBottom:"8px"},children:(0,ve.jsx)(K.Z.Item.Meta,{title:(0,ve.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,ve.jsx)("span",{style:{color:"#1890ff",fontWeight:500,fontSize:"14px"},children:(null==n?void 0:n.name)||"未知知识库"}),(0,ve.jsx)(P.ZP,{type:"text",size:"small",icon:(0,ve.jsx)(oe.Z,{}),onClick:function(n){n.stopPropagation(),p((function(n){return n.filter((function(n){return n!==e}))}))},style:{color:"#1890ff"}})]}),description:(0,ve.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:(null==n?void 0:n.description)||"暂无描述"})})})}}):(0,ve.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂未选择知识库"})]})]}):(0,ve.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"500px"},children:(0,ve.jsx)(D.Z,{size:"large",tip:"正在加载知识库数据..."})})})}));(0,F.useEffect)((function(){console.log("currentConversationMessages 更新了:",vn)}),[vn]),(0,F.useEffect)((function(){}),[]);var Rt=(0,ve.jsx)(E.Z,{title:"引用来源详情",placement:"right",width:500,onClose:function(){return d(!1)},open:l,children:m&&(0,ve.jsxs)("div",{style:{padding:"16px 0"},children:[(0,ve.jsxs)("div",{style:{marginBottom:24},children:[(0,ve.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"文档标题"}),(0,ve.jsx)("div",{style:{padding:12,backgroundColor:"#f5f5f5",borderRadius:6,fontSize:14,color:"#666"},children:m.source_name||"未知来源"})]}),(0,ve.jsxs)("div",{style:{marginBottom:24},children:[(0,ve.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"引用内容"}),(0,ve.jsx)("div",{style:{padding:16,backgroundColor:"#fafafa",border:"1px solid #e8e8e8",borderRadius:6,fontSize:14,lineHeight:1.6,color:"#333",maxHeight:500,overflowY:"auto"},children:m.content||"暂无内容"})]}),m.metadata&&(0,ve.jsxs)("div",{style:{marginBottom:24},children:[(0,ve.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"其他信息"}),(0,ve.jsx)("div",{style:{fontSize:12,color:"#888"},children:Object.entries(m.metadata).map((function(e){var n=v()(e,2),t=n[0],r=n[1];return(0,ve.jsxs)("div",{style:{marginBottom:4},children:[(0,ve.jsxs)("span",{style:{fontWeight:"bold"},children:[t,":"]})," ",String(r)]},t)}))})]})]})});(0,F.useEffect)((function(){if(console.log("🚀 ~ 监听到状态变化 ~ selectedKnowledgeBaseIds:",Wn),console.log("🚀 ~ 监听到状态变化 ~ availableKnowledgeBases:",Bn),Wn.length>0&&Bn.length>0){var e=Wn.filter((function(e){return!Bn.some((function(n){return n._id===e}))}));e.length>0&&console.warn("🚀 ~ 警告：有选中的知识库ID在可用知识库列表中找不到:",e)}}),[Wn,Bn]),(0,F.useEffect)((function(){var e=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Wn.length>0&&(console.log("🚀 ~ 已选知识库IDs:",Wn),0===Bn.length&&console.log("🚀 ~ 可用知识库列表为空，但我们期望它在初始化时已被填充"));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[Ne,Wn]),(0,F.useEffect)((function(){var e=null;return ge&&ge.some((function(e){return"pending"===e.processing_status||"processing"===e.processing_status}))&&Ne&&(e=setInterval(u()(c()().mark((function n(){var t,r,o;return c()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(console.log("检查文件处理状态..."),n.prev=1,!Ne){n.next=7;break}return n.next=5,he({knowledge_base_id:Ne,pageSize:100});case 5:(t=n.sent)&&t.success&&Array.isArray(t.data)&&(r=t.data.map((function(e){return{uid:e.id,name:e.filename,status:"completed"===e.processing_status?"done":"error"===e.processing_status?"error":"uploading",url:e.url,size:e.size,type:e.data_type,created_at:e.created_at,chunk_count:e.chunk_count,processing_status:e.processing_status}})),o=r.every((function(e){return"completed"===e.processing_status||"error"===e.processing_status})),me(r),o&&e&&(console.log("所有文件处理完成，停止检查"),clearInterval(e),e=null));case 7:n.next=12;break;case 9:n.prev=9,n.t0=n.catch(1),console.error("检查文件处理状态出错:",n.t0);case 12:case"end":return n.stop()}}),n,null,[[1,9]])}))),1e4)),function(){e&&clearInterval(e)}}),[ge,Ne]);var Wt=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(V.current){e.next=3;break}return z.ZP.warning("没有活跃的对话"),e.abrupt("return");case 3:try{I.Z.confirm({title:"确认删除",content:"确定要删除此文件吗？删除后将无法恢复，且会影响相关的知识库索引。",okText:"确认删除",okType:"danger",cancelText:"取消",onOk:function(){var e=u()(c()().mark((function e(){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ue.Rj)(n);case 3:(t=e.sent)&&t.success?(z.ZP.success("文件删除成功"),me((function(e){return e.filter((function(e){return e.uid!==n}))})),V.current&&Un(V.current)):z.ZP.error("文件删除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除文件失败:",e.t0),z.ZP.error("删除文件失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()})}catch(e){console.error("删除文件失败:",e),z.ZP.error("删除文件失败")}case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();return(0,ve.jsx)(we.Provider,{value:{expandedRefs:t,setExpandedRefs:r},children:(0,ve.jsxs)("div",{className:Y.layout,style:{height:U-56},children:[(0,ve.jsxs)("div",{className:Y.chat,children:[(0,ve.jsx)(S.Z.List,{roles:{assistant:{placement:"start",typing:{step:5,interval:20},style:{maxWidth:600}}},items:_t,className:Y.messages}),(0,ve.jsx)(j.Z,{items:Se,onItemClick:kt}),(0,ve.jsx)(w.Z,{ref:Te,header:zt,prefix:(0,ve.jsx)(P.ZP,{type:"text",icon:(0,ve.jsx)(A.Z,{}),onClick:function(){Pt(!Ct)}}),onPasteFile:function(e){var n;null===(n=ye.current)||void 0===n||n.upload(e),Pt(!0)},onSubmit:bt,value:We,onChange:Ee,loading:tt.isRequesting(),className:Y.sender,placeholder:"请输入...",onKeyPress:function(){},onFocus:function(){},onBlur:function(){}})]}),(0,ve.jsx)("div",{children:(0,ve.jsx)(ke,{width:450,style:{background:"#fff",padding:"12px",borderLeft:"1px solid #d9d9d9",height:"100%",overflow:"auto"},children:(0,ve.jsxs)("div",{style:{padding:"8px 0"},children:[(0,ve.jsx)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:(0,ve.jsx)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333"},children:"知识资源"})}),(0,ve.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,ve.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,ve.jsx)("span",{children:"已选知识库"}),(0,ve.jsx)(P.ZP,{type:"primary",ghost:!0,size:"small",icon:(0,ve.jsx)(J.Z,{}),onClick:u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{tn(!0),Hn(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),Hn(!1)}finally{tn(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"设置知识库"})]}),Wn&&0!==Wn.length?(0,ve.jsx)("div",{children:(0,ve.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px"},children:Wn.map((function(e){var n=Bn.find((function(n){return n.id===e||n._id===e}));return(0,ve.jsx)("div",{style:{width:"calc(50% - 5px)",padding:"10px",borderRadius:"8px",border:"1px solid #e6f7ff",backgroundColor:"#f0f8ff",boxSizing:"border-box",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:(0,ve.jsx)("span",{style:{fontWeight:500,fontSize:"14px",color:"#1890ff",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:(null==n?void 0:n.name)||"未知知识库"})},e)}))})}):(0,ve.jsxs)("div",{style:{textAlign:"center",padding:"32px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,ve.jsx)(N.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,ve.jsx)("div",{style:{marginBottom:"16px"},children:"暂未选择知识库"}),(0,ve.jsx)(P.ZP,{type:"primary",size:"small",onClick:u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{tn(!0),Hn(!0)}catch(e){console.error("加载知识库数据失败:",e),z.ZP.error("加载知识库数据失败"),Hn(!1)}finally{tn(!1)}case 1:case"end":return e.stop()}}),e)}))),children:"选择知识库"})]})]}),ge&&ge.length>0&&(0,ve.jsxs)("div",{style:{marginTop:"24px"},children:[(0,ve.jsxs)("div",{style:{fontWeight:500,fontSize:"16px",color:"#333",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #f0f0f0",paddingBottom:"12px"},children:[(0,ve.jsx)("span",{children:"上传文件"}),(0,ve.jsx)(P.ZP,{type:"primary",ghost:!0,icon:(0,ve.jsx)(se.Z,{}),size:"small",onClick:function(){Ne&&Un(Ne)},children:"刷新状态"})]}),(0,ve.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:"12px",backgroundColor:"#f9f9f9",padding:"12px",borderRadius:"8px"},children:[(0,ve.jsx)("div",{style:{marginBottom:"8px",fontWeight:500},children:"支持的文件类型：PDF、Word、Excel、CSV、PPT、HTML、Markdown、JSON、图片(JPG/PNG)、音频(WAV/MP3)"}),(0,ve.jsx)("div",{style:{marginTop:"8px",color:"#ff7875"},children:"注意：文件上传后需要处理完成才会被检索到，请耐心等待"})]}),ge&&0!==ge.length?(console.log("渲染文件列表:",ge),console.log("上传进度状态:",Gn),(0,ve.jsx)(K.Z,{dataSource:ge,size:"small",itemLayout:"horizontal",renderItem:function(e){var n=Gn[e.uid];console.log("文件 ".concat(e.name," (").concat(e.uid,") 的上传进度:"),n);var t=e.processing_status;return"string"==typeof t&&(t="completed"===t?1:"error"===t?3:"processing"===t?2:"uploading"===t?4:0),console.log("文件 ".concat(e.name," 的处理状态:"),t,e.processing_status),(0,ve.jsx)(K.Z.Item,{style:{marginBottom:"10px",padding:"12px 16px",borderRadius:"8px",border:"1px solid #eee",backgroundColor:"#fafafa",transition:"all 0.3s",boxShadow:"0 1px 2px rgba(0,0,0,0.03)"},actions:[(0,ve.jsx)(P.ZP,{type:"link",size:"small",danger:!0,icon:(0,ve.jsx)(G.Z,{}),onClick:function(){return Wt(e.uid)},disabled:2===t},"delete")],children:(0,ve.jsx)(K.Z.Item.Meta,{title:(0,ve.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"4px"},children:[(0,ve.jsx)("span",{style:{fontWeight:500,fontSize:"14px",maxWidth:"260px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name}),e.size&&(0,ve.jsxs)("div",{style:{fontSize:"12px",color:"#999",marginBottom:"4px"},children:[(e.size/1024).toFixed(2)," KB"]})]}),description:(0,ve.jsx)("div",{children:n&&"uploading"===n.status?(0,ve.jsxs)("div",{children:[(0,ve.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px"},children:[(0,ve.jsx)("span",{children:"上传中..."}),(0,ve.jsxs)("span",{children:[n.percent,"%"]})]}),(0,ve.jsx)(M.Z,{percent:n.percent,size:"small",status:"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"},showInfo:!1})]}):n&&"error"===n.status?(0,ve.jsxs)("div",{children:[(0,ve.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#ff4d4f"},children:[(0,ve.jsx)("span",{children:"上传失败"}),(0,ve.jsx)("span",{children:"请重试"})]}),(0,ve.jsx)(M.Z,{percent:100,size:"small",status:"exception",showInfo:!1})]}):n&&"done"===n.status?(0,ve.jsxs)("div",{children:[(0,ve.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",marginBottom:"2px",color:"#52c41a"},children:[(0,ve.jsx)("span",{children:"上传完成"}),(0,ve.jsx)("span",{children:"100%"})]}),(0,ve.jsx)(M.Z,{percent:100,size:"small",status:"success",showInfo:!1})]}):1===t?(0,ve.jsx)("div",{style:{fontSize:"12px",color:"#52c41a"},children:"处理完成"}):3===t?(0,ve.jsx)("div",{style:{fontSize:"12px",color:"#f5222d"},children:"处理失败"}):2===t?(0,ve.jsxs)("div",{style:{fontSize:"12px",color:"#1890ff",display:"flex",alignItems:"center"},children:[(0,ve.jsx)(se.Z,{spin:!0,style:{marginRight:"5px"}}),"处理中..."]}):(0,ve.jsx)("div",{style:{fontSize:"12px",color:"#faad14"},children:"等待处理"})})})})}})):(0,ve.jsxs)("div",{style:{textAlign:"center",padding:"24px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px dashed #d9d9d9"},children:[(0,ve.jsx)(O.Z,{style:{fontSize:"36px",marginBottom:"16px",color:"#1890ff"}}),(0,ve.jsx)("div",{children:"暂无上传文件"})]})]})]})})}),Bt,Tt,Rt,(0,ve.jsx)(Dt,{isOpen:Fn,onClose:function(){return Hn(!1)},availableKnowledgeBases:Bn,selectedKnowledgeBaseIds:Wn,setSelectedKnowledgeBaseIds:En}),(0,ve.jsx)(de.Z,{visible:wn,messageId:Cn,conversationId:Ne,appInfo:ze,onClose:function(){return _n(!1)}})]})})}}}]);