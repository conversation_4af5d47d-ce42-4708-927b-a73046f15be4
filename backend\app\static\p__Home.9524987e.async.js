(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4371],{71255:function(n,e,t){"use strict";t.d(e,{Z:function(){return r}});var a=t(1413),s=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}},{tag:"path",attrs:{d:"M894 345a343.92 343.92 0 00-189-130v.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4a31.95 31.95 0 0040.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6 17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5-99 31-1-104-8-9c-84.6-103.2-90.2-251.9-11-361 96.4-132.2 281.2-161.4 413-66 132.2 96.1 161.5 280.6 66 412-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10 1 104-98-33-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1A367.31 367.31 0 00729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62 72.6 99.6 68.5 235.2-8 330z"}},{tag:"path",attrs:{d:"M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}}]},name:"comment",theme:"outlined"},o=t(91146),i=function(n,e){return s.createElement(o.Z,(0,a.Z)((0,a.Z)({},n),{},{ref:e,icon:c}))};var r=s.forwardRef(i)},50228:function(n,e,t){"use strict";t.d(e,{Z:function(){return r}});var a=t(1413),s=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"robot",theme:"outlined"},o=t(91146),i=function(n,e){return s.createElement(o.Z,(0,a.Z)((0,a.Z)({},n),{},{ref:e,icon:c}))};var r=s.forwardRef(i)},55355:function(n,e,t){"use strict";t.d(e,{Z:function(){return r}});var a=t(1413),s=t(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"},o=t(91146),i=function(n,e){return s.createElement(o.Z,(0,a.Z)((0,a.Z)({},n),{},{ref:e,icon:c}))};var r=s.forwardRef(i)},69044:function(n,e,t){"use strict";t.d(e,{CW:function(){return z},F3:function(){return T},Nq:function(){return A},Rd:function(){return N},Rf:function(){return m},Rp:function(){return C},_d:function(){return P},az:function(){return v},cY:function(){return G},cn:function(){return g},h8:function(){return w},iE:function(){return I},jA:function(){return k},mD:function(){return R},ul:function(){return j},w1:function(){return K},wG:function(){return Q}});var a=t(5574),s=t.n(a),c=t(97857),o=t.n(c),i=t(15009),r=t.n(i),l=t(99289),p=t.n(l),u=t(78158);function m(n){return d.apply(this,arguments)}function d(){return(d=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/users",{method:"GET",params:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function g(n){return h.apply(this,arguments)}function h(){return(h=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/users",{method:"POST",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function A(n){return f.apply(this,arguments)}function f(){return(f=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/users/".concat(e.id),{method:"PUT",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function w(n){return M.apply(this,arguments)}function M(){return(M=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/users/".concat(e),{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function v(n,e){return y.apply(this,arguments)}function y(){return(y=p()(r()().mark((function n(e,t){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/users/changeStatus",{method:"POST",data:{id:e,status:t}}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function k(n){return x.apply(this,arguments)}function x(){return(x=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/groups",{method:"GET",params:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function C(n){return b.apply(this,arguments)}function b(){return(b=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/groups",{method:"POST",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function R(n){return L.apply(this,arguments)}function L(){return(L=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/groups/".concat(e.id),{method:"PUT",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function I(n,e){return S.apply(this,arguments)}function S(){return(S=p()(r()().mark((function n(e,t){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/groups/".concat(e),o()({method:"DELETE"},t)));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function T(n){return Z.apply(this,arguments)}function Z(){return(Z=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/roles",{method:"GET",params:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function P(n){return E.apply(this,arguments)}function E(){return(E=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/roles",{method:"POST",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function j(n,e){return D.apply(this,arguments)}function D(){return(D=p()(r()().mark((function n(e,t){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/roles/".concat(e),{method:"PUT",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function N(n){return O.apply(this,arguments)}function O(){return(O=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/roles/".concat(e),{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function z(){return B.apply(this,arguments)}function B(){return(B=p()(r()().mark((function n(){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function G(n){return W.apply(this,arguments)}function W(){return(W=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/roles/".concat(e),{method:"GET"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function K(n){return U.apply(this,arguments)}function U(){return(U=p()(r()().mark((function n(e){var t;return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t=new URLSearchParams,Object.entries(e).forEach((function(n){var e=s()(n,2),a=e[0],c=e[1];t.append(a,String(c))})),n.abrupt("return",(0,u.N)("/api/system/config?".concat(t.toString()),{method:"POST"}));case 3:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function Q(n){return F.apply(this,arguments)}function F(){return(F=p()(r()().mark((function n(e){return r()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,u.N)("/api/useActiveCases",{method:"GET",params:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}},37077:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return vn}});var a=t(19632),s=t.n(a),c=t(15009),o=t.n(c),i=t(99289),r=t.n(i),l=t(5574),p=t.n(l),u=t(68400),m=t.n(u),d=t(24444),g=t(67294),h=t(35312),A=[{path:"/user",layout:!1,routes:[{name:"login",path:"/user/login",component:"./User/Login"}]},{path:"/home",name:"home",icon:"smile",component:"./Home"},{path:"/useCases",name:"useCases",icon:"profile",component:"./UseCases",access:"canAccessUseCases"},{path:"/model-management",name:"modelManagement",icon:"appstore",access:"canAccessModelManagement",routes:[{path:"/model-management/llm",name:"largeLanguageModel",component:"./ModelManagement/llm",access:"canAccessLargeLanguageModel"},{path:"/model-management/embedding",name:"embeddingModel",component:"./ModelManagement/embedding",access:"canAccessEmbeddingModel"},{path:"/model-management/rerank",name:"rerankModel",component:"./ModelManagement/rerank",access:"canAccessRerankModel"},{path:"/model-management/modelAuthorization",name:"modelAuthorization",component:"./ModelManagement/modelAuthorization",access:"canAccessModelAuthorization"}]},{name:"knowledgeManagement",icon:"book",path:"/knowledgeManagement",access:"canAccessKnowledgeManagement",routes:[{name:"knowledgeBase",path:"/knowledgeManagement/knowledgeBase",component:"./KnowledgeManagement/KnowledgeBase",access:"canAccessKnowledgeBase"},{name:"knowledgeInfo",path:"/knowledgeManagement/knowledgeInfo",component:"./KnowledgeManagement/KnowledgeInfo",hideInMenu:!0,access:"canAccessKnowledgeInfo"},{name:"fileInfo",path:"/knowledgeManagement/fileInfo",component:"./KnowledgeManagement/FileInfo",hideInMenu:!0,access:"canAccessFileInfo"},{name:"knowledgeDashboard",path:"/knowledgeManagement/knowledgeDashboard",component:"./KnowledgeManagement/KnowledgeDashboard",access:"canAccessKnowledgeDashboard"},{name:"knowledgeQuestionAnswer",path:"/knowledgeManagement/knowledgeQuestionAnswer",component:"./KnowledgeManagement/KnowledgeQuestionAnswer",access:"canAccessKnowledgeQuestionAnswer"}]},{name:"promotBase",icon:"book",path:"/promptManagement",access:"canAccessPromotBase",component:"./PromptManagement/PromptBase"},{name:"mcpSquare",icon:"apartment",path:"/mcpSquare",access:"canAccessMCPSquare",component:"./MCPSquare"},{name:"FlowManagement",icon:"apartment",path:"/FlowManagement",access:"canAccessFlowManagement",routes:[{name:"wiseflow",path:"/FlowManagement/wiseflow",component:"./FlowManagement/wiseflow",access:"canAccessWiseflow"},{name:"talklist",path:"/FlowManagement/talklist",component:"./FlowManagement/talklist",access:"canAccessTalklist"}]},{name:"LLMmarket",icon:"table",path:"/LLMmarket",access:"canAccessLLMmarket",routes:[{name:"llmModels",path:"/LLMmarket/llmModels",component:"./LLMmarket/llmModels",access:"canAccessLLMmarketModels"},{name:"llmChat",path:"/LLMmarket/llmChat",component:"./LLMmarket/llmChat",access:"canAccessLLMmarketLlmChat"},{name:"llmComparison",path:"/LLMmarket/llmComparison",component:"./LLMmarket/llmComparison",access:"canAccessLLMmarketLlmComparison"}]},{name:"LMTools",icon:"tool",path:"/LMTools",access:"canAccessLMTools",routes:[{name:"llmodelTuning",path:"/LMTools/llmodelTuning",component:"./LMTools/LLModelTuning",access:"canAccessLLModelTuning"},{name:"llmodelEvaluation",path:"/LMTools/llmodelEvaluation",component:"./LMTools/LLModelEvaluation",access:"canAccessModelEvaluation"}]},{path:"/",redirect:"/home"},{path:"*",layout:!1,component:"./404"},{path:"/dataset",name:"dataset",icon:"database",access:"canAccessDataset",routes:[{path:"/dataset/structured",name:"结构化数据",component:"./Dataset/StructuredData",access:"canAccessStructuredData"},{path:"/dataset/unstructured",name:"非结构化数据",component:"./Dataset/UnstructuredData",access:"canAccessUnstructuredData"},{path:"/dataset/structured/new",name:"新建结构化数据集",component:"./Dataset/NewStructuredData",hideInMenu:!0,access:"canCreateStructuredData"},{path:"/dataset/unstructured/new",name:"新建非结构化数据",component:"./Dataset/NewUnstructuredData",hideInMenu:!0,access:"canCreateUnstructuredData"},{path:"/dataset/structured/view/:id",name:"查看结构化数据集",component:"./Dataset/ViewStructuredData",hideInMenu:!0,access:"canViewStructuredData"},{path:"/dataset/unstructured/view/:id",name:"查看非结构化数据",component:"./Dataset/ViewUnstructuredData",hideInMenu:!0,access:"canViewUnstructuredData"}]},{name:"complianceAssistant",icon:"safety",path:"/complianceAssistant",access:"canAccessComplianceAssistant",routes:[{name:"metasploitAssistant",path:"/complianceAssistant/metasploitAssistant",component:"./ComplianceAssistant/MetasploitAssistant",access:"canAccessMetasploitAssistant"},{name:"complianceQA",path:"/complianceAssistant/complianceQA",component:"./ComplianceAssistant/ComplianceQA",access:"canAccessComplianceQA"},{name:"complianceQA_EU",path:"/complianceAssistant/complianceQA_EU",component:"./ComplianceAssistant/ComplianceQA_EU",access:"canAccessComplianceQA_EU"},{name:"caseAnalysis",path:"/complianceAssistant/caseAnalysis",component:"./ComplianceAssistant/CaseAnalysis",access:"canAccessCaseAnalysis"},{name:"LitigationCase",path:"/complianceAssistant/LitigationCase",component:"./ComplianceAssistant/LitigationCase",access:"canAccessLitigationCase"},{name:"informationRetrieval",path:"/complianceAssistant/informationRetrieval",component:"./ComplianceAssistant/InformationRetrieval",access:"canAccessInformationRetrieval"},{name:"fileAudit",path:"/complianceAssistant/fileAudit",component:"./ComplianceAssistant/FileAudit",access:"canAccessFileAudit"}]},{name:"fileAuditAssistant",icon:"folder",path:"/fileAuditAssistant",access:"canAccessFileAuditAssistant",routes:[{name:"offlineAuditTask",path:"/fileAuditAssistant/offlineAuditTask",component:"./FileAuditAssistant/OfflineAuditTask",hideInMenu:!0,access:"canAccessOfflineAuditTask"},{name:"offlineAuditTaskList",path:"/fileAuditAssistant/offlineAuditTaskList",component:"./FileAuditAssistant/OfflineAuditTaskList",access:"canAccessOfflineAuditTaskList"},{name:"documentReviewCenter",path:"/fileAuditAssistant/documentReviewCenter",component:"./FileAuditAssistant/DocumentReviewCenter",access:"canAccessDocumentReviewCenter"}]},{name:"WebInfoAssistant",icon:"global",path:"/webInfoAssistant",access:"canAccessWebInfoAssistant",routes:[{name:"DiplomaticIntelligentSearch",path:"/webInfoAssistant/diplomaticIntelligentSearch",component:"./WebInfoAssistant/DiplomaticIntelligentSearch",access:"canAccessDiplomaticIntelligentSearch"},{name:"FinancialReputationRisk",path:"/webInfoAssistant/financialReputationRisk",component:"./WebInfoAssistant/FinancialReputationRisk",access:"canAccessFinancialReputationRisk"},{name:"PolicyTrackingTool",path:"/webInfoAssistant/policyTrackingTool",component:"./WebInfoAssistant/PolicyTrackingTool",access:"canAccessPolicyTrackingTool"},{name:"ReputationRiskCase",path:"/webInfoAssistant/reputationRiskCase",component:"./WebInfoAssistant/ReputationRiskCase",access:"canAccessReputationRiskCase"}]},{name:"aiContentRecognition",icon:"eye",path:"/aiContentRecognition",access:"canAccessAiContentRecognition",routes:[{name:"ocrRecognition",path:"/aiContentRecognition/ocrRecognition",component:"./AiContentRecognition/OcrRecognition",access:"canAccessOcrRecognition"},{name:"layoutRecognition",path:"/aiContentRecognition/layoutRecognition",component:"./AiContentRecognition/LayoutRecognition",access:"canAccessLayoutRecognition"},{name:"endToEndRecognition",path:"/aiContentRecognition/endToEndRecognition",component:"./AiContentRecognition/EndToEndRecognition",access:"canAccessEndToEndRecognition"},{name:"multimodalRecognition",path:"/aiContentRecognition/multimodalRecognition",component:"./AiContentRecognition/MultimodalRecognition",access:"canAccessMultimodalRecognition"}]},{name:"MediaInsightsReport",icon:"eye",path:"/MediaInsightsReport",component:"./MediaInsightsReport",access:"canAccessMediaInsightsReport"},{name:"MediaInsightsReportManagement",icon:"eye",path:"/MediaInsightsReportManagement",component:"./MediaInsightsReport/MediaInsightsReportManagement",access:"canAccessMediaInsightsReportManagement"},{name:"ComplaintQuestionAnswer",icon:"insuranceOutlined",path:"/consumerProtection/ComplaintQuestionAnswer",component:"./ConsumerProtection/ComplaintQuestionAnswer",access:"ComplaintQuestionAnswer"},{name:"IntelligentReview",icon:"insuranceOutlined",path:"/consumerProtection/IntelligentReview",component:"./ConsumerProtection/IntelligentReview",access:"canAccessIntelligentReview"},{name:"KnowledgeCenter",icon:"insuranceOutlined",path:"/consumerProtection/KnowledgeCenter",component:"./ConsumerProtection/KnowledgeCenter",access:"canAccessKnowledgeCenter"},{name:"ComplaintAnalysis",icon:"insuranceOutlined",path:"/consumerProtection/ComplaintAnalysis",component:"./ConsumerProtection/ComplaintAnalysis",access:"canAccessComplaintAnalysis"},{name:"BusinessOpportunity",icon:"bulb",path:"/businessOpportunity",access:"canAccessBusinessOpportunityAssistant",routes:[{name:"BusinessOpportunityAssistant",path:"/businessOpportunity/assistant",component:"./BusinessOpportunity/Assistant",access:"canAccessBusinessOpportunityAssistant"},{name:"AssistantSettings",path:"/businessOpportunity/assistantSettings",component:"./BusinessOpportunity/AssistantSettings",access:"canAccessAssistantSettings"}]},{name:"BidAssistant",icon:"fileText",path:"/bidAssistant",access:"canAccessBidAssistant",routes:[{name:"Qualification",path:"/bidAssistant/qualification",component:"./BidAssistant/Qualification",access:"canAccessQualification"},{name:"ContractCase",path:"/bidAssistant/contractCase",component:"./BidAssistant/ContractCase",access:"canAccessContractCase"}]},{name:"SolutionAssistant",icon:"solution",path:"/solutionAssistant",access:"canAccessSolutionAssistant",routes:[{name:"SolutionGeneration",path:"/solutionAssistant/generation",component:"./SolutionAssistant/Generation",access:"canAccessSolutionGeneration"},{name:"HistorySolution",path:"/solutionAssistant/history",component:"./SolutionAssistant/History",access:"canAccessHistorySolution"},{name:"SolutionRetrieval",path:"/solutionAssistant/retrieval",component:"./SolutionAssistant/Retrieval",access:"canAccessSolutionRetrieval"}]},{name:"NegotiationAssistant",icon:"solution",path:"/negotiationAssistant",access:"canAccessNegotiationAssistant",routes:[{name:"DocumentGeneration",path:"/negotiationAssistant/documentGeneration",component:"./NegotiationAssistant/DocumentGeneration",access:"canAccessDocumentGeneration"},{name:"StrategyManagement",path:"/negotiationAssistant/strategyManagement",component:"./NegotiationAssistant/StrategyManagement",access:"canAccessStrategyManagement"}]},{name:"customerAssistant",icon:"customerService",path:"/customerAssistant",access:"canAccessCustomerAssistant",routes:[{name:"customerServiceBot",path:"/customerAssistant/customerServiceBot",component:"./CustomerAssistant/CustomerServiceBot",access:"canAccessCustomerServiceBot"},{name:"customerKnowledgeAssistant",path:"/customerAssistant/customerKnowledgeAssistant",component:"./CustomerAssistant/CustomerKnowledgeAssistant",access:"canAccessCustomerKnowledgeAssistant"},{name:"workOrderAssistant",path:"/customerAssistant/workOrderAssistant",component:"./CustomerAssistant/WorkOrderAssistant",access:"canAccessWorkOrderAssistant"},{name:"scriptMining",path:"/customerAssistant/scriptMining",component:"./CustomerAssistant/ScriptMining",access:"canAccessScriptMining"},{name:"ITIncidentReporting",path:"/customerAssistant/WorkOrderAssistant/ITIncidentReporting",component:"./CustomerAssistant/WorkOrderAssistant/ITIncidentReporting",access:"canAccessITIncidentReporting"},{name:"PublicOpinionClsureReport",path:"/customerAssistant/WorkOrderAssistant/PublicOpinionClsureReport",component:"./CustomerAssistant/WorkOrderAssistant/PublicOpinionClsureReport",access:"canAccessPublicOpinionClsureReport"},{name:"PublicOpinionInitialReport",path:"/customerAssistant/WorkOrderAssistant/PublicOpinionInitialReport",component:"./CustomerAssistant/WorkOrderAssistant/PublicOpinionInitialReport",access:"canAccessPublicOpinionInitialReport"}]},{name:"marketingAssistantPage",icon:"safety",path:"/marketingAssistantPage",access:"canAccessMarketingAssistantPage",routes:[{name:"marketingQA",path:"/marketingAssistantPage/marketingQA",component:"./MarketingAssistantPage//MarketingQA",access:"canAccessMarketingQA"}]},{name:"IntelligentRetrieval",icon:"search",path:"/intelligentRetrieval",component:"./IntelligentRetrieval",access:"canAccessIntelligentRetrieval"},{name:"voiceAssistant",icon:"dashboard",path:"/voiceAssistant",access:"canAccessVoiceAssistant",routes:[{name:"realTimeVoiceRecognition",path:"/voiceAssistant/realTimeVoiceRecognition",component:"./VoiceAssistant/RealTimeVoiceRecognition",access:"canAccessRealTimeVoiceRecognition"}]},{name:"entityComplianceReview",icon:"solution",path:"/entityComplianceReview",access:"canAccessEntityComplianceReview",component:"./EntityComplianceReview"},{name:"preLoanAssistant",icon:"solution",path:"/preLoanAssistant",access:"canAccessPreLoanAssistant",routes:[{name:"customerInvestigation",path:"/preLoanAssistant/customerInvestigation",component:"./PreLoanAssistant/CustomerInvestigation",access:"canAccessCustomerInvestigation"},{name:"creditReport",path:"/preLoanAssistant/creditReport",component:"./PreLoanAssistant/CreditReport",access:"canAccessCreditReport"},{name:"aiReportGeneration",path:"/preLoanAssistant/aiReportGeneration",component:"./PreLoanAssistant/AiReportGeneration",access:"canAccessAiReportGeneration"},{name:"reportTemplateManagement",path:"/preLoanAssistant/reportTemplateManagement",component:"./PreLoanAssistant/ReportTemplateManagement",access:"canAccessReportTemplateManagement"},{name:"reportTaskManagement",path:"/preLoanAssistant/reportTaskManagement",component:"./PreLoanAssistant/ReportTaskManagement",access:"canAccessReportTaskManagement"},{name:"createReportTemplate",path:"/preLoanAssistant/createReportTemplate",component:"./PreLoanAssistant/CreateReportTemplate",access:"canAccessCreateReportTemplate"}]},{name:"midLoanAssistant",icon:"solution",path:"/midLoanAssistant",access:"canAccessMidLoanAssistant",routes:[{name:"contractReview",path:"/midLoanAssistant/contractReview",component:"./MidLoanAssistant/ContractReview",access:"canAccessContractReview"},{name:"loanMonitoring",path:"/midLoanAssistant/loanMonitoring",component:"./MidLoanAssistant/LoanMonitoring",access:"canAccessLoanMonitoring"}]},{name:"postLoanAssistant",icon:"alert",path:"/postLoanAssistant",access:"canAccessPostLoanAssistant",routes:[{name:"riskMonitoring",path:"/postLoanAssistant/riskMonitoring",component:"./PostLoanAssistant/RiskMonitoring",access:"canAccessRiskMonitoring"}]},{name:"dataAnalysisAssistant",icon:"lineChart",path:"/dataAnalysisAssistant",access:"canAccessDataAnalysisAssistant",routes:[{name:"dataExploration",path:"/dataAnalysisAssistant/dataExploration",component:"./DataAnalysisAssistant/DataExploration",access:"canAccessDataExploration"},{name:"statisticalAnalysis",path:"/dataAnalysisAssistant/statisticalAnalysis",component:"./DataAnalysisAssistant/StatisticalAnalysis",access:"canAccessStatisticalAnalysis"},{name:"visualAnalysis",path:"/dataAnalysisAssistant/visualAnalysis",component:"./DataAnalysisAssistant/VisualAnalysis",access:"canAccessVisualAnalysis"}]},{name:"marketingAssistant",icon:"shopping",path:"/marketingAssistant",access:"canAccessMarketingAssistant",routes:[{name:"copyWritingGeneration",path:"/marketingAssistant/copyWritingGeneration",component:"./MarketingAssistant/CopyWritingGeneration",access:"canAccessCopyWritingGeneration"},{name:"myCopyWriting",path:"/marketingAssistant/myCopyWriting",component:"./MarketingAssistant/MyCopywriting",access:"canAccessMyCopywriting"},{name:"copyWritingList",path:"/marketingAssistant/copyWritingList",component:"./MarketingAssistant/CopyWritingList",access:"canAccessCopyWritingList"}]},{path:"/admin",name:"admin",icon:"crown",access:"canAccessAdmin",routes:[{path:"/admin/user-management",name:"userManagement",component:"./Admin/UserManagement",access:"canAccessUserManagement"},{path:"/admin/role-management",name:"roleManagement",component:"./Admin/RoleManagement",access:"canAccessRoleManagement"},{path:"/admin/system-management",name:"systemManagement",component:"./Admin/SystemManagement",access:"canAccessSystemManagement"},{path:"/admin/group-management",name:"groupManagement",component:"./Admin/GroupManagement",access:"canAccessGroupManagement"},{path:"/admin/system-app-settings",name:"systemAppSettings",component:"./Admin/SystemAppSettings",access:"canAccessSystemAppSettings"},{path:"/admin/system-app-info",name:"systemAppInfo",component:"./Admin/SystemAppSettings/SystemAppInfo",hideInMenu:!0,access:"canViewSystemAppInfo"},{path:"/admin/conversation-management",name:"conversationManagement",component:"./Admin/ConversationManagement",access:"canAccessConversationManagement"},{path:"/admin/knowledge-base-management",name:"knowledgeBaseManagement",component:"./Admin/KnowledgeBaseManagement",access:"canAccessKnowledgeBaseManagement"},{path:"/admin/message-management",name:"messageManagement",component:"./Admin/MessageManagement",access:"canAccessMessageManagement"},{path:"/admin/prompt-management",name:"promptAdminManagement",component:"./Admin/PromptManagement",access:"canAccessPromptAdminManagement"},{path:"/admin/mcp-management",name:"mcpManagement",component:"./Admin/McpManagement",access:"canAccessMcpManagement"},{path:"/admin/useCases-management",name:"useCasesManagement",component:"./Admin/UseCasesManagement",access:"canAccessUseCasesManagement"},{path:"/admin/announcement-management",name:"announcementManagement",component:"./Admin/AnnouncementManagement",access:"canAccessAnnouncementManagement"},{path:"/admin/log-management",name:"logManagement",component:"./Admin/LogManagement",access:"canAccessLogManagement"}]},{name:"account",icon:"user",path:"/account",access:"canAccessAccount",routes:[{path:"/account",redirect:"/account/center"},{name:"center",icon:"smile",path:"/account/center",component:"./account/center",access:"canAccessCenter"},{name:"settings",icon:"smile",path:"/account/settings",component:"./account/settings",access:"canAccessSettings"}]}],f=t(69044),w=t(78404),M=t(40110),v=t(41156),y=t(1413),k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M894 462c30.9 0 43.8-39.7 18.7-58L530.8 126.2a31.81 31.81 0 00-37.6 0L111.3 404c-25.1 18.2-12.2 58 18.8 58H192v374h-72c-4.4 0-8 3.6-8 8v52c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-52c0-4.4-3.6-8-8-8h-72V462h62zM512 196.7l271.1 197.2H240.9L512 196.7zM264 462h117v374H264V462zm189 0h117v374H453V462zm307 374H642V462h118v374z"}}]},name:"bank",theme:"outlined"},x=t(91146),C=function(n,e){return g.createElement(x.Z,(0,y.Z)((0,y.Z)({},n),{},{ref:e,icon:k}))};var b,R,L,I,S,T,Z,P,E,j,D,N,O,z,B,G,W,K,U,Q,F,V=g.forwardRef(C),H=t(55355),_=t(90389),q=t(26522),Y=t(50228),$=t(43425),J=t(15360),X=t(71255),nn=t(2830),en=t(13520),tn=t(71471),an=t(55102),sn=t(78045),cn=t(42075),on=t(96074),rn=t(71230),ln=t(15746),pn=t(4393),un=t(32983),mn=t(85893),dn=tn.Z.Title,gn=tn.Z.Paragraph,hn=tn.Z.Text,An=an.Z.Search,fn=(0,d.kc)((function(n){var e=n.css;return{layout:e(b||(b=m()(["\n      width: 100%;\n      min-height: 100vh;\n      background: #ffffff;\n      font-family: AlibabaPuHuiTi, sans-serif;\n    "]))),container:e(R||(R=m()(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      width: 100%;\n      padding: 40px 24px;\n    "]))),header:e(L||(L=m()(["\n      text-align: center;\n      margin-bottom: 24px;\n      max-width: 800px;\n    "]))),introduction:e(I||(I=m()(["\n      text-align: left;\n      margin-bottom: 40px;\n      width: 100%;\n      max-width: 1200px;\n      padding: 24px 32px;\n      background: linear-gradient(to right, #e6f7ff, #1890ff05);\n      border-radius: 12px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n    "]))),searchContainer:e(S||(S=m()(["\n      width: 100%;\n      max-width: 600px;\n      margin-bottom: 32px;\n    "]))),cardGrid:e(T||(T=m()(["\n      width: 100%;\n      max-width: 1200px;\n    "]))),card:e(Z||(Z=m()(["\n      height: 100%;\n      transition: all 0.3s;\n      border-radius: 8px;\n      overflow: hidden;\n      cursor: pointer;\n      position: relative;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n      \n      &:hover {\n        transform: translateY(-6px);\n        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);\n        \n        .cardTitle {\n          color: #1890ff;\n        }\n      }\n      \n      .ant-card-meta-title {\n        margin-bottom: 4px;\n        & > a {\n          display: inline-block;\n          max-width: 100%;\n          color: #222222;\n        }\n      }\n      \n      .ant-card-meta-description {\n        height: 44px;\n        overflow: hidden;\n        line-height: 22px;\n      }\n    "]))),cardCover:e(P||(P=m()(["\n      height: 140px;\n      overflow: hidden;\n      background: linear-gradient(120deg, #e6f7ff, #1890ff15);\n      background-image: url('/static/imgs/cover/52.png');\n      background-size: cover;\n      background-position: center;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    "]))),cardIcon:e(E||(E=m()(["\n      font-size: 38px;\n      color: #1890ff;\n      position: absolute;\n      left: 16px;\n      top: 16px;\n    "]))),avatarContainer:e(j||(j=m()(["\n      padding: 16px;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      background-color: rgba(255, 255, 255, 0.8);\n      border-top-left-radius: 8px;\n      border-top-right-radius: 8px;\n      min-height: 72px;\n    "]))),avatarIcon:e(D||(D=m()(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 44px;\n      height: 44px;\n      border-radius: 8px;\n      background: linear-gradient(135deg, #e6f7ff, #1890ff15);\n      margin-bottom: 0;\n      font-size: 22px;\n      color: #1890ff;\n      transition: all 0.3s;\n      flex-shrink: 0;\n    "]))),cardStats:e(N||(N=m()(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 16px;\n      padding: 12px 16px;\n      border-top: 1px solid #f0f0f0;\n      font-size: 12px;\n      color: #888888;\n      background-color: rgba(255, 255, 255, 0.9);\n    "]))),cardTitle:e(O||(O=m()(["\n      font-weight: bold;\n      font-size: 16px;\n      color: #222222;\n      margin-bottom: 4px;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n    "]))),cardSubtitle:e(z||(z=m()(["\n      font-size: 12px;\n      color: #888888;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n    "]))),cardDescription:e(B||(B=m()(["\n      font-size: 14px;\n      color: #888888;\n      height: 42px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n      line-height: 21px;\n      padding: 0 16px;\n      background-color: rgba(255, 255, 255, 0.8);\n      \n      .ant-card-meta-description {\n        height: 42px;\n        overflow: hidden;\n        line-height: 21px;\n      }\n    "]))),cardGoIcon:e(G||(G=m()(["\n      opacity: 0;\n      transition: all 0.3s;\n      color: #1890ff;\n      margin-left: 4px;\n    "]))),cardBadge:e(W||(W=m()(["\n      position: absolute;\n      top: 12px;\n      right: 12px;\n    "]))),emptyResult:e(K||(K=m()(["\n      padding: 48px 0;\n      text-align: center;\n    "]))),divider:e(U||(U=m()(["\n      margin: 16px 0 32px;\n    "]))),categoryFilter:e(Q||(Q=m()(["\n      margin-bottom: 16px;\n    "]))),titleContainer:e(F||(F=m()(["\n      margin-left: 16px;\n      width: calc(100% - 60px);\n      overflow: hidden;\n      flex: 1;\n    "])))}})),wn=(0,w.kH)(),Mn=function(n,e){return Array.isArray(n)&&0!==n.length?function(n){var e=[];return Array.isArray(n)&&n.forEach((function n(t){t.routes&&0!==t.routes.length||!t.component||t.hideInMenu?t.routes&&t.routes.forEach(n):e.push(t)})),e}(n.filter((function(n){return!n.access||e[n.access]}))):[]},vn=function(){var n=fn().styles,e=(0,h.useNavigate)(),t=(0,h.useAccess)(),a=(0,g.useState)([]),c=p()(a,2),i=c[0],l=c[1],u=(0,g.useState)([]),m=p()(u,2),d=m[0],w=m[1],y=(0,g.useState)(""),k=p()(y,2),x=k[0],C=k[1],b=(0,g.useState)([]),R=p()(b,2),L=R[0],I=R[1],S=(0,h.useIntl)(),T=(0,g.useState)(!1),Z=p()(T,2),P=Z[0],E=Z[1],j=(0,g.useState)("all"),D=p()(j,2),N=D[0],O=D[1],z=(0,h.useModel)("@@initialState").initialState,B=(null==z?void 0:z.menuData)||[],G=function(n){var e,t,a=(e=n.path,(t=e.replace(/^\/|\/$/g,"").split("/"))[t.length-1].replace(/-([a-z])/g,(function(n){return n[1].toUpperCase()}))),s=S.formatMessage({id:a},void 0);return s===a?n.name:s},W=function(n){if(C(n),n.trim()){var e=n.toLowerCase(),t=i.filter((function(n){return G(n).toLowerCase().includes(e)||n.path.toLowerCase().includes(e)}));console.log("搜索词:",n,"匹配数量:",t.length,"匹配结果:",t),I(t)}else I(i)},K=function(){var n=r()(o()().mark((function n(){var e,t;return o()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,(0,f.CW)();case 3:(e=n.sent)&&e.success&&(t=[],function n(e){e&&e.forEach((function(e){(e.key.toLowerCase().includes("customer")||e.title.toLowerCase().includes("客户"))&&t.push(e.key),e.children&&e.children.length>0&&n(e.children)}))}(e.data),console.log("获取到的客户相关权限:",t),w(t)),n.next=10;break;case 7:n.prev=7,n.t0=n.catch(0),console.error("获取权限树出错:",n.t0);case 10:case"end":return n.stop()}}),n,null,[[0,7]])})));return function(){return n.apply(this,arguments)}}();(0,g.useEffect)((function(){K()}),[]),(0,g.useEffect)((function(){if(!P)try{if(Array.isArray(A)){var n=Mn(A,t),e=["home/*","home","login","settings","center","log-management","profile","basic","logManagement","announcementManagement","canAccessUserManagement","roleManagement","systemManagement","groupManagement","systemAppSettings","systemAppInfo","conversationManagement","messageManagement","useCasesManagement","knowledgeBaseManagement","mcpManagement","promptAdminManagement","useCasesManagement","knowledgeBaseManagement","mcpManagement","announcementManagement","logManagement","UseCases","llmodelTuning","llmodelEvaluation","largeLanguageModel","embeddingModel","rerankModel","modelAuthorization","knowledgeManagement","knowledgeBase","knowledgeInfo","fileInfo","knowledgeDashboard","user-management","userManagement","talklist","wiseflow","useCases"],a=n.filter((function(n){return!e.includes(n.name)&&(!!n.name&&(!n.hideInMenu&&(!(d.length>0)||(n.name.toLowerCase().includes("customer")||n.path.toLowerCase().includes("customer")||n.access&&d.some((function(e){var t;return null===(t=n.access)||void 0===t?void 0:t.toLowerCase().includes(e.toLowerCase())}))))))}));console.log("当前用户可访问的路由:",a),l(a),E(!0)}else console.error("routes不是一个数组:",A),l([]),E(!0);console.log("当前用户权限菜单:",t),console.log("当前系统菜单数据:",B)}catch(n){console.error("处理路由时出错:",n),l([]),E(!0)}}),[t,B,d,P]),(0,g.useEffect)((function(){var n=s()(i);if(x){var e=x.toLowerCase();n=n.filter((function(n){return G(n).toLowerCase().includes(e)||n.path.toLowerCase().includes(e)}))}"all"!==N&&(n=n.filter((function(n){var e=n.path.toLowerCase();switch(N){case"credit":return e.includes("loan")||e.includes("credit")||e.includes("finance");case"customer":return e.includes("customer")||e.includes("client");case"knowledge":return e.includes("knowledge")||e.includes("info");case"compliance":return e.includes("compliance")||e.includes("legal");default:return!0}}))),I(n)}),[i,x,N]);return(0,mn.jsx)("div",{className:n.layout,children:(0,mn.jsxs)("div",{className:n.container,children:[(0,mn.jsxs)("div",{className:n.introduction,style:{backgroundImage:"url(/static/imgs/cover/52.png)",backgroundSize:"cover",backgroundPosition:"center",padding:"20px",borderRadius:"8px"},children:[(0,mn.jsx)(dn,{style:{color:"#fff"},level:2,children:wn.title}),(0,mn.jsx)(gn,{style:{color:"#fff"},children:"WiseAgent 是一个强大的智能助手平台，提供多样化的AI解决方案，助力企业数字化转型。 集成了大语言模型、知识库管理、流程编排等功能，为各行业提供智能化服务支持。"}),(0,mn.jsx)(hn,{type:"secondary",children:"选择下方功能卡片，开始探索智能助手的能力。"})]}),(0,mn.jsx)("div",{className:n.searchContainer,children:(0,mn.jsx)(An,{placeholder:"搜索功能模块...",allowClear:!0,enterButton:(0,mn.jsx)(M.Z,{}),size:"large",onSearch:W,onChange:function(n){return W(n.target.value)},onClear:function(){C(""),I(i)},value:x})}),(0,mn.jsx)("div",{className:n.categoryFilter,children:(0,mn.jsxs)(sn.ZP.Group,{value:N,onChange:function(n){O(n.target.value)},buttonStyle:"solid",children:[(0,mn.jsx)(sn.ZP.Button,{value:"all",children:(0,mn.jsxs)(cn.Z,{children:[(0,mn.jsx)(v.Z,{}),"全部应用"]})}),(0,mn.jsx)(sn.ZP.Button,{value:"credit",children:(0,mn.jsxs)(cn.Z,{children:[(0,mn.jsx)(V,{}),"信贷应用"]})}),(0,mn.jsx)(sn.ZP.Button,{value:"customer",children:(0,mn.jsxs)(cn.Z,{children:[(0,mn.jsx)(H.Z,{}),"客户服务"]})}),(0,mn.jsx)(sn.ZP.Button,{value:"knowledge",children:(0,mn.jsxs)(cn.Z,{children:[(0,mn.jsx)(_.Z,{}),"知识管理"]})}),(0,mn.jsx)(sn.ZP.Button,{value:"compliance",children:(0,mn.jsxs)(cn.Z,{children:[(0,mn.jsx)(q.Z,{}),"合规应用"]})})]})}),x&&(0,mn.jsxs)(gn,{style:{marginBottom:16},children:["搜索结果: ",(0,mn.jsx)(hn,{strong:!0,children:L.length})," 个功能模块",(0,mn.jsxs)(hn,{type:"secondary",style:{marginLeft:8},children:['(搜索词: "',x,'")']})]}),x&&(0,mn.jsx)(on.Z,{className:n.divider}),(0,mn.jsx)("div",{className:n.cardGrid,children:L.length>0?(0,mn.jsx)(rn.Z,{gutter:[16,16],children:L.map((function(t){var a=G(t),s=v.Z,c=t.path.toLowerCase();return c.includes("knowledge")?s=_.Z:c.includes("customer")?s=H.Z:c.includes("agent")?s=Y.Z:c.includes("management")?s=$.Z:c.includes("file")?s=J.Z:c.includes("chat")?s=X.Z:c.includes("tool")?s=nn.Z:c.includes("data")&&(s=en.Z),(0,mn.jsx)(ln.Z,{xs:24,sm:12,md:8,lg:6,children:(0,mn.jsxs)(pn.Z,{hoverable:!0,className:n.card,onClick:function(){return e(t.path)},style:{backgroundImage:"url(/static/imgs/cover/51.png)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",position:"relative",overflow:"hidden"},bodyStyle:{padding:"0"},children:[(0,mn.jsxs)("div",{className:n.avatarContainer,children:[(0,mn.jsx)("div",{className:n.avatarIcon,children:(0,mn.jsx)(s,{})}),(0,mn.jsxs)("div",{className:n.titleContainer,children:[(0,mn.jsx)("div",{className:n.cardTitle,children:a}),(0,mn.jsx)("div",{className:n.cardSubtitle,children:c.split("/").pop()})]})]}),t.description&&(0,mn.jsx)(pn.Z.Meta,{title:null,description:(0,mn.jsx)(gn,{className:n.cardDescription,ellipsis:{rows:2},children:t.description})})]})},t.name)}))}):(0,mn.jsx)("div",{className:n.emptyResult,children:(0,mn.jsx)(un.Z,{image:un.Z.PRESENTED_IMAGE_SIMPLE,description:(0,mn.jsxs)("span",{children:["没有找到匹配的功能模块",x&&' "'.concat(x,'"')]})})})})]})})}},5273:function(n,e,t){"use strict";t.d(e,{Z:function(){return c}});var a=t(67294),s=t(75164);function c(n){const e=a.useRef(null),t=()=>{s.Z.cancel(e.current),e.current=null};return[()=>{t(),e.current=(0,s.Z)((()=>{e.current=null}))},a=>{e.current&&(a.stopPropagation(),t()),null==n||n(a)}]}},15746:function(n,e,t){"use strict";var a=t(21584);e.Z=a.Z},71230:function(n,e,t){"use strict";var a=t(17621);e.Z=a.Z},50132:function(n,e,t){"use strict";var a=t(87462),s=t(1413),c=t(4942),o=t(97685),i=t(91),r=t(93967),l=t.n(r),p=t(21770),u=t(67294),m=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],d=(0,u.forwardRef)((function(n,e){var t=n.prefixCls,r=void 0===t?"rc-checkbox":t,d=n.className,g=n.style,h=n.checked,A=n.disabled,f=n.defaultChecked,w=void 0!==f&&f,M=n.type,v=void 0===M?"checkbox":M,y=n.title,k=n.onChange,x=(0,i.Z)(n,m),C=(0,u.useRef)(null),b=(0,u.useRef)(null),R=(0,p.Z)(w,{value:h}),L=(0,o.Z)(R,2),I=L[0],S=L[1];(0,u.useImperativeHandle)(e,(function(){return{focus:function(n){var e;null===(e=C.current)||void 0===e||e.focus(n)},blur:function(){var n;null===(n=C.current)||void 0===n||n.blur()},input:C.current,nativeElement:b.current}}));var T=l()(r,d,(0,c.Z)((0,c.Z)({},"".concat(r,"-checked"),I),"".concat(r,"-disabled"),A));return u.createElement("span",{className:T,title:y,style:g,ref:b},u.createElement("input",(0,a.Z)({},x,{className:"".concat(r,"-input"),ref:C,onChange:function(e){A||("checked"in n||S(e.target.checked),null==k||k({target:(0,s.Z)((0,s.Z)({},n),{},{type:v,checked:e.target.checked}),stopPropagation:function(){e.stopPropagation()},preventDefault:function(){e.preventDefault()},nativeEvent:e.nativeEvent}))},disabled:A,checked:!!I,type:v})),u.createElement("span",{className:"".concat(r,"-inner")}))}));e.Z=d},68400:function(n){n.exports=function(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))},n.exports.__esModule=!0,n.exports.default=n.exports}}]);