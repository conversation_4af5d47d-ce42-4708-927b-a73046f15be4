"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2952],{27496:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},l=n(91146),c=function(e,t){return a.createElement(l.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(c)},21450:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(67294);var a=r.createContext({});const s={classNames:{},styles:{},className:"",style:{}};var l=e=>{const t=r.useContext(a);return r.useMemo((()=>({...s,...t[e]})),[t[e]])}},43495:function(e,t,n){n.d(t,{I$:function(){return g}});var r=n(83262),a=n(36158),s=n(11568),l=n(9361),c=n(29691),o=n(92372),i=n(67294);const d=(0,s.jG)(l.Z.defaultAlgorithm),f={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},u=(e,t,n)=>{const r=n.getDerivativeToken(e),{override:a,...s}=t;let l={...r,override:a};return l=(0,o.Z)(l),s&&Object.entries(s).forEach((([e,t])=>{const{theme:n,...r}=t;let a=r;n&&(a=u({...l,...r},{override:r},n)),l[e]=a})),l};function p(){const{token:e,hashed:t,theme:n=d,override:r,cssVar:a}=i.useContext(l.Z._internalContext),[o,p,g]=(0,s.fp)(n,[l.Z.defaultSeed,e],{salt:`1.4.0-${t||""}`,override:r,getComputedToken:u,cssVar:a&&{prefix:a.prefix,key:a.key,unitless:c.NJ,ignore:c.ID,preserve:f}});return[n,g,t?p:"",o,a]}const{genStyleHooks:g,genComponentStyleHook:h,genSubStyleComponent:x}=(0,r.rb)({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=(0,a.Z)();return{iconPrefixCls:t,rootPrefixCls:e()}},useToken:()=>{const[e,t,n,r,a]=p();return{theme:e,realToken:t,hashId:n,token:r,cssVar:a}},useCSP:()=>{const{csp:e}=(0,a.Z)();return e??{}},layer:{name:"antdx",dependencies:["antd"]}})},36158:function(e,t,n){var r=n(21532),a=n(67294);t.Z=function(){const{getPrefixCls:e,direction:t,csp:n,iconPrefixCls:s,theme:l}=a.useContext(r.ZP.ConfigContext);return{theme:l,getPrefixCls:e,direction:t,csp:n,iconPrefixCls:s}}},39455:function(e,t,n){n.r(t);var r=n(19632),a=n.n(r),s=n(5574),l=n.n(s),c=n(67294),o=n(87547),i=n(13520),d=n(56299),f=n(27496),u=n(76654),p=n(71471),g=n(86250),h=n(4393),x=n(72269),y=n(42075),m=n(83622),v=n(55102),j=n(85893),b=p.Z.Title,k=p.Z.Text,C={ai:{placement:"start",avatar:{icon:(0,j.jsx)(o.Z,{}),style:{background:"#fde3cf"}},typing:{step:5,interval:20},header:(0,j.jsx)("div",{style:{fontSize:16,fontWeight:600},children:"AI助手"}),style:{fontSize:16,maxWidth:600}},user:{placement:"start",avatar:{icon:(0,j.jsx)(o.Z,{}),style:{background:"#87d068"}},variant:"borderless",style:{width:600,fontSize:16,fontWeight:600}},dataAgent:{placement:"start",avatar:{icon:(0,j.jsx)(i.Z,{}),style:{background:"#91caff"}},typing:{step:4,interval:25},header:(0,j.jsx)("div",{style:{fontSize:20,fontWeight:600},children:"数据整理助手"}),variant:"borderless"},analysisAgent:{placement:"start",avatar:{icon:(0,j.jsx)(d.Z,{}),style:{background:"#d3adf7"}},typing:{step:3,interval:30},header:(0,j.jsx)("div",{style:{fontSize:20,fontWeight:600},children:"分析建议助手"}),variant:"borderless"}},Z=function(e,t){var n=function(e){return(0,j.jsxs)(g.Z,{children:["#",t,": ",e]})};switch(e.role){case"ai":return{placement:"start",avatar:{icon:(0,j.jsx)(o.Z,{}),style:{background:"#fde3cf"}},typing:{step:5,interval:20},header:(0,j.jsx)("div",{style:{fontSize:16,fontWeight:600},children:"AI助手"}),style:{maxWidth:600},messageRender:n};case"user":return{placement:"end",avatar:{icon:(0,j.jsx)(o.Z,{}),style:{background:"#87d068"}},style:{fontSize:16,fontWeight:600},messageRender:n};case"dataAgent":return{placement:"start",avatar:{icon:(0,j.jsx)(i.Z,{}),style:{background:"#91caff"}},typing:{step:4,interval:25},header:(0,j.jsx)("div",{style:{fontSize:16,fontWeight:600},children:"数据整理助手"}),style:{maxWidth:600,background:"#e6f4ff",borderColor:"#91caff"},messageRender:n};case"analysisAgent":return{placement:"start",avatar:{icon:(0,j.jsx)(d.Z,{}),style:{background:"#d3adf7"}},typing:{step:3,interval:30},header:(0,j.jsx)("div",{style:{fontSize:16,fontWeight:600},children:"分析建议助手"}),style:{maxWidth:600,background:"#f9f0ff",borderColor:"#d3adf7"},messageRender:n};default:return{messageRender:n}}};t.default=function(){var e=c.useState(!1),t=l()(e,2),n=t[0],r=t[1],s=c.useState(""),o=l()(s,2),i=o[0],d=o[1],p=c.useRef(null),S=c.useMemo((function(){return n?Z:C}),[n]),M=c.useState((function(){return Array.from({length:5}).map((function(e,t){var n=t%4,r="user",a="这是用户提出的问题示例。";return 1===n?(r="ai",a="这是AI回复的内容示例。 ".repeat(3)):2===n?(r="dataAgent",a="我是数据整理Agent，我正在整理相关数据：\n1. 数据项A: 示例值\n2. 数据项B: 示例值\n3. 数据项C: 示例值"):3===n&&(r="analysisAgent",a="我是分析建议Agent，根据以上数据，我的分析结果如下：\n- 关键发现：...\n- 建议措施：...\n- 数据趋势：..."),{key:t,role:r,content:a}}))})),w=l()(M,2),$=w[0],P=w[1],A=function(){if(i.trim()){var e=$.length>0?Math.max.apply(Math,a()($.map((function(e){return Number(e.key)}))))+1:0,t={key:e,role:"user",content:i},n={key:e+1,role:"ai",content:"我正在处理您的请求..."},r={key:e+2,role:"dataAgent",content:"我正在整理相关数据，请稍候..."},s={key:e+3,role:"analysisAgent",content:"根据数据，我将为您提供分析..."};P([].concat(a()($),[t,n,r,s])),d(""),setTimeout((function(){var t;null===(t=p.current)||void 0===t||t.scrollTo({key:e+3,block:"end"})}),100)}};return(0,j.jsx)(h.Z,{style:{height:"100%",background:"#f5f5f5"},children:(0,j.jsxs)(g.Z,{vertical:!0,gap:"middle",style:{height:"100%"},children:[(0,j.jsx)(b,{level:4,children:"资讯检索"}),(0,j.jsxs)(g.Z,{gap:"small",justify:"space-between",children:[(0,j.jsxs)(g.Z,{gap:"large",align:"center",children:[(0,j.jsx)(k,{children:"角色设置方式:"}),(0,j.jsx)(x.Z,{checked:n,onChange:function(e){return r(e)},checkedChildren:"函数",unCheckedChildren:"对象"})]}),(0,j.jsxs)(y.Z,{children:[(0,j.jsx)(m.ZP,{onClick:function(){var e=$.length>0?Math.max.apply(Math,a()($.map((function(e){return Number(e.key)}))))+1:0;P([].concat(a()($),[{key:e,role:"dataAgent",content:"这是一条新的数据整理信息。"}]))},children:"添加数据消息"}),(0,j.jsx)(m.ZP,{onClick:function(){var e;null===(e=p.current)||void 0===e||e.scrollTo({key:0,block:"nearest"})},children:"滚动到顶部"})]})]}),(0,j.jsx)(h.Z,{bodyStyle:{padding:"12px",height:"500px",overflow:"auto"},style:{flex:1,background:"#fff"},children:(0,j.jsx)(u.Z.List,{ref:p,roles:S,items:$})}),(0,j.jsxs)(g.Z,{gap:"small",children:[(0,j.jsx)(v.Z,{placeholder:"请输入您的问题...",value:i,onChange:function(e){return d(e.target.value)},onPressEnter:A,style:{flex:1}}),(0,j.jsx)(m.ZP,{type:"primary",icon:(0,j.jsx)(f.Z,{}),onClick:A,children:"发送"})]})]})})}},86250:function(e,t,n){n.d(t,{Z:function(){return C}});var r=n(67294),a=n(93967),s=n.n(a),l=n(98423),c=n(98065),o=n(53124),i=n(83559),d=n(83262);const f=["wrap","nowrap","wrap-reverse"],u=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],p=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"];var g=function(e,t){return s()(Object.assign(Object.assign(Object.assign({},((e,t)=>{const n=!0===t.wrap?"wrap":t.wrap;return{[`${e}-wrap-${n}`]:n&&f.includes(n)}})(e,t)),((e,t)=>{const n={};return p.forEach((r=>{n[`${e}-align-${r}`]=t.align===r})),n[`${e}-align-stretch`]=!t.align&&!!t.vertical,n})(e,t)),((e,t)=>{const n={};return u.forEach((r=>{n[`${e}-justify-${r}`]=t.justify===r})),n})(e,t)))};const h=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},x=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},y=e=>{const{componentCls:t}=e,n={};return f.forEach((e=>{n[`${t}-wrap-${e}`]={flexWrap:e}})),n},m=e=>{const{componentCls:t}=e,n={};return p.forEach((e=>{n[`${t}-align-${e}`]={alignItems:e}})),n},v=e=>{const{componentCls:t}=e,n={};return u.forEach((e=>{n[`${t}-justify-${e}`]={justifyContent:e}})),n};var j=(0,i.I$)("Flex",(e=>{const{paddingXS:t,padding:n,paddingLG:r}=e,a=(0,d.IX)(e,{flexGapSM:t,flexGap:n,flexGapLG:r});return[h(a),x(a),y(a),m(a),v(a)]}),(()=>({})),{resetStyle:!1}),b=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const k=r.forwardRef(((e,t)=>{const{prefixCls:n,rootClassName:a,className:i,style:d,flex:f,gap:u,children:p,vertical:h=!1,component:x="div"}=e,y=b(e,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:m,direction:v,getPrefixCls:k}=r.useContext(o.E_),C=k("flex",n),[Z,S,M]=j(C),w=null!=h?h:null==m?void 0:m.vertical,$=s()(i,a,null==m?void 0:m.className,C,S,M,g(C,e),{[`${C}-rtl`]:"rtl"===v,[`${C}-gap-${u}`]:(0,c.n)(u),[`${C}-vertical`]:w}),P=Object.assign(Object.assign({},null==m?void 0:m.style),d);return f&&(P.flex=f),u&&!(0,c.n)(u)&&(P.gap=u),Z(r.createElement(x,Object.assign({ref:t,className:$,style:P},(0,l.Z)(y,["justify","wrap","align"])),p))}));var C=k}}]);