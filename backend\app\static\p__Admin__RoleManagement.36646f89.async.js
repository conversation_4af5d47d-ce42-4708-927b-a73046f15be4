"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7535],{51042:function(e,r,n){var t=n(1413),a=n(67294),u=n(42110),s=n(91146),c=function(e,r){return a.createElement(s.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:u.Z}))},i=a.forwardRef(c);r.Z=i},5966:function(e,r,n){var t=n(97685),a=n(1413),u=n(91),s=n(21770),c=n(8232),i=n(55241),o=n(98423),p=n(67294),f=n(62633),l=n(85893),d=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],v="text",m=function(e){var r=(0,s.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),n=(0,t.Z)(r,2),u=n[0],o=n[1];return(0,l.jsx)(c.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(r){var n,t=r.getFieldValue(e.name||[]);return(0,l.jsx)(i.Z,(0,a.Z)((0,a.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return o(e)},content:(0,l.jsxs)("div",{style:{padding:"4px 0"},children:[null===(n=e.statusRender)||void 0===n?void 0:n.call(e,t),e.strengthText?(0,l.jsx)("div",{style:{marginTop:10},children:(0,l.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:u,children:e.children}))}})},y=function(e){var r=e.fieldProps,n=e.proFieldProps,t=(0,u.Z)(e,d);return(0,l.jsx)(f.Z,(0,a.Z)({valueType:v,fieldProps:r,filedConfig:{valueType:v},proFieldProps:n},t))};y.Password=function(e){var r=e.fieldProps,n=e.proFieldProps,s=(0,u.Z)(e,h),c=(0,p.useState)(!1),i=(0,t.Z)(c,2),d=i[0],y=i[1];return null!=r&&r.statusRender&&s.name?(0,l.jsx)(m,{name:s.name,statusRender:null==r?void 0:r.statusRender,popoverProps:null==r?void 0:r.popoverProps,strengthText:null==r?void 0:r.strengthText,open:d,onOpenChange:y,children:(0,l.jsx)("div",{children:(0,l.jsx)(f.Z,(0,a.Z)({valueType:"password",fieldProps:(0,a.Z)((0,a.Z)({},(0,o.Z)(r,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var n;null==r||null===(n=r.onBlur)||void 0===n||n.call(r,e),y(!1)},onClick:function(e){var n;null==r||null===(n=r.onClick)||void 0===n||n.call(r,e),y(!0)}}),proFieldProps:n,filedConfig:{valueType:v}},s))})}):(0,l.jsx)(f.Z,(0,a.Z)({valueType:"password",fieldProps:r,proFieldProps:n,filedConfig:{valueType:v}},s))},y.displayName="ProFormComponent",r.Z=y},27504:function(e,r,n){n.r(r),n.d(r,{default:function(){return S}});var t=n(9783),a=n.n(t),u=n(19632),s=n.n(u),c=n(15009),i=n.n(c),o=n(97857),p=n.n(o),f=n(99289),l=n.n(f),d=n(5574),h=n.n(d),v=n(67294),m=n(97131),y=n(12453),g=n(2453),x=n(17788),w=n(83622),k=n(51042),b=n(69044),Z=n(37476),P=n(5966),T=n(63496),M=n(85893),C=function(e){var r=e.formRef,n=e.onSubmit,t=e.modalVisible,u=e.values,s=e.treeData,c=e.checkedKeys,o=e.onTreeCheck,f=e.onCancel;return(0,M.jsxs)(Z.Y,{formRef:r,title:null!=u&&u.id?"编辑角色":"新建角色",visible:t,onVisibleChange:function(e){e||f()},onFinish:function(){var e=l()(i()().mark((function e(r){var t,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=p()(p()({},r),{},{id:null==u?void 0:u.id,access:c.reduce((function(e,r){return p()(p()({},e),{},a()({},r,!0))}),{})}),e.next=3,n(t);case 3:return s=e.sent,e.abrupt("return",s);case 5:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),initialValues:u,children:[(0,M.jsx)(P.Z,{name:"id",hidden:!0}),(0,M.jsx)(P.Z,{name:"name",label:"角色名称",placeholder:"请输入角色名称",rules:[{required:!0,message:"角色名称为必填项"}]}),(0,M.jsx)(P.Z,{name:"description",label:"描述",placeholder:"请输入角色描述"}),(0,M.jsx)(T.Z,{checkable:!0,onCheck:function(e){return o(e)},checkedKeys:c,treeData:s})]})},j=n(35312),S=function(){var e=(0,j.useIntl)(),r=(0,v.useState)(!1),n=h()(r,2),t=n[0],u=n[1],c=(0,v.useState)(!1),o=h()(c,2),f=o[0],d=o[1],Z=(0,v.useState)(void 0),P=h()(Z,2),T=P[0],S=P[1],E=(0,v.useState)([]),N=h()(E,2),R=N[0],A=N[1],K=(0,v.useRef)(),F=(0,v.useRef)(),O=(0,v.useState)([]),D=h()(O,2),I=D[0],_=D[1],G=(0,v.useState)({}),L=h()(G,2),V=L[0],W=L[1],U=(0,v.useState)({}),q=h()(U,2),z=q[0],B=q[1],Y=function e(r,n,t){r.forEach((function(r){var a=r.key,u=r.children;n&&(V[n]||(V[n]=[]),V[n].push(a));var s=void 0;if(a.startsWith("canAccess")&&!n){var c=a.replace("canAccess","").charAt(0).toLowerCase()+a.replace("canAccess","").slice(1);s="menu.".concat(c)}else if(a.startsWith("canAccess")&&n&&t){var i=a.replace("canAccess","").charAt(0).toLowerCase()+a.replace("canAccess","").slice(1);s="".concat(t,".").concat(i)}z[a]={parentKey:n,menuKey:s,children:{}},u&&u.length>0&&e(u,a,s)}))},$=function(r){var n;if(!r.startsWith("canAccess"))return r;var t=z[r];if(!t||!t.menuKey)return r.replace("canAccess","").replace(/([A-Z])/g," $1").trim();var a=e.formatMessage({id:t.menuKey},{defaultMessage:""});if(t.parentKey&&null!==(n=z[t.parentKey])&&void 0!==n&&n.menuKey){var u=z[t.parentKey].menuKey,s=e.formatMessage({id:u},{defaultMessage:""});if(s&&s!==u&&a&&a!==t.menuKey)return"".concat(s," - ").concat(a)}if(a&&a!==t.menuKey)return a;var c=r.replace("canAccess",""),i=e.formatMessage({id:c},{defaultMessage:""});if(i&&i!==c)return i;var o=c.charAt(0).toLowerCase()+c.slice(1),p="menu.".concat(o),f=e.formatMessage({id:p},{defaultMessage:""});return f&&f!==p?f:c.replace(/([A-Z])/g," $1").trim()};(0,v.useEffect)((function(){var e=function(){var e=l()(i()().mark((function e(){var r,n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,b.CW)();case 3:r=e.sent,n={},W({}),B(n),Y(r),t=function e(r){return r.map((function(r){return p()(p()({},r),{},{title:$(r.key),children:r.children?e(r.children):void 0})}))}(r),_(t),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),g.ZP.error("获取权限树据失败，请重试");case 17:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(){return e.apply(this,arguments)}}();e()}),[e]),(0,v.useEffect)((function(){var e;t||(null===(e=F.current)||void 0===e||e.resetFields(),A([]))}),[t]);var H=function(e){var r=function e(r){for(var n=0,t=Object.entries(V);n<t.length;n++){var a=h()(t[n],2),u=a[0];if(a[1].includes(r))return[u].concat(s()(e(u)))}return[]},n=new Set;return e.forEach((function(e){r(e).forEach((function(e){return n.add(e)}))})),s()(new Set([].concat(s()(e),s()(Array.from(n)))))},J=function(){var r=l()(i()().mark((function r(n){var t,s,c;return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return t=g.ZP.loading(e.formatMessage({id:"adding",defaultMessage:"正在添加"})),r.prev=1,c=H(R),r.next=5,(0,b._d)(p()(p()({},n),{},{access:c.reduce((function(e,r){return p()(p()({},e),{},a()({},r,!0))}),{})}));case 5:return t(),g.ZP.success(e.formatMessage({id:"add.success",defaultMessage:"添加成功"})),u(!1),null===(s=K.current)||void 0===s||s.reload(),r.abrupt("return",!0);case 12:return r.prev=12,r.t0=r.catch(1),t(),g.ZP.error(e.formatMessage({id:"add.fail",defaultMessage:"添加失败，请重试"})),r.abrupt("return",!1);case 17:case"end":return r.stop()}}),r,null,[[1,12]])})));return function(e){return r.apply(this,arguments)}}(),Q=function(){var r=l()(i()().mark((function r(n){var t,u,s,c;return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(t=g.ZP.loading(e.formatMessage({id:"updating",defaultMessage:"正在更新"})),r.prev=1,n.id){r.next=4;break}throw new Error("Role ID is undefined");case 4:return s=H(R),c=s.reduce((function(e,r){return p()(p()({},e),{},a()({},r,!0))}),{}),r.next=8,(0,b.ul)(n.id,p()(p()({},n),{},{access:c}));case 8:return t(),g.ZP.success(e.formatMessage({id:"update.success",defaultMessage:"更新成功"})),d(!1),S(void 0),null===(u=K.current)||void 0===u||u.reload(),r.abrupt("return",!0);case 16:return r.prev=16,r.t0=r.catch(1),t(),g.ZP.error(e.formatMessage({id:"update.fail",defaultMessage:"更新失败，请重试"})),r.abrupt("return",!1);case 21:case"end":return r.stop()}}),r,null,[[1,16]])})));return function(e){return r.apply(this,arguments)}}(),X=function(){var e=l()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:x.Z.confirm({title:"确认删除",content:"确定要删除这个角色吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=l()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=g.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,b.Rd)(r.id);case 4:return n(),g.ZP.success("删除成功"),null===(t=K.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),g.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),ee=function(){var e=l()(i()().mark((function e(r){var n,t,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,b.cY)(r.id);case 3:t=e.sent,d(!0),S(t),a=Object.entries(t.access||{}).filter((function(e){return h()(e,2)[1]})).map((function(e){return h()(e,1)[0]})),A(a),null===(n=F.current)||void 0===n||n.setFieldsValue({name:t.name,description:t.description}),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),g.ZP.error("获取角色详情失败，请重试");case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(r){return e.apply(this,arguments)}}(),re=[{title:e.formatMessage({id:"role.name",defaultMessage:"名称"}),dataIndex:"name",valueType:"text"},{title:e.formatMessage({id:"role.description",defaultMessage:"描述"}),dataIndex:"description",valueType:"textarea"},{title:e.formatMessage({id:"role.createdAt",defaultMessage:"创建时间"}),dataIndex:"created_at",valueType:"dateTime",search:!1},{title:e.formatMessage({id:"role.deletable",defaultMessage:"可删除"}),dataIndex:"deletable",valueType:"text",search:!1,render:function(e){return e?"是":"否"}},{title:e.formatMessage({id:"role.action",defaultMessage:"操作"}),dataIndex:"option",valueType:"option",render:function(r,n){return[(0,M.jsx)(w.ZP,{type:"link",onClick:function(){return ee(n)},children:e.formatMessage({id:"edit",defaultMessage:"编辑"})},"edit-".concat(n.id)),(0,M.jsx)(w.ZP,{type:"link",danger:!0,onClick:function(){return X(n)},disabled:!n.deletable,children:e.formatMessage({id:"delete",defaultMessage:"删除"})},"delete-".concat(n.id))]}}];return(0,M.jsxs)(m._z,{children:[(0,M.jsx)(y.Z,{headerTitle:e.formatMessage({id:"menu.roleManagement",defaultMessage:"角色管理"}),actionRef:K,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,M.jsxs)(w.ZP,{type:"primary",onClick:function(){u(!0),A([])},children:[(0,M.jsx)(k.Z,{})," ",e.formatMessage({id:"new",defaultMessage:"新建"})]},"primary")]},request:function(){var e=l()(i()().mark((function e(r){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,b.F3)(r);case 2:return n=e.sent,e.abrupt("return",{data:n.data||[],success:n.success||!0,total:n.total||0});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:re,pagination:{pageSize:10}}),(0,M.jsx)(C,{modalVisible:t,onCancel:function(){return u(!1)},onSubmit:J,formRef:F,values:{},treeData:I,checkedKeys:R,onTreeCheck:A}),T&&(0,M.jsx)(C,{modalVisible:f,onCancel:function(){d(!1),S(void 0)},onSubmit:Q,formRef:F,values:T,treeData:I,checkedKeys:R,onTreeCheck:A})]})}},69044:function(e,r,n){n.d(r,{CW:function(){return I},F3:function(){return E},Nq:function(){return m},Rd:function(){return O},Rf:function(){return l},Rp:function(){return P},_d:function(){return R},az:function(){return w},cY:function(){return G},cn:function(){return h},h8:function(){return g},iE:function(){return j},jA:function(){return b},mD:function(){return M},ul:function(){return K},w1:function(){return V},wG:function(){return U}});var t=n(5574),a=n.n(t),u=n(97857),s=n.n(u),c=n(15009),i=n.n(c),o=n(99289),p=n.n(o),f=n(78158);function l(e){return d.apply(this,arguments)}function d(){return(d=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/users",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return v.apply(this,arguments)}function v(){return(v=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/users",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return y.apply(this,arguments)}function y(){return(y=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/users/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return x.apply(this,arguments)}function x(){return(x=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/users/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e,r){return k.apply(this,arguments)}function k(){return(k=p()(i()().mark((function e(r,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/users/changeStatus",{method:"POST",data:{id:r,status:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return Z.apply(this,arguments)}function Z(){return(Z=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/groups",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return T.apply(this,arguments)}function T(){return(T=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/groups",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return C.apply(this,arguments)}function C(){return(C=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/groups/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e,r){return S.apply(this,arguments)}function S(){return(S=p()(i()().mark((function e(r,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/groups/".concat(r),s()({method:"DELETE"},n)));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return N.apply(this,arguments)}function N(){return(N=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/roles",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e){return A.apply(this,arguments)}function A(){return(A=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/roles",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e,r){return F.apply(this,arguments)}function F(){return(F=p()(i()().mark((function e(r,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/roles/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return D.apply(this,arguments)}function D(){return(D=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/roles/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return _.apply(this,arguments)}function _(){return(_=p()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return L.apply(this,arguments)}function L(){return(L=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/roles/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return W.apply(this,arguments)}function W(){return(W=p()(i()().mark((function e(r){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new URLSearchParams,Object.entries(r).forEach((function(e){var r=a()(e,2),t=r[0],u=r[1];n.append(t,String(u))})),e.abrupt("return",(0,f.N)("/api/system/config?".concat(n.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e){return q.apply(this,arguments)}function q(){return(q=p()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/useActiveCases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);