# app/main.py
import os
from fastapi import FastAPI, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from app.routers import auth, users, rule, groups, role, llm, embedding, authorization, rerank # 添加 permission
from app.routers import chat, conversation, system_app_setting,complianceQA,message,marketingQA,feedback,chat2kb,wisegraph_api
from app.routers import structured_dataset,unstructured_dataset,dataset_file,structured_data_record # 添加 permission
from app.routers import consumerProtectionRule,consumerProtection,wjb_webSeach_agent,financial_webSeach_agent,policy_webSeach_agent ,mediaInsightsReport,auditTask# 添加 permission
from app.routers import auditTask # 添加 permission
from app.routers import base_system_app_api
from app.routers import knowledge_base,source_files # 添加 permission
from app.routers import system_settings # 添加 permission
from app.routers import useCases # 添加 permission
from app.routers import aiContentRecognition # 添加 AI内容识别路由
from app.routers import customerAssistant # 添加 AI内容识别路由
from app.routers import reportTemplate # 添加 AI内容识别路由
from app.routers import prompt # 添加提示词管理路由
from app.routers import getContextFromLangflow # 添加提示词管理路由
from app.routers import complaintAnalysis_agent # 添加投诉分析路由
from app.routers import wise_retrieval
from app.routers import access_log # 添加访问日志路由
# api
from app.api import preLoanAssistant_api, fina_api # 添加 permission
from app.utils.init_data import init_all
# from app.utils.auth import verify_token
from fastapi.responses import Response, FileResponse
from fastapi import Request # 添加 Request 导入
from .db.mongodb import mongodb
from app.db.miniIO import minio
from app.engines.wisegraph.graph_retriever import neo4j
from app.utils.config import settings
# 设置日志配置
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)
from app.engines.coe.coe_service import COEService
# 导入中间件
from app.middleware import AccessLogMiddleware, PerformanceMonitorMiddleware

# 定义 API 路由的统一前缀
api_prefix = "/api"

# 初始化数据
async def init_data():
    await init_all()
    logger.info("--==初始化数据完成==--")

# 获取配置
# settings = get_settings()

# 创建应用
app = FastAPI(
    title=os.getenv('APP_NAME', 'RoarData AI App'),
    debug=os.getenv('DEBUG', True)
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], 
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加访问日志中间件
app.add_middleware(AccessLogMiddleware)

# 添加性能监控中间件
# app.add_middleware(PerformanceMonitorMiddleware)

# 先注册所有 API 路由, 并确保它们有统一的前缀
app.include_router(auth.router, tags=["auth"])
app.include_router(users.router, tags=["users"])
app.include_router(rule.router,  tags=["rule"])
app.include_router(groups.router,  tags=["groups"])
app.include_router(role.router,tags=["roles"])
app.include_router(llm.router,tags=["llm"])
app.include_router(embedding.router,tags=["embedding"])
app.include_router(rerank.router,tags=["rerank"])
app.include_router(authorization.router,tags=["authorization"])
app.include_router(conversation.router,tags=["conversation"])
app.include_router(message.router,tags=["message"])
app.include_router(feedback.router,tags=["feedback"])
app.include_router(system_app_setting.router,tags=["system_app_setting"])
app.include_router(structured_dataset.router,tags=["structured_dataset"])
app.include_router(unstructured_dataset.router,tags=["unstructured_dataset"])
app.include_router(dataset_file.router,tags=["dataset_file"])
app.include_router(structured_data_record.router,tags=["structured_data_record"])
app.include_router(knowledge_base.router,tags=["knowledge_base"])
app.include_router(source_files.router,tags=["source_files"])
app.include_router(useCases.router,tags=["useCases"])
app.include_router(consumerProtectionRule.router,tags=["consumerProtectionRule"])
app.include_router(consumerProtection.router,tags=["consumerProtection"])
app.include_router(wjb_webSeach_agent.router,tags=["wjb_webSeach_agent"])
app.include_router(financial_webSeach_agent.router,tags=["financial_webSeach_agent"])
app.include_router(policy_webSeach_agent.router,tags=["policyTrackingTool"])
app.include_router(auditTask.router,tags=["auditTask"])
app.include_router(customerAssistant.router,tags=["customerAssistant"])
app.include_router(reportTemplate.router,tags=["reportTemplate"])
app.include_router(prompt.router,tags=["prompt"]) # 添加提示词管理路由
app.include_router(complaintAnalysis_agent.router,tags=["complaintAnalysis"])
app.include_router(mediaInsightsReport.router,tags=["mediaInsightsReport"])
app.include_router(access_log.router,tags=["access_logs"]) # 添加访问日志路由

# api
app.include_router(preLoanAssistant_api.router,tags=["preLoanAssistant"])
app.include_router(fina_api.router,tags=["fina"])
# system app 系统自带应用
app.include_router(base_system_app_api.router,tags=["base_system_app_api"])
# 移除现有的chat路由注册
# app.include_router(chat.router, prefix=api_prefix, tags=["chat"]) # 注意: 这个可能没有 v1
# 重新添加chat路由，不添加额外的前缀，因为router定义时已有前缀
app.include_router(chat.router, tags=["chat"])
app.include_router(complianceQA.router,tags=["complianceQA"])
app.include_router(chat2kb.router, tags=["chat2kb"])
app.include_router(wisegraph_api.router, tags=["wisegraph"])
app.include_router(getContextFromLangflow.router, tags=["getContextFromLangflow"])
app.include_router(marketingQA.router,tags=["marketingQA"])
app.include_router(aiContentRecognition.router, tags=["aiContentRecognition"])
app.include_router(system_settings.router) # 注意: 这个可能没有 v1
app.include_router(wise_retrieval.router, tags=["wise_retrieval"])

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 构建静态文件目录的路径
static_dir = os.path.join(current_dir, "static")


# 挂载静态文件目录到 /static 路径
app.mount("/static", StaticFiles(directory=static_dir, html=True), name="static")

# 添加捕获所有 GET 请求的路由 (应在 API 路由之后)
# 用于处理 SPA 的前端路由
@app.get("/{full_path:path}", include_in_schema=False)
async def serve_spa_index(request: Request):
    path = request.url.path

    # 1. 如果是 API 请求，正常进行接口访问
    if path.startswith(api_prefix):
        # 不处理API请求，让它继续通过正常的路由处理
        return Response(status_code=404)  # 这里返回404是因为如果到达这里，说明没有匹配的API路由

    # 2. 所有非 API 请求返回 index.html
    index_path = os.path.join(static_dir, "index.html")
    if os.path.exists(index_path):
        return FileResponse(index_path)
    
    # 3. index.html 不存在时返回错误
    logger.error(f"index.html not found at {index_path}")
    return Response("前端入口点未找到。", status_code=500)

@app.on_event("startup")
async def on_startup():
    logger.info("--==开始启动应用==--")
    try:
        # Settings实例会在导入时自动初始化环境变量
        logger.info("环境变量已加载完成")
        
        # 初始化数据库连接
        mongodb.connect()
        logger.info("数据库连接初始化完成")

        # 初始化图数据库连接
        if settings.GRAPH_RAG:  
            await neo4j.initialize()
            logger.info("Neo4j连接初始化完成")
        else:
            logger.info("Neo4j未启用")

        # 在应用启动时初始化连接
        if settings.MINIO_ENABLE:
            await minio.connect()
            logger.info("MinIO 连接初始化完成")
        else:
            logger.info("MinIO 未启用")
        # 初始化应用数据
        await init_data()
        logger.info("--==应用启动完成==--")
        await COEService.initialize()


        if settings.INDEXER_ENABLED:
            from app.engines.indexing.base_indexer import BaseIndexer
            from apscheduler.schedulers.background import BackgroundScheduler
            scheduler = BackgroundScheduler()
            indexer = BaseIndexer()
            scheduler.add_job(indexer.execute, 'interval', seconds=10)
            scheduler.start()
            logger.info("--==索引器初始化完成==--")
        else:
            logger.info("--==索引器未启用==--")

    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        raise e

@app.on_event("shutdown")
async def shutdown_db_client():
    # 先关闭日志中间件
    from app.middleware.access_logger import shutdown_event
    await shutdown_event()
    logger.info("访问日志已完成最终刷新")
    
    await mongodb.disconnect()
    # 关闭Neo4j连接
    if settings.GRAPH_RAG:
        await neo4j.close()
        logger.info("Neo4j连接已关闭")
    # 应用关闭时断开连接
    if settings.MINIO_ENABLE:
        await minio.disconnect()
        logger.info("MinIO 连接已关闭")
    logger.info("--==应用关闭，断开数据库连接==--")
