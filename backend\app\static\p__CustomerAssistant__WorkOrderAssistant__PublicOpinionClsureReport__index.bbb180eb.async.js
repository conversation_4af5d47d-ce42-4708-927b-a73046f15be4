"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3752],{71255:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),c=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}},{tag:"path",attrs:{d:"M894 345a343.92 343.92 0 00-189-130v.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4a31.95 31.95 0 0040.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6 17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5-99 31-1-104-8-9c-84.6-103.2-90.2-251.9-11-361 96.4-132.2 281.2-161.4 413-66 132.2 96.1 161.5 280.6 66 412-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10 1 104-98-33-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1A367.31 367.31 0 00729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62 72.6 99.6 68.5 235.2-8 330z"}},{tag:"path",attrs:{d:"M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}}]},name:"comment",theme:"outlined"},a=n(91146),s=function(e,t){return c.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=c.forwardRef(s)},1832:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),c=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M834.1 469.2A347.49 347.49 0 00751.2 354l-29.1-26.7a8.09 8.09 0 00-13 3.3l-13 37.3c-8.1 23.4-23 47.3-44.1 70.8-1.4 1.5-3 1.9-4.1 2-1.1.1-2.8-.1-4.3-1.5-1.4-1.2-2.1-3-2-4.8 3.7-60.2-14.3-128.1-53.7-202C555.3 171 510 123.1 453.4 89.7l-41.3-24.3c-5.4-3.2-12.3 1-12 7.3l2.2 48c1.5 32.8-2.3 61.8-11.3 85.9-11 29.5-26.8 56.9-47 81.5a295.64 295.64 0 01-47.5 46.1 352.6 352.6 0 00-100.3 121.5A347.75 347.75 0 00160 610c0 47.2 9.3 92.9 27.7 136a349.4 349.4 0 0075.5 110.9c32.4 32 70 57.2 111.9 74.7C418.5 949.8 464.5 959 512 959s93.5-9.2 136.9-27.3A348.6 348.6 0 00760.8 857c32.4-32 57.8-69.4 75.5-110.9a344.2 344.2 0 0027.7-136c0-48.8-10-96.2-29.9-140.9zM713 808.5c-53.7 53.2-125 82.4-201 82.4s-147.3-29.2-201-82.4c-53.5-53.1-83-123.5-83-198.4 0-43.5 9.8-85.2 29.1-124 18.8-37.9 46.8-71.8 80.8-97.9a349.6 349.6 0 0058.6-56.8c25-30.5 44.6-64.5 58.2-101a240 240 0 0012.1-46.5c24.1 22.2 44.3 49 61.2 80.4 33.4 62.6 48.8 118.3 45.8 165.7a74.01 74.01 0 0024.4 59.8 73.36 73.36 0 0053.4 18.8c19.7-1 37.8-9.7 51-24.4 13.3-14.9 24.8-30.1 34.4-45.6 14 17.9 25.7 37.4 35 58.4 15.9 35.8 24 73.9 24 113.1 0 74.9-29.5 145.4-83 198.4z"}}]},name:"fire",theme:"outlined"},a=n(91146),s=function(e,t){return c.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=c.forwardRef(s)},66513:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),c=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},a=n(91146),s=function(e,t){return c.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=c.forwardRef(s)},95993:function(e,t,n){n.r(t),n.d(t,{default:function(){return Ze}});var r=n(97857),c=n.n(r),o=n(15009),a=n.n(o),s=n(64599),i=n.n(s),l=n(19632),u=n.n(l),d=n(99289),f=n.n(d),p=n(5574),x=n.n(p),m=n(88310),v=n(17598),g=n(15525),h=n(19050),k=n(19669),y=n(42075),b=n(83062),Z=n(83622),j=n(2487),w=n(67294),_=n(24444),C=(0,_.kc)((function(e){var t=e.token;return{referenceList:{".ant-list-item:first-child":{paddingTop:"0"}},card:{listItemMetaTitle:{color:t.colorTextHeading},".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},toolbar:{boxShadow:"0 1px 4px rgba(0,0,0,0.1)"},listItem:{transition:"all 0.3s ease"}}})),S=n(85893),P=(0,w.forwardRef)((function(e,t){var n=e.messages,r=C().styles,c=(0,w.useState)([]),o=x()(c,2),a=o[0],s=o[1],i=(0,w.useState)(""),l=x()(i,2),u=l[0],d=l[1];(0,w.useEffect)((function(){n&&n.length>0?s(n):s([])}),[n]);var f=w.useState(!1),p=x()(f,2),_=(p[0],p[1]),P=w.useState("all"),D=x()(P,2),z=D[0],I=(D[1],(null==a?void 0:a.filter((function(e){var t="all"===z||e.type===z,n=!u||e.messageId===u;return t&&n})))||[]),M=w.useRef(null),T=(w.useRef(new Map),function(e){console.log("scrollToItem--",e);var t=document.getElementById("content-".concat(e));t&&t.scrollIntoView({behavior:"smooth",block:"center"})});return(0,w.useImperativeHandle)(t,(function(){return{scrollToItem:T,updateReferenceList:function(e){setReferenceList(e)},filterByMessageId:function(e){d(e)},clearFilter:function(){d("")},getFilterMessageId:function(){return u}}})),(0,S.jsxs)("div",{children:[(0,S.jsx)("div",{style:{position:"sticky",top:0,padding:"8px 12px 8px 12px",zIndex:1,borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,S.jsxs)(y.Z,{children:[(0,S.jsx)(b.Z,{title:"展开全部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(m.Z,{}),onClick:function(){return _(!0)}})}),(0,S.jsx)(b.Z,{title:"收起全部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(v.Z,{}),onClick:function(){return _(!1)}})}),(0,S.jsx)(b.Z,{title:"滚动到顶部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(g.Z,{}),onClick:function(){M.current&&M.current.scrollTo({top:0,behavior:"smooth"})}})}),(0,S.jsx)(b.Z,{title:"滚动到底部",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(h.Z,{}),onClick:function(){M.current&&M.current.scrollTo({top:M.current.scrollHeight,behavior:"smooth"})}})}),u&&(0,S.jsx)(b.Z,{title:"清除筛选",mouseEnterDelay:2,children:(0,S.jsx)(Z.ZP,{type:"primary",icon:(0,S.jsx)(k.Z,{}),onClick:function(){return d("")}})})]})}),(0,S.jsx)("div",{ref:M,style:{height:"calc(100vh - 130px)",overflowY:"auto"},children:(0,S.jsx)(j.Z,{size:"large",className:r.referenceList,rowKey:"id",itemLayout:"vertical",dataSource:I})})]})}));P.displayName="WorkOrderReference";var D=P,z=n(93461),I=n(34114),M=n(78205),T=n(78919),E=n(4628),R=n(9502),F=n(76654),H=n(71471),N=n(2453),Y=n(17788),A=n(74330),B=n(86250),L=n(66309),O=n(55102),W=n(85265),J=n(10048),q=n(10981),G=n(78404),K=n(1832),X=n(14079),U=n(66513),V=n(93045),$=n(71255),Q=n(51042),ee=n(82061),te=n(47389),ne=n(87784),re=n(25820),ce=n(75750),oe=n(12906),ae=n(85175),se=n(43471),ie=n(27484),le=n.n(ie),ue=(0,_.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"30%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"70%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),de=n(93933),fe=n(13973);function pe(e){return e+"-"+Date.now()}var xe=function(e,t){return(0,S.jsxs)(y.Z,{align:"start",children:[e,(0,S.jsx)("span",{children:t})]})},me=[{key:"1",label:xe((0,S.jsx)(K.Z,{style:{color:"#FF4D4F"}}),"常见工单"),description:"快速处理常见工单问题",children:[{key:"1-1",description:"如何处理客户投诉工单？"},{key:"1-2",description:"账户异常工单处理流程？"},{key:"1-3",description:"系统故障工单如何处理？"}]},{key:"2",label:xe((0,S.jsx)(X.Z,{style:{color:"#52C41A"}}),"工单指南"),description:"工单处理标准流程",children:[{key:"2-1",icon:(0,S.jsx)(U.Z,{style:{color:"#722ED1"}}),description:"工单分类与优先级判断"},{key:"2-2",icon:(0,S.jsx)(V.Z,{style:{color:"#1890FF"}}),description:"工单回复规范与模板"},{key:"2-3",icon:(0,S.jsx)($.Z,{style:{color:"#13C2C2"}}),description:"工单升级与转派流程"}]},{key:"3",label:xe((0,S.jsx)(X.Z,{style:{color:"#FA8C16"}}),"业务知识"),description:"快速查找业务知识",children:[{key:"3-1",icon:(0,S.jsx)(U.Z,{style:{color:"#EB2F96"}}),description:"产品功能与使用说明"},{key:"3-2",icon:(0,S.jsx)(V.Z,{style:{color:"#F5222D"}}),description:"常见问题解决方案"},{key:"3-3",icon:(0,S.jsx)($.Z,{style:{color:"#A0D911"}}),description:"业务流程与规范说明"}]}],ve=[{key:"historyConversation",description:"历史对话",icon:(0,S.jsx)(X.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,S.jsx)(Q.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,S.jsx)(ee.Z,{style:{color:"#1890FF"}})}],ge=(0,q.bG)(),he=(0,G.kH)(),ke=(0,J.Z)({html:!0,breaks:!0}),ye=function(e){return(0,S.jsx)(H.Z,{style:{marginBottom:0},children:(0,S.jsx)("div",{dangerouslySetInnerHTML:{__html:ke.render(e)}})})},be="workOrderAssistant",Ze=function(){var e,t=ue().styles,n=(0,w.useState)(window.innerHeight),r=x()(n,1)[0],o=w.useRef(),s=w.useState(!1),l=x()(s,2),d=l[0],p=l[1],m=w.useState(""),v=x()(m,2),g=v[0],h=v[1],k=w.useState([]),j=x()(k,2),_=j[0],C=j[1],P=w.useState(),H=x()(P,2),J=H[0],G=H[1],K=(0,w.useState)(!1),X=x()(K,2),U=X[0],V=X[1],$=(0,w.useState)(!1),ie=x()($,2),xe=ie[0],ke=ie[1],Ze=(0,w.useState)(!1),je=x()(Ze,2),we=je[0],_e=je[1],Ce=(0,w.useState)(""),Se=x()(Ce,2),Pe=Se[0],De=Se[1],ze=(0,w.useState)(""),Ie=x()(ze,2),Me=Ie[0],Te=Ie[1],Ee=(0,w.useState)([]),Re=x()(Ee,2),Fe=Re[0],He=Re[1],Ne=(0,w.useRef)(null),Ye=(0,w.useState)(!1),Ae=x()(Ye,2),Be=Ae[0],Le=Ae[1],Oe=(0,w.useState)(""),We=x()(Oe,2),Je=We[0],qe=We[1],Ge=function(e){qe(e),Le(!0)},Ke=function(e){var t=Fe.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){N.ZP.success("复制成功")})).catch((function(){N.ZP.error("复制失败")}))},Xe=(0,z.Z)({request:(e=f()(a()().mark((function e(t,n){var r,c,s,l,d,f,p,x,m,v,g,h,k,y,b,Z,j,w,_,C,S,P,D,z,I,M,T,E,R,F,H,Y,A,B,L,O,W;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,c=t.message,s=n.onSuccess,l=n.onUpdate,d=n.onError,e.prev=2,!xe){e.next=6;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ke(!0),p=(0,q.bW)(),x=c?c.id:pe(o.current),c||s({content:"出现了异常:",role:"assistant",id:x,references:[],collected:!1}),m={conversation_id:o.current||"",message_id:x,meta_data:{},extra:{},role:c?c.role:"user",content:c?c.content:"",app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,references:[],token_count:null,price:null,collected:!1,created_at:le()().format("YYYY-MM-DD HH:mm:ss")},He((function(e){var t=[].concat(u()(e),[m]);return console.log("更新后的消息列表:",t),t})),o.current){e.next=15;break}throw N.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",o.current),v={conversation_id:o.current,app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,extra:{},messages:r},g={id:pe(o.current),role:"user",content:"",references:[],collected:!1},h=!1,k="",y=[],l(g),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(p)},body:JSON.stringify(v)});case 24:if((b=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(b.status));case 27:if(Z=null===(f=b.body)||void 0===f?void 0:f.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:j=new TextDecoder("utf-8"),w={conversation_id:o.current||"",message_id:g.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:be,user_id:null==ge?void 0:ge.id,user_name:null==ge?void 0:ge.name,references:[],token_count:null,price:null,collected:!1,created_at:le()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(h){e.next=100;break}return e.next=35,Z.read();case 35:_=e.sent,C=_.value,_.done&&(h=!0),k+=j.decode(C,{stream:!0}),S=k.split("\n\n"),k=S.pop()||"",P=i()(S),e.prev=43,P.s();case 45:if((D=P.n()).done){e.next=90;break}if(""!==(z=D.value).trim()){e.next=49;break}return e.abrupt("continue",88);case 49:I=z.split("\n"),M=null,T=null,E=i()(I);try{for(E.s();!(R=E.n()).done;)(F=R.value).startsWith("event: ")?M=F.substring(7).trim():F.startsWith("data: ")&&(T=F.substring(6))}catch(e){E.e(e)}finally{E.f()}if(!T){e.next=88;break}e.t0=M,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:88;break;case 58:if("[DONE]"===T){e.next=69;break}e.prev=59,Y=JSON.parse(T),(A=(null===(H=Y.choices[0])||void 0===H||null===(H=H.delta)||void 0===H?void 0:H.content)||"")&&(g.content+=A,l(g)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",s({content:"出现了异常:"+T,role:"assistant",id:pe(o.current),references:[],collected:!1}));case 69:return e.abrupt("break",88);case 70:try{B=JSON.parse(T),console.log("模块状态：",B)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",88);case 72:try{L=JSON.parse(T),console.log("appStreamData===>",L),y=L,g.references=y}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",88);case 74:try{console.log("flowResponsesData",T)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",88);case 76:return h=!0,e.abrupt("break",88);case 78:e.prev=78,O=JSON.parse(T),l(O),e.next=87;break;case 83:throw e.prev=83,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 87:return e.abrupt("break",88);case 88:e.next=45;break;case 90:e.next=95;break;case 92:e.prev=92,e.t3=e.catch(43),P.e(e.t3);case 95:return e.prev=95,P.f(),e.finish(95);case 98:e.next=32;break;case 100:if(s(g),!g.content||""===g.content.trim()){e.next=108;break}return w.content=g.content,w.references=y,e.next=106,(0,de.tn)(w);case 106:(W=e.sent).success?(w.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),He((function(e){var t=[].concat(u()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):N.ZP.error("消息上报失败");case 108:e.next=115;break;case 110:e.prev=110,e.t4=e.catch(2),console.log("error===>",e.t4),s({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:pe(o.current),references:[],collected:!1}),d(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 115:return e.prev=115,ke(!1),e.finish(115);case 118:case"end":return e.stop()}}),e,null,[[2,110,115,118],[43,92,95,98],[59,65],[78,83]])}))),function(t,n){return e.apply(this,arguments)})}),Ue=x()(Xe,1)[0],Ve=(0,I.Z)({agent:Ue}),$e=Ve.onRequest,Qe=Ve.messages,et=Ve.setMessages,tt=function(e){G(e),console.log("activeKey 设置",e),o.current=e},nt=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return c()(c()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return c()(c()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));C([].concat(u()(t),u()(n)))},rt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},ct=function(){var e=f()(a()().mark((function e(t){var n,r,c;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ke(!0),console.info("获取对话信息",t),n=le()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,de.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),c=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==he?void 0:he.logo)||"/static/logo.png":(null==ge?void 0:ge.avatar)||"/avatar/default.jpeg"}}})),He(r.messages),et(c),tt(t)):N.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ke(!1),G(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),ot=function(){var e=f()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,de.Db)(o.current);case 4:e.sent.success?(He([]),et([]),Ne.current&&Ne.current.updateReferenceList([])):N.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),at=function(){var e=f()(a()().mark((function e(){var t,n,r,c;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,q.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,de.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:be});case 10:c=e.sent,nt([].concat(u()(_),[{key:c.id||"",id:c.id||"",label:c.conversation_name||"",conversation_name:c.conversation_name||"",active_at:c.active_at||"",pinned_at:c.pinned_at,pinned:c.pinned||!1,messages:[]}])),tt(c.id||""),ot(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),st=function(){var e=f()(a()().mark((function e(t){var n,r,o,s,i;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,o=!r,e.prev=6,s=le()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,de.X1)(t,{conversation_name:null,active_at:null,pinned:o,pinned_at:s});case 10:i=_.map((function(e){return e.key===t?c()(c()({},e),{},{pinned:o}):e})),nt(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),it=function(){var e=f()(a()().mark((function e(t){var n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,de.SJ)(t);case 3:n=_.filter((function(e){return e.key!==t})),nt(n),o.current===t&&n.length>0&&ct(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),lt=function(){var e=f()(a()().mark((function e(t,n){var r,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,de.X1)(t,r);case 7:null!=(o=e.sent)&&o.success?C((function(e){return e.map((function(e){return e.key===t?c()(c()({},e),{},{label:n}):e}))})):N.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),ut=function(){var e=f()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,ct(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,w.useEffect)((function(){var e=function(){var e=f()(a()().mark((function e(){var t,n,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,q.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,de.Mw)({user_id:t.id,app_info:be});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,at();case 10:e.next=15;break;case 12:r=rt(n.data),nt(n.data),ct(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[be]);var dt=function(){var e=f()(a()().mark((function e(t){var n,r,c,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(Fe),n=Fe.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return N.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=Fe[n],c=Fe.slice(n),console.log("将要删除的消息:",c),e.next=13,(0,de.qP)(c.map((function(e){return e.message_id})));case 13:e.sent.success||N.ZP.error("删除消息失败"),He((function(e){return e.slice(0,n)})),et((function(e){return e.slice(0,n)})),"assistant"===r.role?(o=Fe.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&$e({id:t,role:"user",content:o.content,references:[],collected:!1}):$e({id:t,role:"user",content:r.content,references:[],collected:!1}),N.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),N.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),ft=function(){var e=f()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Y.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return f()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,de.$Z)(t);case 4:e.sent.success?(He((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",Fe),et((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",Qe),N.ZP.success("消息及相关引用已删除")):N.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),N.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),pt=function(){var e=f()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,de.bk)({message_id:t,collected:!n});case 3:e.sent.success?(N.ZP.success(n?"取消收藏成功":"收藏成功"),et((function(e){return e.map((function(e){return e.id===t?c()(c()({},e),{},{message:c()(c()({},e.message),{},{collected:!n})}):e}))})),He((function(e){return e.map((function(e){return e.message_id===t?c()(c()({},e),{},{collected:!n}):e}))}))):N.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),xt=function(){var e=f()(a()().mark((function e(t){var n,r,c;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return N.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,c=n.description,"historyConversation"!==r){e.next=8;break}V(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,at();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,ot();case 16:e.next=19;break;case 18:$e({id:pe(o.current),role:"user",content:c,references:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),mt=(0,S.jsxs)(y.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,S.jsx)(M.Z,{variant:"borderless",icon:(0,S.jsx)("img",{src:(null==he?void 0:he.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以为您解答产品咨询、业务办理等相关问题，请问有什么可以帮您？"}),(0,S.jsx)(T.Z,{title:"以下是常见问题，您可以直接点击进行咨询",items:me,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #f0f7ff 0%, #fff1f0 100%)",border:0,flex:1}},onItemClick:xt})]}),vt=Qe.length>0?Qe.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:o.current+"_"+t,loadingRender:function(){return(0,S.jsxs)(y.Z,{children:[(0,S.jsx)(A.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:n.role,messageRender:ye,avatar:"local"===r?{src:(null==ge?void 0:ge.avatar)||"/avatar/default.jpeg"}:{src:(null==he?void 0:he.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,S.jsxs)(B.Z,{children:[n.references.length>0&&(0,S.jsxs)(L.Z,{bordered:!1,color:"blue",onClick:function(){return e=n.id,console.log("filterMessageReference===>",e),void(Ne.current&&(Ne.current.getFilterMessageId()===e?Ne.current.clearFilter():Ne.current.filterByMessageId(e)));var e},children:["引用:",n.references.length]}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:n.collected?(0,S.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,S.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id,n.collected)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(n.id)}})]}):(0,S.jsxs)(B.Z,{children:[(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:n.collected?(0,S.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,S.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id,n.collected)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}}),(0,S.jsx)(Z.ZP,{size:"small",type:"text",icon:(0,S.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return Ke(n.id)}})]})}})):[{content:mt,variant:"borderless"}],gt=(0,S.jsx)(E.Z.Header,{title:"Attachments",open:d,onOpenChange:p,styles:{content:{padding:0}}}),ht=(0,S.jsxs)("div",{className:t.logo,children:[(0,S.jsx)("span",{children:"对话记录"}),(0,S.jsx)(b.Z,{title:"新对话",children:(0,S.jsx)(Z.ZP,{type:"text",icon:(0,S.jsx)(Q.Z,{}),onClick:at,style:{fontSize:"16px"}})})]}),kt=(0,S.jsx)(Y.Z,{title:"修改对话标题",open:we,onOk:function(){Me&&Pe.trim()&&(lt(Me,Pe.trim()),_e(!1))},onCancel:function(){_e(!1),De(""),Te("")},children:(0,S.jsx)(O.Z,{value:Pe,onChange:function(e){return De(e.target.value)},placeholder:"请输入新的对话标题"})}),yt=(0,S.jsx)(W.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return V(!1)},open:U,children:(0,S.jsxs)("div",{className:t.menu,children:[ht,(0,S.jsx)(R.Z,{items:_,activeKey:J,onActiveChange:ut,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,S.jsx)(te.Z,{})},{label:"置顶",key:"pin",icon:(0,S.jsx)(ne.Z,{})},{label:"删除",key:"delete",icon:(0,S.jsx)(ee.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":Te(e.key),De(e.label),_e(!0);break;case"pin":st(e.key);break;case"delete":if(xe)return void N.ZP.error("系统正在处理其他对话。请稍😊");it(e.key)}}}},groupable:!0})]})});return(0,w.useEffect)((function(){console.log("currentConversationMessages 更新了:",Fe)}),[Fe]),(0,S.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,S.jsxs)("div",{className:t.chat,children:[(0,S.jsx)(F.Z.List,{items:vt,className:t.messages}),(0,S.jsx)(T.Z,{items:ve,onItemClick:xt}),(0,S.jsx)(E.Z,{value:g,header:gt,onSubmit:function(e){console.log("nextContent===>",e),e&&($e({id:pe(o.current),role:"user",content:e,references:[],collected:!1}),h(""))},onChange:h,loading:Ue.isRequesting(),className:t.sender})]}),(0,S.jsx)("div",{className:t.reference,children:(0,S.jsx)(D,{ref:Ne,messages:Fe})}),kt,yt,(0,S.jsx)(fe.Z,{visible:Be,messageId:Je,conversationId:J,appInfo:be,onClose:function(){return Le(!1)}})]})}}}]);