(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5770],{51042:function(e,t,n){"use strict";var r=n(1413),s=n(67294),o=n(42110),a=n(91146),i=function(e,t){return s.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},c=s.forwardRef(i);t.Z=c},14079:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),s=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 161H699.2c-49.1 0-97.1 14.1-138.4 40.7L512 233l-48.8-31.3A255.2 255.2 0 00324.8 161H96c-17.7 0-32 14.3-32 32v568c0 17.7 14.3 32 32 32h228.8c49.1 0 97.1 14.1 138.4 40.7l44.4 28.6c1.3.8 2.8 1.3 4.3 1.3s3-.4 4.3-1.3l44.4-28.6C602 807.1 650.1 793 699.2 793H928c17.7 0 32-14.3 32-32V193c0-17.7-14.3-32-32-32zM324.8 721H136V233h188.8c35.4 0 69.8 10.1 99.5 29.2l48.8 31.3 6.9 4.5v462c-47.6-25.6-100.8-39-155.2-39zm563.2 0H699.2c-54.4 0-107.6 13.4-155.2 39V298l6.9-4.5 48.8-31.3c29.7-19.1 64.1-29.2 99.5-29.2H888v488zM396.9 361H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm223.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0-4.1-3.2-7.5-7.1-7.5H627.1c-3.9 0-7.1 3.4-7.1 7.5zM396.9 501H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm416 0H627.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5z"}}]},name:"read",theme:"outlined"},a=n(91146),i=function(e,t){return s.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var c=s.forwardRef(i)},9502:function(e,t,n){"use strict";n.d(t,{Z:function(){return H}});var r=n(87462),s=n(93967),o=n.n(s),a=n(67294),i=n(71471);const c=a.createContext(null);var l=({children:e})=>{const{prefixCls:t}=a.useContext(c);return a.createElement("div",{className:o()(`${t}-group-title`)},e&&a.createElement(i.Z.Text,null,e))},u=n(29245),d=n(51398),m=function(e,t){return a.createElement(d.Z,(0,r.Z)({},e,{ref:t,icon:u.Z}))};var f=a.forwardRef(m),p=n(85418),g=n(64217);const h=e=>{e.stopPropagation()};var y=e=>{const{prefixCls:t,info:n,className:s,direction:c,onClick:l,active:u,menu:d,...m}=e,y=(0,g.Z)(m,{aria:!0,data:!0,attr:!0}),{disabled:v}=n,b=o()(s,`${t}-item`,{[`${t}-item-active`]:u&&!v},{[`${t}-item-disabled`]:v}),{trigger:w,...$}=d||{},S=$?.getPopupContainer;return a.createElement("li",(0,r.Z)({},y,{className:b,onClick:()=>{!v&&l&&l(n)},title:`${n.label}`}),n.icon&&a.createElement("div",{className:`${t}-icon`},n.icon),a.createElement(i.Z.Text,{className:`${t}-label`},n.label),!v&&d&&a.createElement(p.Z,{menu:$,placement:"rtl"===c?"bottomLeft":"bottomRight",trigger:["click"],disabled:v,getPopupContainer:S},(e=>{const n=a.createElement(f,{onClick:h,className:`${t}-menu-icon`});return w?"function"==typeof w?w(e,{originNode:n}):w:n})(n)))},v=n(21770),b=n(21450),w=n(36158);const $="__ungrouped";var S=(e,t=[])=>{const[n,r,s]=a.useMemo((()=>{if(!e)return[!1,void 0,void 0];let t={sort:void 0,title:void 0};return"object"==typeof e&&(t={...t,...e}),[!0,t.sort,t.title]}),[e]);return a.useMemo((()=>{if(!n){return[[{name:$,data:t,title:void 0}],n]}const e=t.reduce(((e,t)=>{const n=t.group||$;return e[n]||(e[n]=[]),e[n].push(t),e}),{});return[(r?Object.keys(e).sort(r):Object.keys(e)).map((t=>({name:t===$?void 0:t,title:s,data:e[t]}))),n]}),[t,e])},x=n(11568),E=n(83262),R=n(43495);var k=(0,R.I$)("Conversations",(e=>(e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",gap:e.paddingXXS,overflowY:"auto",padding:e.paddingSM,margin:0,listStyle:"none","ul, ol":{margin:0,padding:0,listStyle:"none"},[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-list`]:{display:"flex",gap:e.paddingXXS,flexDirection:"column",[`& ${t}-item`]:{paddingInlineStart:e.paddingXL},[`& ${t}-label`]:{color:e.colorTextDescription,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},[`& ${t}-item`]:{display:"flex",height:e.controlHeightLG,minHeight:e.controlHeightLG,gap:e.paddingXS,padding:`0 ${(0,x.bf)(e.paddingXS)}`,alignItems:"center",borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,"&:hover":{backgroundColor:e.colorBgTextHover},"&-active":{backgroundColor:e.colorBgTextHover,[`& ${t}-label, ${t}-menu-icon`]:{color:e.colorText}},"&-disabled":{cursor:"not-allowed",[`& ${t}-label`]:{color:e.colorTextDisabled}},"&:hover, &-active":{[`& ${t}-menu-icon`]:{opacity:.6}},[`${t}-menu-icon:hover`]:{opacity:1}},[`& ${t}-label`]:{flex:1,color:e.colorText,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`& ${t}-menu-icon`]:{opacity:0,fontSize:e.fontSizeXL},[`& ${t}-group-title`]:{display:"flex",alignItems:"center",height:e.controlHeightLG,minHeight:e.controlHeightLG,padding:`0 ${(0,x.bf)(e.paddingXS)}`}}}})((0,E.IX)(e,{}))),(()=>({})));var H=e=>{const{prefixCls:t,rootClassName:n,items:s,activeKey:i,defaultActiveKey:u,onActiveChange:d,menu:m,styles:f={},classNames:p={},groupable:h,className:$,style:x,...E}=e,R=(0,g.Z)(E,{attr:!0,aria:!0,data:!0}),[H,C]=(0,v.Z)(u,{value:i}),[Z,N]=S(h,s),{getPrefixCls:T,direction:q}=(0,w.Z)(),M=T("conversations",t),L=(0,b.Z)("conversations"),[A,U,X]=k(M),z=o()(M,L.className,$,n,U,X,{[`${M}-rtl`]:"rtl"===q}),I=e=>{C(e.key),d&&d(e.key)};return A(a.createElement("ul",(0,r.Z)({},R,{style:{...L.style,...x},className:z}),Z.map(((e,t)=>{const n=e.data.map(((e,t)=>a.createElement(y,{key:e.key||`key-${t}`,info:e,prefixCls:M,direction:q,className:o()(p.item,L.classNames.item),style:{...L.styles.item,...f.item},menu:"function"==typeof m?m(e):m,active:H===e.key,onClick:I})));return N?a.createElement("li",{key:e.name||`key-${t}`},a.createElement(c.Provider,{value:{prefixCls:M}},e.title?.(e.name,{components:{GroupTitle:l}})||a.createElement(l,{key:e.name},e.name)),a.createElement("ul",{className:`${M}-list`},n)):n}))))}},93461:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var r=n(67294);const s=e=>""!==(e??"").trim();var o=function(e){const{readableStream:t,transformStream:n}=e;if(!(t instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const r=new TextDecoderStream,o=n?t.pipeThrough(r).pipeThrough(n):t.pipeThrough(r).pipeThrough(function(){let e="";return new TransformStream({transform(t,n){e+=t;const r=e.split("\n\n");r.slice(0,-1).forEach((e=>{s(e)&&n.enqueue(e)})),e=r[r.length-1]},flush(t){s(e)&&t.enqueue(e)}})}()).pipeThrough(new TransformStream({transform(e,t){const n=e.split("\n").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)throw new Error('The key-value separator ":" is not found in the sse line chunk!');const r=t.slice(0,n);if(!s(r))return e;const o=t.slice(n+1);return{...e,[r]:o}}),{});0!==Object.keys(n).length&&t.enqueue(n)}}));return o[Symbol.asyncIterator]=async function*(){const e=this.getReader();for(;;){const{done:t,value:n}=await e.read();if(t)break;n&&(yield n)}},o};var a=async(e,t={})=>{const{fetch:n=globalThis.fetch,middlewares:r={},...s}=t;if("function"!=typeof n)throw new Error("The options.fetch must be a typeof fetch function!");let o=[e,s];if("function"==typeof r.onRequest){o=await r.onRequest(...o)}let a=await n(...o);if("function"==typeof r.onResponse){const e=await r.onResponse(a);if(!(e instanceof Response))throw new Error("The options.onResponse must return a Response instance!");a=e}if(!a.ok)throw new Error(`Fetch failed with status ${a.status}`);if(!a.body)throw new Error("The response body is empty.");return a};class i{baseURL;model;defaultHeaders;customOptions;constructor(e){const{baseURL:t,model:n,dangerouslyApiKey:r,...s}=e;this.baseURL=e.baseURL,this.model=e.model,this.defaultHeaders={"Content-Type":"application/json",...e.dangerouslyApiKey&&{Authorization:e.dangerouslyApiKey}},this.customOptions=s}static init(e){if(!e.baseURL||"string"!=typeof e.baseURL)throw new Error("The baseURL is not valid!");return new i(e)}create=async(e,t,n)=>{const r=new AbortController,s={method:"POST",body:JSON.stringify({model:this.model,...e}),headers:this.defaultHeaders,signal:r.signal};t?.onStream?.(r);try{const e=await a(this.baseURL,{fetch:this.customOptions.fetch,...s});if(n)return void await this.customResponseHandler(e,t,n);const r=e.headers.get("content-type")||"";switch(r.split(";")[0].trim()){case"text/event-stream":await this.sseResponseHandler(e,t);break;case"application/json":await this.jsonResponseHandler(e,t);break;default:throw new Error(`The response content-type: ${r} is not support!`)}}catch(e){const n=e instanceof Error?e:new Error("Unknown error!");throw t?.onError?.(n),n}};customResponseHandler=async(e,t,n)=>{const r=[];for await(const s of o({readableStream:e.body,transformStream:n}))r.push(s),t?.onUpdate?.(s);t?.onSuccess?.(r)};sseResponseHandler=async(e,t)=>{const n=[],r=o({readableStream:e.body});for await(const e of r)n.push(e),t?.onUpdate?.(e);t?.onSuccess?.(n)};jsonResponseHandler=async(e,t)=>{const n=await e.json();t?.onUpdate?.(n),t?.onSuccess?.([n])}}var c=i.init;let l=0;class u{config;requestingMap={};constructor(e){this.config=e}finishRequest(e){delete this.requestingMap[e]}request=(e,t,n)=>{const{request:r}=this.config,{onUpdate:s,onSuccess:o,onError:a,onStream:i}=t,c=l;l+=1,this.requestingMap[c]=!0,r?.(e,{onStream:e=>{this.requestingMap[c]&&i?.(e)},onUpdate:e=>{this.requestingMap[c]&&s(e)},onSuccess:e=>{this.requestingMap[c]&&(o(e),this.finishRequest(c))},onError:e=>{this.requestingMap[c]&&(a(e),this.finishRequest(c))}},n)};isRequesting(){return Object.keys(this.requestingMap).length>0}}function d(e){const{request:t,...n}=e;return r.useMemo((()=>[new u({request:t||c({baseURL:n.baseURL,model:n.model,dangerouslyApiKey:n.dangerouslyApiKey}).create,...n})]),[e?.baseURL,e?.dangerouslyApiKey,e?.model])}},34114:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(56790),s=n(67294);function o(e){const{defaultMessages:t,agent:n,requestFallback:o,requestPlaceholder:a,parser:i,transformMessage:c,transformStream:l,resolveAbortController:u}=e,d=s.useRef(0),[m,f,p]=function(e){const[,t]=s.useState(0),n=s.useRef("function"==typeof e?e():e),r=s.useCallback((e=>{n.current="function"==typeof e?e(n.current):e,t((e=>e+1))}),[]),o=s.useCallback((()=>n.current),[]);return[n.current,r,o]}((()=>(t||[]).map(((e,t)=>({id:`default_${t}`,status:"local",...e}))))),g=(e,t)=>{const n={id:`msg_${d.current}`,message:e,status:t};return d.current+=1,n},h=s.useMemo((()=>{const e=[];return m.forEach((t=>{const n=i?i(t.message):t.message,r=(s=n,Array.isArray(s)?s:[s]);var s;r.forEach(((n,s)=>{let o=t.id;r.length>1&&(o=`${o}_${s}`),e.push({id:o,message:n,status:t.status})}))})),e}),[m]),y=e=>e.filter((e=>"loading"!==e.status&&"error"!==e.status)).map((e=>e.message)),v=()=>y(p()),b=e=>{const{chunk:t,chunks:n,originMessage:r}=e;if("function"==typeof c)return c(e);if(t)return t;if(Array.isArray(n)){return r||(n?.length>0?n?.[n?.length-1]:void 0)}return n};return{onRequest:(0,r.zX)((e=>{if(!n)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let t,r=null,s={};if(e&&"object"==typeof e&&"message"in e){const{message:n,...r}=e;t=n,s=r}else t=e;f((e=>{let n=[...e,g(t,"local")];if(a){let e;e="function"==typeof a?a(t,{messages:y(n)}):a;const s=g(e,"loading");r=s.id,n=[...n,s]}return n}));let i=null;const c=(e,t,n)=>{let s=p().find((e=>e.id===i));if(s)f((r=>r.map((r=>{if(r.id===i){const s=b({originMessage:r.message,chunk:t,chunks:n,status:e});return{...r,message:s,status:e}}return r}))));else{const o=b({chunk:t,status:e,chunks:n});s=g(o,e),f((e=>[...e.filter((e=>e.id!==r)),s])),i=s.id}return s};n.request({message:t,messages:v(),...s},{onUpdate:e=>{c("loading",e,[])},onSuccess:e=>{c("success",void 0,e)},onError:async e=>{if(o){let n;n="function"==typeof o?await o(t,{error:e,messages:v()}):o,f((e=>[...e.filter((e=>e.id!==r&&e.id!==i)),g(n,"error")]))}else f((e=>e.filter((e=>e.id!==r&&e.id!==i))))},onStream:e=>{u?.(e)}},l)})),messages:m,parsedMessages:h,setMessages:f}}},78205:function(e,t,n){"use strict";n.d(t,{Z:function(){return h}});var r=n(71471),s=n(86250),o=n(93967),a=n.n(o),i=n(67294),c=n(21450),l=n(36158),u=n(83262),d=n(43495);const m=e=>{const{componentCls:t,calc:n}=e,r=n(e.fontSizeHeading3).mul(e.lineHeightHeading3).equal(),s=n(e.fontSize).mul(e.lineHeight).equal();return{[t]:{gap:e.padding,[`${t}-icon`]:{height:n(r).add(s).add(e.paddingXXS).equal(),display:"flex",img:{height:"100%"}},[`${t}-content-wrapper`]:{gap:e.paddingXS,flex:"auto",minWidth:0,[`${t}-title-wrapper`]:{gap:e.paddingXS},[`${t}-title`]:{margin:0},[`${t}-extra`]:{marginInlineStart:"auto"}}}}},f=e=>{const{componentCls:t}=e;return{[t]:{"&-filled":{paddingInline:e.padding,paddingBlock:e.paddingSM,background:e.colorFillContent,borderRadius:e.borderRadiusLG},"&-borderless":{[`${t}-title`]:{fontSize:e.fontSizeHeading3,lineHeight:e.lineHeightHeading3}}}}};var p=(0,d.I$)("Welcome",(e=>{const t=(0,u.IX)(e,{});return[m(t),f(t)]}),(()=>({})));function g(e,t){const{prefixCls:n,rootClassName:o,className:u,style:d,variant:m="filled",classNames:f={},styles:g={},icon:h,title:y,description:v,extra:b}=e,{direction:w,getPrefixCls:$}=(0,l.Z)(),S=$("welcome",n),x=(0,c.Z)("welcome"),[E,R,k]=p(S),H=i.useMemo((()=>{if(!h)return null;let e=h;return"string"==typeof h&&h.startsWith("http")&&(e=i.createElement("img",{src:h,alt:"icon"})),i.createElement("div",{className:a()(`${S}-icon`,x.classNames.icon,f.icon),style:g.icon},e)}),[h]),C=i.useMemo((()=>y?i.createElement(r.Z.Title,{level:4,className:a()(`${S}-title`,x.classNames.title,f.title),style:g.title},y):null),[y]),Z=i.useMemo((()=>b?i.createElement("div",{className:a()(`${S}-extra`,x.classNames.extra,f.extra),style:g.extra},b):null),[b]);return E(i.createElement(s.Z,{ref:t,className:a()(S,x.className,u,o,R,k,`${S}-${m}`,{[`${S}-rtl`]:"rtl"===w}),style:d},H,i.createElement(s.Z,{vertical:!0,className:`${S}-content-wrapper`},b?i.createElement(s.Z,{align:"flex-start",className:`${S}-title-wrapper`},C,Z):C,v&&i.createElement(r.Z.Text,{className:a()(`${S}-description`,x.classNames.description,f.description),style:g.description},v))))}var h=i.forwardRef(g)},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var s=0,o=function(){};return{s:o,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){c=!0,a=e},f:function(){try{i||null==n.return||n.return()}finally{if(c)throw a}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);