from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, IntField, DictField, <PERSON><PERSON>anField
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from bson import ObjectId

# MongoEngine 模型
class SystemLog(Document):
    meta = {
        'collection': 'system_logs',
        'indexes': [
            'user_id',
            'route',
            'created_at'
        ]
    }
    user_id = IntField(required=True)
    user_name = StringField(required=True)
    route = StringField(required=True)  # 访问路由
    method = StringField(required=True)  # HTTP方法
    params = DictField(default={})  # 请求参数
    status_code = IntField(required=True)  # 响应状态码
    success = BooleanField(default=True)  # 访问状态
    ip_address = StringField()  # 访问IP
    user_agent = StringField()  # 用户代理
    response_time = IntField()  # 响应时间(毫秒)
    created_at = DateTimeField(default=datetime.now)  # 创建时间

# Pydantic 模型
class SystemLogBase(BaseModel):
    user_id: int
    user_name: str
    route: str
    method: str
    params: Dict[str, Any] = {}
    status_code: int
    success: bool = True
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    response_time: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.now)

class SystemLogCreate(SystemLogBase):
    pass

class SystemLogResponse(SystemLogBase):
    id: int
