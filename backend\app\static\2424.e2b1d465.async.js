"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2424],{22424:function(e,t,n){n.d(t,{Z:function(){return ce}});const{entries:o,setPrototypeOf:r,isFrozen:i,getPrototypeOf:a,getOwnPropertyDescriptor:l}=Object;let{freeze:c,seal:s,create:u}=Object,{apply:m,construct:p}="undefined"!=typeof Reflect&&Reflect;c||(c=function(e){return e}),s||(s=function(e){return e}),m||(m=function(e,t,n){return e.apply(t,n)}),p||(p=function(e,t){return new e(...t)});const f=v(Array.prototype.forEach),d=v(Array.prototype.lastIndexOf),h=v(Array.prototype.pop),g=v(Array.prototype.push),T=v(Array.prototype.splice),y=v(String.prototype.toLowerCase),E=v(String.prototype.toString),A=v(String.prototype.match),_=v(String.prototype.replace),S=v(String.prototype.indexOf),N=v(String.prototype.trim),b=v(Object.prototype.hasOwnProperty),w=v(RegExp.prototype.test),R=(O=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p(O,t)});var O;function v(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return m(e,t,o)}}function C(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;r&&r(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(i(t)||(t[o]=e),r=e)}e[r]=!0}return e}function D(e){for(let t=0;t<e.length;t++){b(e,t)||(e[t]=null)}return e}function L(e){const t=u(null);for(const[n,r]of o(e)){b(e,n)&&(Array.isArray(r)?t[n]=D(r):r&&"object"==typeof r&&r.constructor===Object?t[n]=L(r):t[n]=r)}return t}function x(e,t){for(;null!==e;){const n=l(e,t);if(n){if(n.get)return v(n.get);if("function"==typeof n.value)return v(n.value)}e=a(e)}return function(){return null}}const k=c(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),I=c(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),M=c(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),U=c(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),z=c(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),P=c(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),H=c(["#text"]),F=c(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),B=c(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),W=c(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),G=c(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Y=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),j=s(/<%[\w\W]*|[\w\W]*%>/gm),X=s(/\$\{[\w\W]*/gm),q=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),$=s(/^aria-[\-\w]+$/),K=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),V=s(/^(?:\w+script|data):/i),Z=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=s(/^html$/i),Q=s(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:$,ATTR_WHITESPACE:Z,CUSTOM_ELEMENT:Q,DATA_ATTR:q,DOCTYPE_NAME:J,ERB_EXPR:j,IS_ALLOWED_URI:K,IS_SCRIPT_OR_DATA:V,MUSTACHE_EXPR:Y,TMPLIT_EXPR:X});const te=1,ne=3,oe=7,re=8,ie=9,ae=function(){return"undefined"==typeof window?null:window},le=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML(e){return e},createScriptURL(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}};var ce=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ae();const n=t=>e(t);if(n.version="3.2.5",n.removed=[],!t||!t.document||t.document.nodeType!==ie||!t.Element)return n.isSupported=!1,n;let{document:r}=t;const i=r,a=i.currentScript,{DocumentFragment:l,HTMLTemplateElement:s,Node:m,Element:p,NodeFilter:O,NamedNodeMap:v=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:D,DOMParser:Y,trustedTypes:j}=t,X=p.prototype,q=x(X,"cloneNode"),$=x(X,"remove"),V=x(X,"nextSibling"),Z=x(X,"childNodes"),Q=x(X,"parentNode");if("function"==typeof s){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let ce,se="";const{implementation:ue,createNodeIterator:me,createDocumentFragment:pe,getElementsByTagName:fe}=r,{importNode:de}=i;let he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof o&&"function"==typeof Q&&ue&&void 0!==ue.createHTMLDocument;const{MUSTACHE_EXPR:ge,ERB_EXPR:Te,TMPLIT_EXPR:ye,DATA_ATTR:Ee,ARIA_ATTR:Ae,IS_SCRIPT_OR_DATA:_e,ATTR_WHITESPACE:Se,CUSTOM_ELEMENT:Ne}=ee;let{IS_ALLOWED_URI:be}=ee,we=null;const Re=C({},[...k,...I,...M,...z,...H]);let Oe=null;const ve=C({},[...F,...B,...W,...G]);let Ce=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),De=null,Le=null,xe=!0,ke=!0,Ie=!1,Me=!0,Ue=!1,ze=!0,Pe=!1,He=!1,Fe=!1,Be=!1,We=!1,Ge=!1,Ye=!0,je=!1;const Xe="user-content-";let qe=!0,$e=!1,Ke={},Ve=null;const Ze=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Je=null;const Qe=C({},["audio","video","img","source","image","track"]);let et=null;const tt=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),nt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",rt="http://www.w3.org/1999/xhtml";let it=rt,at=!1,lt=null;const ct=C({},[nt,ot,rt],E);let st=C({},["mi","mo","mn","ms","mtext"]),ut=C({},["annotation-xml"]);const mt=C({},["title","style","font","a","script"]);let pt=null;const ft=["application/xhtml+xml","text/html"],dt="text/html";let ht=null,gt=null;const Tt=r.createElement("form"),yt=function(e){return e instanceof RegExp||e instanceof Function},Et=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!gt||gt!==e){if(e&&"object"==typeof e||(e={}),e=L(e),pt=-1===ft.indexOf(e.PARSER_MEDIA_TYPE)?dt:e.PARSER_MEDIA_TYPE,ht="application/xhtml+xml"===pt?E:y,we=b(e,"ALLOWED_TAGS")?C({},e.ALLOWED_TAGS,ht):Re,Oe=b(e,"ALLOWED_ATTR")?C({},e.ALLOWED_ATTR,ht):ve,lt=b(e,"ALLOWED_NAMESPACES")?C({},e.ALLOWED_NAMESPACES,E):ct,et=b(e,"ADD_URI_SAFE_ATTR")?C(L(tt),e.ADD_URI_SAFE_ATTR,ht):tt,Je=b(e,"ADD_DATA_URI_TAGS")?C(L(Qe),e.ADD_DATA_URI_TAGS,ht):Qe,Ve=b(e,"FORBID_CONTENTS")?C({},e.FORBID_CONTENTS,ht):Ze,De=b(e,"FORBID_TAGS")?C({},e.FORBID_TAGS,ht):{},Le=b(e,"FORBID_ATTR")?C({},e.FORBID_ATTR,ht):{},Ke=!!b(e,"USE_PROFILES")&&e.USE_PROFILES,xe=!1!==e.ALLOW_ARIA_ATTR,ke=!1!==e.ALLOW_DATA_ATTR,Ie=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Me=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ue=e.SAFE_FOR_TEMPLATES||!1,ze=!1!==e.SAFE_FOR_XML,Pe=e.WHOLE_DOCUMENT||!1,Be=e.RETURN_DOM||!1,We=e.RETURN_DOM_FRAGMENT||!1,Ge=e.RETURN_TRUSTED_TYPE||!1,Fe=e.FORCE_BODY||!1,Ye=!1!==e.SANITIZE_DOM,je=e.SANITIZE_NAMED_PROPS||!1,qe=!1!==e.KEEP_CONTENT,$e=e.IN_PLACE||!1,be=e.ALLOWED_URI_REGEXP||K,it=e.NAMESPACE||rt,st=e.MATHML_TEXT_INTEGRATION_POINTS||st,ut=e.HTML_INTEGRATION_POINTS||ut,Ce=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ce.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ce.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ce.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ue&&(ke=!1),We&&(Be=!0),Ke&&(we=C({},H),Oe=[],!0===Ke.html&&(C(we,k),C(Oe,F)),!0===Ke.svg&&(C(we,I),C(Oe,B),C(Oe,G)),!0===Ke.svgFilters&&(C(we,M),C(Oe,B),C(Oe,G)),!0===Ke.mathMl&&(C(we,z),C(Oe,W),C(Oe,G))),e.ADD_TAGS&&(we===Re&&(we=L(we)),C(we,e.ADD_TAGS,ht)),e.ADD_ATTR&&(Oe===ve&&(Oe=L(Oe)),C(Oe,e.ADD_ATTR,ht)),e.ADD_URI_SAFE_ATTR&&C(et,e.ADD_URI_SAFE_ATTR,ht),e.FORBID_CONTENTS&&(Ve===Ze&&(Ve=L(Ve)),C(Ve,e.FORBID_CONTENTS,ht)),qe&&(we["#text"]=!0),Pe&&C(we,["html","head","body"]),we.table&&(C(we,["tbody"]),delete De.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw R('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw R('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ce=e.TRUSTED_TYPES_POLICY,se=ce.createHTML("")}else void 0===ce&&(ce=le(j,a)),null!==ce&&"string"==typeof se&&(se=ce.createHTML(""));c&&c(e),gt=e}},At=C({},[...I,...M,...U]),_t=C({},[...z,...P]),St=function(e){let t=Q(e);t&&t.tagName||(t={namespaceURI:it,tagName:"template"});const n=y(e.tagName),o=y(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===ot?t.namespaceURI===rt?"svg"===n:t.namespaceURI===nt?"svg"===n&&("annotation-xml"===o||st[o]):Boolean(At[n]):e.namespaceURI===nt?t.namespaceURI===rt?"math"===n:t.namespaceURI===ot?"math"===n&&ut[o]:Boolean(_t[n]):e.namespaceURI===rt?!(t.namespaceURI===ot&&!ut[o])&&(!(t.namespaceURI===nt&&!st[o])&&(!_t[n]&&(mt[n]||!At[n]))):!("application/xhtml+xml"!==pt||!lt[e.namespaceURI]))},Nt=function(e){g(n.removed,{element:e});try{Q(e).removeChild(e)}catch(t){$(e)}},bt=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Be||We)try{Nt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},wt=function(e){let t=null,n=null;if(Fe)e="<remove></remove>"+e;else{const t=A(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===pt&&it===rt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=ce?ce.createHTML(e):e;if(it===rt)try{t=(new Y).parseFromString(o,pt)}catch(e){}if(!t||!t.documentElement){t=ue.createDocument(it,"template",null);try{t.documentElement.innerHTML=at?se:o}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),it===rt?fe.call(t,Pe?"html":"body")[0]:Pe?t.documentElement:i},Rt=function(e){return me.call(e.ownerDocument||e,e,O.SHOW_ELEMENT|O.SHOW_COMMENT|O.SHOW_TEXT|O.SHOW_PROCESSING_INSTRUCTION|O.SHOW_CDATA_SECTION,null)},Ot=function(e){return e instanceof D&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof v)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},vt=function(e){return"function"==typeof m&&e instanceof m};function Ct(e,t,o){f(e,(e=>{e.call(n,t,o,gt)}))}const Dt=function(e){let t=null;if(Ct(he.beforeSanitizeElements,e,null),Ot(e))return Nt(e),!0;const o=ht(e.nodeName);if(Ct(he.uponSanitizeElement,e,{tagName:o,allowedTags:we}),e.hasChildNodes()&&!vt(e.firstElementChild)&&w(/<[/\w!]/g,e.innerHTML)&&w(/<[/\w!]/g,e.textContent))return Nt(e),!0;if(e.nodeType===oe)return Nt(e),!0;if(ze&&e.nodeType===re&&w(/<[/\w]/g,e.data))return Nt(e),!0;if(!we[o]||De[o]){if(!De[o]&&xt(o)){if(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,o))return!1;if(Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(o))return!1}if(qe&&!Ve[o]){const t=Q(e)||e.parentNode,n=Z(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o){const r=q(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,V(e))}}}return Nt(e),!0}return e instanceof p&&!St(e)?(Nt(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!w(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ue&&e.nodeType===ne&&(t=e.textContent,f([ge,Te,ye],(e=>{t=_(t,e," ")})),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),Ct(he.afterSanitizeElements,e,null),!1):(Nt(e),!0)},Lt=function(e,t,n){if(Ye&&("id"===t||"name"===t)&&(n in r||n in Tt))return!1;if(ke&&!Le[t]&&w(Ee,t));else if(xe&&w(Ae,t));else if(!Oe[t]||Le[t]){if(!(xt(e)&&(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,e)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(e))&&(Ce.attributeNameCheck instanceof RegExp&&w(Ce.attributeNameCheck,t)||Ce.attributeNameCheck instanceof Function&&Ce.attributeNameCheck(t))||"is"===t&&Ce.allowCustomizedBuiltInElements&&(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,n)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(n))))return!1}else if(et[t]);else if(w(be,_(n,Se,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==S(n,"data:")||!Je[e]){if(Ie&&!w(_e,_(n,Se,"")));else if(n)return!1}else;return!0},xt=function(e){return"annotation-xml"!==e&&A(e,Ne)},kt=function(e){Ct(he.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Ot(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Oe,forceKeepAttr:void 0};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=ht(a);let u="value"===a?c:N(c);if(o.attrName=s,o.attrValue=u,o.keepAttr=!0,o.forceKeepAttr=void 0,Ct(he.uponSanitizeAttribute,e,o),u=o.attrValue,!je||"id"!==s&&"name"!==s||(bt(a,e),u=Xe+u),ze&&w(/((--!?|])>)|<\/(style|title)/i,u)){bt(a,e);continue}if(o.forceKeepAttr)continue;if(bt(a,e),!o.keepAttr)continue;if(!Me&&w(/\/>/i,u)){bt(a,e);continue}Ue&&f([ge,Te,ye],(e=>{u=_(u,e," ")}));const m=ht(e.nodeName);if(Lt(m,s,u)){if(ce&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(l);else switch(j.getAttributeType(m,s)){case"TrustedHTML":u=ce.createHTML(u);break;case"TrustedScriptURL":u=ce.createScriptURL(u)}try{l?e.setAttributeNS(l,a,u):e.setAttribute(a,u),Ot(e)?Nt(e):h(n.removed)}catch(e){}}}Ct(he.afterSanitizeAttributes,e,null)},It=function e(t){let n=null;const o=Rt(t);for(Ct(he.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Ct(he.uponSanitizeShadowNode,n,null),Dt(n),kt(n),n.content instanceof l&&e(n.content);Ct(he.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,r=null,a=null,c=null;if(at=!e,at&&(e="\x3c!--\x3e"),"string"!=typeof e&&!vt(e)){if("function"!=typeof e.toString)throw R("toString is not a function");if("string"!=typeof(e=e.toString()))throw R("dirty is not a string, aborting")}if(!n.isSupported)return e;if(He||Et(t),n.removed=[],"string"==typeof e&&($e=!1),$e){if(e.nodeName){const t=ht(e.nodeName);if(!we[t]||De[t])throw R("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof m)o=wt("\x3c!----\x3e"),r=o.ownerDocument.importNode(e,!0),r.nodeType===te&&"BODY"===r.nodeName||"HTML"===r.nodeName?o=r:o.appendChild(r);else{if(!Be&&!Ue&&!Pe&&-1===e.indexOf("<"))return ce&&Ge?ce.createHTML(e):e;if(o=wt(e),!o)return Be?null:Ge?se:""}o&&Fe&&Nt(o.firstChild);const s=Rt($e?e:o);for(;a=s.nextNode();)Dt(a),kt(a),a.content instanceof l&&It(a.content);if($e)return e;if(Be){if(We)for(c=pe.call(o.ownerDocument);o.firstChild;)c.appendChild(o.firstChild);else c=o;return(Oe.shadowroot||Oe.shadowrootmode)&&(c=de.call(i,c,!0)),c}let u=Pe?o.outerHTML:o.innerHTML;return Pe&&we["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&w(J,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u),Ue&&f([ge,Te,ye],(e=>{u=_(u,e," ")})),ce&&Ge?ce.createHTML(u):u},n.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Et(e),He=!0},n.clearConfig=function(){gt=null,He=!1},n.isValidAttribute=function(e,t,n){gt||Et({});const o=ht(e),r=ht(t);return Lt(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&g(he[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=d(he[e],t);return-1===n?void 0:T(he[e],n,1)[0]}return h(he[e])},n.removeHooks=function(e){he[e]=[]},n.removeAllHooks=function(){he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()}}]);