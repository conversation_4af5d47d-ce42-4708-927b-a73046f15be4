"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6527],{11488:function(e,n,t){function r(e){console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ modules-引用:",e);var n=e.find((function(e){return"AI 对话"===e.moduleName&&e.quoteList}));return console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ aiChatModule:",n),n&&n.quoteList?n.quoteList.map((function(e){return{id:e.id,type:"knowledge",reference:e}})):[]}function o(e){if(e){var n=[];return e.forEach((function(e){e.references&&(n=n.concat(e.references.map((function(n){return n.id=e.message_id+"-"+n.id,n.messageId=e.message_id,n}))))})),n}return[]}t.d(n,{P:function(){return o},n:function(){return r}})},98194:function(e,n,t){t.r(n),t.d(n,{default:function(){return we}});var r=t(97857),o=t.n(r),c=t(15009),a=t.n(c),s=t(64599),i=t.n(s),l=t(19632),u=t.n(l),d=t(99289),f=t.n(d),p=t(5574),m=t.n(p),x=t(11488),g=t(93461),v=t(34114),h=t(78205),k=t(78919),b=t(4628),y=t(9502),_=t(76654),Z=t(42075),w=t(71471),j=t(2453),S=t(17788),M=t(74330),C=t(86250),I=t(66309),P=t(83622),L=t(83062),D=t(55102),R=t(67294),N=t(10048),T=t(10981),E=t(78404),F=t(1832),z=t(14079),Y=t(66513),q=t(93045),H=t(71255),A=t(82061),B=t(47389),O=t(87784),G=t(25820),J=t(75750),W=t(12906),K=t(85175),U=t(43471),X=t(51042),$=t(27484),Q=t.n($),V=(0,t(24444).kc)((function(e){var n=e.token;return{addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{background:"".concat(n.colorBgLayout,"80"),width:"280px",height:"100%",display:"flex",flexDirection:"column"},chat:{height:"100%",width:"100%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},settings:{background:"".concat(n.colorBgLayout,"80"),width:"300px",height:"100%",borderLeft:"1px solid ".concat(n.colorBorder),padding:n.padding,overflowY:"auto"}}})),ee=t(93933),ne=t(13973),te=t(9783),re=t.n(te),oe=t(8232),ce=t(34041),ae=t(86125),se=t(78158);function ie(e){return le.apply(this,arguments)}function le(){return(le=f()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,se.N)("/api/llms",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ue=t(85893),de={m_id:0,temperature:.7,max_tokens:4096,top_p:1,frequency_penalty:0,presence_penalty:0,system_prompt:""},fe=(0,R.forwardRef)((function(e,n){var t=e.onChange,r=e.defaultModelId,c=void 0===r?0:r,s=(0,R.useState)([]),i=m()(s,2),l=i[0],u=i[1],d=(0,R.useState)(0),p=m()(d,2),x=p[0],g=p[1],v=(0,R.useState)(de),h=m()(v,2),k=h[0],b=h[1],y=(0,R.useState)(!0),_=m()(y,2),Z=_[0],w=_[1],S=(0,R.useRef)([]),M=(0,R.useRef)(!1),C=(0,R.useRef)(null);console.log("[ModelSelector] 渲染, selectedModel =",x,"defaultModelId =",c);var I=function(e){if(e){console.log("[ModelSelector] handleModelChange 被调用, modelId =",e);var n=S.current.find((function(n){return n.id===e}));if(n){console.log("[ModelSelector] 选择新模型:",n),g(e);var t=o()(o()({},k),{},{m_id:e,temperature:n.temperature||k.temperature,max_tokens:n.max_tokens||k.max_tokens,top_p:n.top_p||k.top_p,frequency_penalty:n.frequency_penalty||k.frequency_penalty,presence_penalty:n.presence_penalty||k.presence_penalty});console.log("[ModelSelector] 更新模型配置:",t),b(t)}else console.warn("[ModelSelector] 未找到对应模型, modelId =",e)}};(0,R.useImperativeHandle)(n,(function(){return{getCurrentModel:function(){return{model:S.current.find((function(e){return e.id===x})),config:k}},setModelById:(e=f()(a()().mark((function e(n){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("[ModelSelector] setModelById 被调用, modelId =",n,"模型已加载:",M.current),n){e.next=3;break}return e.abrupt("return",!1);case 3:if(e.prev=3,M.current){e.next=8;break}return console.log("[ModelSelector] 模型尚未加载，保存请求的modelId:",n),C.current=n,e.abrupt("return",!0);case 8:if(console.log("[ModelSelector] 当前模型列表:",S.current.map((function(e){return{id:e.id,name:e.name}}))),!(t=S.current.find((function(e){return e.id===n})))){e.next=16;break}return console.log("[ModelSelector] 找到目标模型:",t),I(n),e.abrupt("return",!0);case 16:return console.warn("[ModelSelector] 未找到目标模型, modelId =",n),e.abrupt("return",!1);case 18:e.next=24;break;case 20:return e.prev=20,e.t0=e.catch(3),console.error("[ModelSelector] 设置模型失败:",e.t0),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,null,[[3,20]])}))),function(n){return e.apply(this,arguments)})};var e}));var P=function(e,n){b((function(t){return o()(o()({},t),{},re()({},e,n))}))};return(0,R.useEffect)((function(){var e=function(){var e=f()(a()().mark((function e(){var n,t,r,o,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("[ModelSelector] 开始获取模型列表"),w(!0),e.next=5,ie({current:1,pageSize:100});case 5:(n=e.sent).success&&Array.isArray(n.data)?(console.log("[ModelSelector] 获取到模型列表, 总数:",n.data.length),console.log("[ModelSelector] 模型ID列表:",n.data.map((function(e){return e.id}))),u(n.data),S.current=n.data,(t=C.current||c)>0?(console.log("[ModelSelector] 尝试设置模型, targetModelId =",t),(r=n.data.find((function(e){return Number(e.id)===Number(t)})))?(console.log("[ModelSelector] 找到目标模型:",r),setTimeout((function(){I(r.id)}),0),C.current=null):(console.warn("[ModelSelector] 未找到目标模型, targetModelId =",t),n.data.length>0&&(o=n.data[0],console.log("[ModelSelector] 使用第一个模型作为默认:",o),setTimeout((function(){I(o.id)}),0)))):n.data.length>0&&(s=n.data[0],console.log("[ModelSelector] 使用第一个模型作为默认:",s),setTimeout((function(){I(s.id)}),0)),M.current=!0):(console.error("[ModelSelector] 获取模型列表失败:",n),j.ZP.error("获取模型列表失败")),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("[ModelSelector] 获取模型列表时出错:",e.t0),j.ZP.error("获取模型列表失败");case 13:return e.prev=13,w(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,9,13,16]])})));return function(){return e.apply(this,arguments)}}();e()}),[]),(0,R.useEffect)((function(){if(c>0&&M.current){console.log("[ModelSelector] defaultModelId变化，尝试设置新模型:",c);var e=S.current.find((function(e){return Number(e.id)===Number(c)}));e?(console.log("[ModelSelector] 找到目标模型:",e),I(e.id)):console.warn("[ModelSelector] 未找到目标模型, defaultModelId =",c)}}),[c]),(0,R.useEffect)((function(){null==t||t(k)}),[k,t]),(0,ue.jsx)("div",{children:(0,ue.jsxs)(oe.Z,{layout:"vertical",children:[(0,ue.jsx)("h4",{children:"模型"}),(0,ue.jsx)(oe.Z.Item,{children:(0,ue.jsx)(ce.default,{value:x?x.toString():void 0,onChange:function(e){return I(Number(e))},options:l.map((function(e){return{label:e.name||e.id.toString(),value:e.id.toString(),description:e.description}})),placeholder:"请选择模型",loading:Z})}),x>0&&(0,ue.jsxs)(ue.Fragment,{children:[(0,ue.jsx)("h4",{children:"模型参数"}),(0,ue.jsx)(oe.Z.Item,{label:"系统提示词",children:(0,ue.jsx)(D.Z.TextArea,{value:k.system_prompt,onChange:function(e){return P("system_prompt",e.target.value)},placeholder:"输入系统提示词"})}),(0,ue.jsx)(oe.Z.Item,{label:"温度(Temperature)",children:(0,ue.jsx)(ae.Z,{min:0,max:2,step:.1,value:k.temperature,onChange:function(e){return P("temperature",e)},tooltip:{formatter:function(e){return"".concat(e)}}})}),(0,ue.jsx)(oe.Z.Item,{label:"最大输出长度(Max Tokens)",children:(0,ue.jsx)(ae.Z,{min:100,max:32e3,step:100,value:k.max_tokens,onChange:function(e){return P("max_tokens",e)},tooltip:{formatter:function(e){return"".concat(e)}}})}),(0,ue.jsx)(oe.Z.Item,{label:"Top P",children:(0,ue.jsx)(ae.Z,{min:0,max:1,step:.01,value:k.top_p,onChange:function(e){return P("top_p",e)},tooltip:{formatter:function(e){return"".concat(e)}}})})]})]})})})),pe=fe,me=t(35312),xe=function(e,n){return(0,ue.jsxs)(Z.Z,{align:"start",children:[e,(0,ue.jsx)("span",{children:n})]})};function ge(e){return e+"-"+Date.now()}var ve=[{key:"1",label:xe((0,ue.jsx)(F.Z,{style:{color:"#FF4D4F"}}),"基础能力测试"),description:"测试LLM的基础理解和推理能力",children:[{key:"1-1",description:"请解释什么是大语言模型(LLM)以及它的主要应用场景"},{key:"1-2",description:"给出一个复杂的数学问题,请一步步解释解题思路"},{key:"1-3",description:"请对比分析GPT-3和GPT-4的主要区别和优势"}]},{key:"2",label:xe((0,ue.jsx)(z.Z,{style:{color:"#1890FF"}}),"专业领域测试"),description:"测试LLM在特定领域的专业知识",children:[{key:"2-1",icon:(0,ue.jsx)(Y.Z,{}),description:"请详细解释量子计算的基本原理和应用前景"},{key:"2-2",icon:(0,ue.jsx)(q.Z,{}),description:"从医学角度分析新冠病毒的变异机制"},{key:"2-3",icon:(0,ue.jsx)(H.Z,{}),description:"分析当前人工智能在金融领域的应用现状"}]},{key:"3",label:xe((0,ue.jsx)(z.Z,{style:{color:"#52C41A"}}),"创造力测试"),description:"测试LLM的创造性思维能力",children:[{key:"3-1",icon:(0,ue.jsx)(Y.Z,{}),description:"请创作一个科幻短篇故事,主题是人工智能"},{key:"3-2",icon:(0,ue.jsx)(q.Z,{}),description:"设计一个创新的解决方案来应对气候变化问题"},{key:"3-3",icon:(0,ue.jsx)(H.Z,{}),description:"从多个角度分析未来10年人类社会的可能发展趋势"}]}],he=[{key:"clearConversation",description:"清空对话",icon:(0,ue.jsx)(A.Z,{style:{color:"#1890FF"}})}],ke=(0,T.bG)(),be=(0,E.kH)(),ye=(0,N.Z)({html:!0,breaks:!0}),_e=function(e){return(0,ue.jsx)(w.Z,{style:{marginBottom:0},children:(0,ue.jsx)("div",{dangerouslySetInnerHTML:{__html:ye.render(e)}})})},Ze="llmchat",we=function(){var e,n=V().styles,t=(0,R.useState)(window.innerHeight),r=m()(t,1)[0],c=R.useRef(),s=(0,R.useRef)(null),l=(0,me.useLocation)(),d=R.useState(!1),p=m()(d,2),w=p[0],N=p[1],E=R.useState(""),F=m()(E,2),z=F[0],Y=F[1],q=R.useState([]),H=m()(q,2),$=H[0],te=H[1],re=R.useState(),oe=m()(re,2),ce=oe[0],ae=oe[1],se=(0,R.useState)(!1),ie=m()(se,2),le=ie[0],de=ie[1],fe=(0,R.useState)(0),xe=m()(fe,2),ye=xe[0],we=xe[1],je=(0,R.useState)(!1),Se=m()(je,2),Me=Se[0],Ce=Se[1],Ie=(0,R.useState)(""),Pe=m()(Ie,2),Le=Pe[0],De=Pe[1],Re=(0,R.useState)(""),Ne=m()(Re,2),Te=Ne[0],Ee=Ne[1],Fe=(0,R.useState)([]),ze=m()(Fe,2),Ye=ze[0],qe=ze[1],He=(0,R.useRef)(null),Ae=(0,R.useState)(!1),Be=m()(Ae,2),Oe=Be[0],Ge=Be[1],Je=(0,R.useState)(""),We=m()(Je,2),Ke=We[0],Ue=We[1],Xe=function(e){Ue(e),Ge(!0)},$e=function(e){var n=Ye.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){j.ZP.success("复制成功")})).catch((function(){j.ZP.error("复制失败")}))},Qe=(0,g.Z)({request:(e=f()(a()().mark((function e(n,t){var r,o,l,d,f,p,m,g,v,h,k,b,y,_,Z,w,S,M,C,I,P,L,D,R,N,E,F,z,Y,q,H,A,B,O,G,J,W,K,U,X;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,o=n.message,l=t.onSuccess,d=t.onUpdate,f=t.onError,e.prev=2,!le){e.next=6;break}return j.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(de(!0),h=o?o.id:ge(c.current),o||l({content:"出现了异常:",role:"assistant",id:h,references:[],query:[],collected:!1}),k={conversation_id:c.current||"",message_id:h,meta_data:{},extra:{},role:o?o.role:"user",content:o?o.content:"",app_info:Ze,user_id:null==ke?void 0:ke.id,user_name:null==ke?void 0:ke.name,references:[],token_count:null,price:null,collected:!1,created_at:Q()().format("YYYY-MM-DD HH:mm:ss")},qe((function(e){var n=[].concat(u()(e),[k]);return console.log("更新后的消息列表:",n),n})),b=(0,T.bW)(),c.current){e.next=15;break}throw j.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",c.current),console.log("extra===>",null===(p=s.current)||void 0===p?void 0:p.getCurrentModel().config),y={conversation_id:c.current,app_info:Ze,user_id:null==ke?void 0:ke.id,user_name:null==ke?void 0:ke.name,extra:null===(m=s.current)||void 0===m?void 0:m.getCurrentModel().config,messages:r},_={id:ge(c.current),role:"user",content:"",references:[],collected:!1},Z=!1,w="",S=[],d(_),console.log("content===>",y),e.next=26,fetch("/api/chat/v1/completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(b)},body:JSON.stringify(y)});case 26:if((M=e.sent).ok){e.next=29;break}throw new Error("HTTP 错误！状态码：".concat(M.status));case 29:if(C=null===(g=M.body)||void 0===g?void 0:g.getReader()){e.next=32;break}throw new Error("当前浏览器不支持 ReadableStream。");case 32:I=new TextDecoder("utf-8"),P={conversation_id:c.current||"",message_id:_.id,meta_data:{},extra:null===(v=s.current)||void 0===v?void 0:v.getCurrentModel().config,role:"assistant",content:"",app_info:Ze,user_id:null==ke?void 0:ke.id,user_name:null==ke?void 0:ke.name,references:[],token_count:null,price:null,collected:!1,created_at:Q()().format("YYYY-MM-DD HH:mm:ss")};case 34:if(Z){e.next=102;break}return e.next=37,C.read();case 37:L=e.sent,D=L.value,L.done&&(Z=!0),w+=I.decode(D,{stream:!0}),R=w.split("\n\n"),w=R.pop()||"",N=i()(R),e.prev=45,N.s();case 47:if((E=N.n()).done){e.next=92;break}if(""!==(F=E.value).trim()){e.next=51;break}return e.abrupt("continue",90);case 51:z=F.split("\n"),Y=null,q=null,H=i()(z);try{for(H.s();!(A=H.n()).done;)(B=A.value).startsWith("event: ")?Y=B.substring(7).trim():B.startsWith("data: ")&&(q=B.substring(6))}catch(e){H.e(e)}finally{H.f()}if(!q){e.next=90;break}e.t0=Y,e.next="answer"===e.t0?60:"moduleStatus"===e.t0?72:"appStreamResponse"===e.t0?74:"flowResponses"===e.t0?76:"end"===e.t0?78:"error"===e.t0?80:90;break;case 60:if("[DONE]"===q){e.next=71;break}e.prev=61,G=JSON.parse(q),(J=(null===(O=G.choices[0])||void 0===O||null===(O=O.delta)||void 0===O?void 0:O.content)||"")&&(_.content+=J,d(_)),e.next=71;break;case 67:return e.prev=67,e.t1=e.catch(61),console.error("Error parsing answer data:",e.t1),e.abrupt("return",l({content:"出现了异常:"+q,role:"assistant",id:ge(c.current),references:[],collected:!1}));case 71:return e.abrupt("break",90);case 72:try{W=JSON.parse(q),console.log("模块状态：",W)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",90);case 74:try{K=JSON.parse(q),console.log("appStreamData===>",K),S=(0,x.n)(K),_.references=S}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",90);case 76:try{console.log("flowResponsesData",q)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",90);case 78:return Z=!0,e.abrupt("break",90);case 80:e.prev=80,U=JSON.parse(q),d(U),e.next=89;break;case 85:throw e.prev=85,e.t2=e.catch(80),console.error("Error event received:",e.t2),e.t2;case 89:return e.abrupt("break",90);case 90:e.next=47;break;case 92:e.next=97;break;case 94:e.prev=94,e.t3=e.catch(45),N.e(e.t3);case 97:return e.prev=97,N.f(),e.finish(97);case 100:e.next=34;break;case 102:if(l(_),!_.content||""===_.content.trim()){e.next=110;break}return P.content=_.content,P.references=S,e.next=108,(0,ee.tn)(P);case 108:(X=e.sent).success?(P.message_id=X.data.message_id,console.log("创建消息成功，返回数据:",X.data),qe((function(e){var n=[].concat(u()(e),[X.data]);return console.log("更新后的消息列表:",n),n}))):j.ZP.error("消息上报失败");case 110:e.next=117;break;case 112:e.prev=112,e.t4=e.catch(2),console.log("error===>",e.t4),l({content:"出现错误："+e.t4,role:"assistant",id:ge(c.current),references:[],collected:!1}),f(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 117:return e.prev=117,de(!1),e.finish(117);case 120:case"end":return e.stop()}}),e,null,[[2,112,117,120],[45,94,97,100],[61,67],[80,85]])}))),function(n,t){return e.apply(this,arguments)})}),Ve=m()(Qe,1)[0],en=(0,v.Z)({agent:Ve}),nn=en.onRequest,tn=en.messages,rn=en.setMessages,on=function(e){ae(e),console.log("activeKey 设置",e),c.current=e},cn=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return o()(o()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return o()(o()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));te([].concat(u()(n),u()(t)))},an=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},sn=function(){var e=f()(a()().mark((function e(n){var t,r,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,de(!0),console.info("获取对话信息",n),t=Q()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,ee.$o)(n,{conversation_name:null,active_at:t,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),o=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==be?void 0:be.logo)||"/static/logo.png":(null==ke?void 0:ke.avatar)||"/avatar/default.jpeg"}}})),qe(r.messages),rn(o),on(n)):j.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,de(!1),ae(n),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(n){return e.apply(this,arguments)}}(),ln=function(){var e=f()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(c.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,ee.Db)(c.current);case 4:e.sent.success?(qe([]),rn([]),He.current&&He.current.updateReferenceList([])):j.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),un=function(){var e=f()(a()().mark((function e(){var n,t,r,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!le){e.next=3;break}return j.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,T.bG)())){e.next=19;break}return e.prev=5,t=(new Date).toLocaleString(),r="对话-".concat(t),e.next=10,(0,ee.Xw)({user_id:n.id,user_name:n.name,conversation_name:r,app_info:Ze});case 10:o=e.sent,cn([].concat(u()($),[{key:o.id||"",id:o.id||"",label:o.conversation_name||"",conversation_name:o.conversation_name||"",active_at:o.active_at||"",pinned_at:o.pinned_at,pinned:o.pinned||!1,messages:[]}])),on(o.id||""),ln(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),dn=function(){var e=f()(a()().mark((function e(n){var t,r,c,s,i;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=$.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,c=!r,e.prev=6,s=Q()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,ee.X1)(n,{conversation_name:null,active_at:null,pinned:c,pinned_at:s});case 10:i=$.map((function(e){return e.key===n?o()(o()({},e),{},{pinned:c}):e})),cn(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),fn=function(){var e=f()(a()().mark((function e(n){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ee.SJ)(n);case 3:t=$.filter((function(e){return e.key!==n})),cn(t),c.current===n&&t.length>0&&sn(t[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(n){return e.apply(this,arguments)}}(),pn=function(){var e=f()(a()().mark((function e(n,t){var r,c;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,$.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,ee.X1)(n,r);case 7:null!=(c=e.sent)&&c.success?te((function(e){return e.map((function(e){return e.key===n?o()(o()({},e),{},{label:t}):e}))})):j.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}(),mn=function(){var e=f()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!le){e.next=3;break}return j.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,sn(n);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,R.useEffect)((function(){var e=function(){var e=f()(a()().mark((function e(){var n,t,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=(0,T.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,ee.Mw)({user_id:n.id,app_info:Ze});case 5:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=15;break}if(0!==t.data.length){e.next=12;break}return e.next=10,un();case 10:e.next=15;break;case 12:r=an(t.data),cn(t.data),sn(r?r.id:t.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[Ze]),(0,R.useEffect)((function(){var e=new URLSearchParams(l.search).get("modelId");if(e){console.info("[LLMChat] 从URL获取模型ID:",e);var n=Number(e);isNaN(n)?console.warn("[LLMChat] URL中的modelId参数无效:",e):we(n)}}),[l.search]),(0,R.useEffect)((function(){s.current&&ye>0&&(console.log("[LLMChat] 尝试设置模型ID:",ye),s.current.setModelById(ye).then((function(e){e?console.log("[LLMChat] 模型设置成功:",ye):(console.warn("[LLMChat] 模型设置失败，可能ID不存在:",ye),j.ZP.warning("所选模型不存在，已使用默认模型"))})).catch((function(e){console.error("[LLMChat] 设置模型时发生错误:",e)})))}),[ye]);var xn=function(){var e=f()(a()().mark((function e(n){var t,r,o,c;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=Ye.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return j.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=Ye[t],o=Ye.slice(t),console.log("将要删除的消息:",o),e.next=12,(0,ee.qP)(o.map((function(e){return e.message_id})));case 12:e.sent.success||j.ZP.error("删除消息失败"),qe((function(e){return e.slice(0,t)})),rn((function(e){return e.slice(0,t)})),"assistant"===r.role?(c=Ye.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&nn({id:n,role:"user",content:c.content,references:[],query:[],collected:!1}):nn({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),j.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),j.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),gn=function(){var e=f()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:S.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return f()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,ee.$Z)(n);case 4:e.sent.success?(qe((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",Ye),rn((function(e){return e.filter((function(e){return e.message.id!==n}))})),console.log("delete messages===>",tn),j.ZP.success("消息及相关引用已删除")):j.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),j.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),vn=function(){var e=f()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,ee.bk)({message_id:n,collected:!t});case 3:e.sent.success?(j.ZP.success(t?"取消收藏成功":"收藏成功"),rn((function(e){return e.map((function(e){return e.id===n?o()(o()({},e),{},{message:o()(o()({},e.message),{},{collected:!t})}):e}))})),qe((function(e){return e.map((function(e){return e.message_id===n?o()(o()({},e),{},{collected:!t}):e}))}))):j.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),hn=function(){var e=f()(a()().mark((function e(n){var t,r,o;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!le){e.next=3;break}return j.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,o=t.description,"clearConversation"!==r){e.next=9;break}return e.next=7,ln();case 7:e.next=10;break;case 9:nn({id:ge(c.current),role:"user",content:o,references:[],query:[],collected:!1});case 10:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),kn=(0,ue.jsxs)(Z.Z,{direction:"vertical",size:16,className:n.placeholder,children:[(0,ue.jsx)(h.Z,{variant:"borderless",icon:(0,ue.jsx)("img",{src:(null==be?void 0:be.logo)||"/static/logo.png",alt:"logo"}),title:"欢迎使用 LLM 对话测试平台",description:"这是一个专业的大语言模型评测与对话平台，您可以在这里测试和比较不同模型的性能表现。"}),(0,ue.jsx)(k.Z,{title:"你想问什么呢?",items:ve,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)",border:0,flex:1}},onItemClick:hn})]}),bn=tn.length>0?tn.map((function(e){var n=e.id,t=e.message,r=e.status;return{key:c.current+"_"+n,loadingRender:function(){return(0,ue.jsxs)(Z.Z,{children:[(0,ue.jsx)(M.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&t.content.length<1,content:t.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:t.role,messageRender:_e,avatar:"local"===r?{src:(null==ke?void 0:ke.avatar)||"/avatar/default.jpeg"}:{src:(null==be?void 0:be.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,ue.jsxs)(C.Z,{children:[t.references.length>0&&(0,ue.jsxs)(I.Z,{bordered:!1,color:"blue",onClick:function(){return e=t.id,console.log("filterMessageReference===>",e),void(He.current&&(He.current.getFilterMessageId()===e?He.current.clearFilter():He.current.filterByMessageId(e)));var e},children:["引用:",t.references.length]}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(A.Z,{style:{color:"#ccc"}}),onClick:function(){return gn(t.id)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:t.collected?(0,ue.jsx)(G.Z,{style:{color:"#FFD700"}}):(0,ue.jsx)(J.Z,{style:{color:"#ccc"}}),onClick:function(){return vn(t.id,t.collected)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(W.Z,{style:{color:"#ccc"}}),onClick:function(){return Xe(t.id)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(K.Z,{style:{color:"#ccc"}}),onClick:function(){return $e(t.id)}})]}):(0,ue.jsxs)(C.Z,{children:[(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(U.Z,{style:{color:"#ccc"}}),onClick:function(){return xn(t.id)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(A.Z,{style:{color:"#ccc"}}),onClick:function(){return gn(t.id)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:t.collected?(0,ue.jsx)(G.Z,{style:{color:"#FFD700"}}):(0,ue.jsx)(J.Z,{style:{color:"#ccc"}}),onClick:function(){return vn(t.id,t.collected)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(W.Z,{style:{color:"#ccc"}}),onClick:function(){return Xe(t.id)}}),(0,ue.jsx)(P.ZP,{size:"small",type:"text",icon:(0,ue.jsx)(K.Z,{style:{color:"#ccc"}}),onClick:function(){return $e(t.id)}})]})}})):[{content:kn,variant:"borderless"}],yn=(0,ue.jsx)(b.Z.Header,{title:"Attachments",open:w,onOpenChange:N,styles:{content:{padding:0}}}),_n=(0,ue.jsxs)("div",{className:n.logo,children:[(0,ue.jsx)("span",{children:"对话记录"}),(0,ue.jsx)(L.Z,{title:"新对话",children:(0,ue.jsx)(P.ZP,{type:"text",icon:(0,ue.jsx)(X.Z,{}),onClick:un,style:{fontSize:"16px"}})})]}),Zn=(0,ue.jsx)(S.Z,{title:"修改对话标题",open:Me,onOk:function(){Te&&Le.trim()&&(pn(Te,Le.trim()),Ce(!1))},onCancel:function(){Ce(!1),De(""),Ee("")},children:(0,ue.jsx)(D.Z,{value:Le,onChange:function(e){return De(e.target.value)},placeholder:"请输入新的对话标题"})});return(0,ue.jsxs)("div",{className:n.layout,style:{height:r-56},children:[(0,ue.jsxs)("div",{className:n.menu,children:[_n,(0,ue.jsx)(y.Z,{items:$,activeKey:ce,onActiveChange:mn,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,ue.jsx)(B.Z,{})},{label:"置顶",key:"pin",icon:(0,ue.jsx)(O.Z,{})},{label:"删除",key:"delete",icon:(0,ue.jsx)(A.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":Ee(e.key),De(e.label),Ce(!0);break;case"pin":dn(e.key);break;case"delete":if(le)return void j.ZP.error("系统正在处理其他对话。请稍😊");fn(e.key)}}}},groupable:!0})]}),(0,ue.jsx)("div",{className:n.chat,children:ce?(0,ue.jsxs)(ue.Fragment,{children:[(0,ue.jsx)(_.Z.List,{items:bn,className:n.messages}),(0,ue.jsx)(k.Z,{items:he,onItemClick:hn}),(0,ue.jsx)(b.Z,{value:z,header:yn,onSubmit:function(e){console.log("nextContent===>",e),e&&(nn({id:ge(c.current),role:"user",content:e,references:[],collected:!1}),Y(""))},onChange:Y,loading:Ve.isRequesting(),className:n.sender})]}):kn}),(0,ue.jsx)("div",{className:n.settings,children:(0,ue.jsx)(pe,{ref:s,defaultModelId:ye>0?ye:void 0},"model-selector-".concat(ye))}),Zn,(0,ue.jsx)(ne.Z,{visible:Oe,messageId:Ke,conversationId:ce,appInfo:Ze,onClose:function(){return Ge(!1)}})]})}}}]);