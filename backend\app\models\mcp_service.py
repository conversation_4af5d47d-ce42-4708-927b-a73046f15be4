from mongoengine import Document, <PERSON>Field, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class mcpService(Document):
    meta = {
        'collection': 'mcp_service'
    }
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    type = StringField()  # hosted ,local
    is_active = BooleanField(required=True, default=True) # 是否启用
    url = StringField() # 服务地址
    transport = StringField() # http, websocket
    args = ListField() # 接口密钥

    usage_count = IntField(required=True, default=0) # 调用量
