"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6654],{76654:function(e,t,n){n.d(t,{Z:function(){return Z}});var r=n(87462),a=n(93967),o=n.n(a),i=n(67294),l=n(68997),s=n(21450),c=n(36158),d=n(8410);function u(e){return"string"==typeof e}var m=(e,t,n,r)=>{const a=i.useRef(""),[o,l]=i.useState(1),s=t&&u(e);(0,d.Z)((()=>{!s&&u(e)?l(e.length):u(e)&&u(a.current)&&0!==e.indexOf(a.current)&&l(1),a.current=e}),[e]),i.useEffect((()=>{if(s&&o<e.length){const e=setTimeout((()=>{l((e=>e+n))}),r);return()=>{clearTimeout(e)}}}),[o,t,e]);return[s?e.slice(0,o):e,s&&o<e.length]};var f=function(e){return i.useMemo((()=>{if(!e)return[!1,0,0,null];let t={step:1,interval:50,suffix:null};return"object"==typeof e&&(t={...t,...e}),[!0,t.step,t.interval,t.suffix]}),[e])};var p=({prefixCls:e})=>i.createElement("span",{className:`${e}-dot`},i.createElement("i",{className:`${e}-dot-item`,key:"item-1"}),i.createElement("i",{className:`${e}-dot-item`,key:"item-2"}),i.createElement("i",{className:`${e}-dot-item`,key:"item-3"})),g=n(11568),h=n(83262),y=n(43495);const b=e=>{const{componentCls:t,paddingSM:n,padding:r}=e;return{[t]:{[`${t}-content`]:{"&-filled,&-outlined,&-shadow":{padding:`${(0,g.bf)(n)} ${(0,g.bf)(r)}`,borderRadius:e.borderRadiusLG},"&-filled":{backgroundColor:e.colorFillContent},"&-outlined":{border:`1px solid ${e.colorBorderSecondary}`},"&-shadow":{boxShadow:e.boxShadowTertiary}}}}},v=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:a,padding:o,calc:i}=e,l=`${t}-content`;return{[t]:{[l]:{"&-round":{borderRadius:{_skip_check_:!0,value:i(n).mul(r).div(2).add(a).equal()},paddingInline:i(o).mul(1.25).equal()}},[`&-start ${l}-corner`]:{borderStartStartRadius:e.borderRadiusXS},[`&-end ${l}-corner`]:{borderStartEndRadius:e.borderRadiusXS}}}};var $=e=>{const{componentCls:t,padding:n}=e;return{[`${t}-list`]:{display:"flex",flexDirection:"column",gap:n,overflowY:"auto","&::-webkit-scrollbar":{width:8,backgroundColor:"transparent"},"&::-webkit-scrollbar-thumb":{backgroundColor:e.colorTextTertiary,borderRadius:e.borderRadiusSM},"&":{scrollbarWidth:"thin",scrollbarColor:`${e.colorTextTertiary} transparent`}}}};const x=new g.E4("loadingMove",{"0%":{transform:"translateY(0)"},"10%":{transform:"translateY(4px)"},"20%":{transform:"translateY(0)"},"30%":{transform:"translateY(-4px)"},"40%":{transform:"translateY(0)"}}),k=new g.E4("cursorBlink",{"0%":{opacity:1},"50%":{opacity:0},"100%":{opacity:1}}),E=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:a,colorText:o,calc:i}=e;return{[t]:{display:"flex",columnGap:a,[`&${t}-end`]:{justifyContent:"end",flexDirection:"row-reverse",[`& ${t}-content-wrapper`]:{alignItems:"flex-end"}},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-typing ${t}-content:last-child::after`]:{content:'"|"',fontWeight:900,userSelect:"none",opacity:1,marginInlineStart:"0.1em",animationName:k,animationDuration:"0.8s",animationIterationCount:"infinite",animationTimingFunction:"linear"},[`& ${t}-avatar`]:{display:"inline-flex",justifyContent:"center",alignSelf:"flex-start"},[`& ${t}-header, & ${t}-footer`]:{fontSize:n,lineHeight:r,color:e.colorText},[`& ${t}-header`]:{marginBottom:e.paddingXXS},[`& ${t}-footer`]:{marginTop:a},[`& ${t}-content-wrapper`]:{flex:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start",minWidth:0,maxWidth:"100%"},[`& ${t}-content`]:{position:"relative",boxSizing:"border-box",minWidth:0,maxWidth:"100%",color:o,fontSize:e.fontSize,lineHeight:e.lineHeight,minHeight:i(a).mul(2).add(i(r).mul(n)).equal(),wordBreak:"break-word",[`& ${t}-dot`]:{position:"relative",height:"100%",display:"flex",alignItems:"center",columnGap:e.marginXS,padding:`0 ${(0,g.bf)(e.paddingXXS)}`,"&-item":{backgroundColor:e.colorPrimary,borderRadius:"100%",width:4,height:4,animationName:x,animationDuration:"2s",animationIterationCount:"infinite",animationTimingFunction:"linear","&:nth-child(1)":{animationDelay:"0s"},"&:nth-child(2)":{animationDelay:"0.2s"},"&:nth-child(3)":{animationDelay:"0.4s"}}}}}}};var C=(0,y.I$)("Bubble",(e=>{const t=(0,h.IX)(e,{});return[E(t),$(t),b(t),v(t)]}),(()=>({})));const S=i.createContext({}),w=(e,t)=>{const{prefixCls:n,className:a,rootClassName:d,style:u,classNames:g={},styles:h={},avatar:y,placement:b="start",loading:v=!1,loadingRender:$,typing:x,content:k="",messageRender:E,variant:w="filled",shape:N,onTypingComplete:R,header:T,footer:I,_key:_,...H}=e,{onUpdate:M}=i.useContext(S),Z=i.useRef(null);i.useImperativeHandle(t,(()=>({nativeElement:Z.current})));const{direction:X,getPrefixCls:D}=(0,c.Z)(),z=D("bubble",n),B=(0,s.Z)("bubble"),[W,Y,F,P]=f(x),[j,q]=m(k,W,Y,F);i.useEffect((()=>{M?.()}),[j]);const G=i.useRef(!1);i.useEffect((()=>{q||v?G.current=!1:G.current||(G.current=!0,R?.())}),[q,v]);const[L,U,V]=C(z),O=o()(z,d,B.className,a,U,V,`${z}-${b}`,{[`${z}-rtl`]:"rtl"===X,[`${z}-typing`]:q&&!v&&!E&&!P}),A=i.useMemo((()=>i.isValidElement(y)?y:i.createElement(l.Z,y)),[y]),J=i.useMemo((()=>E?E(j):j),[j,E]),K=e=>"function"==typeof e?e(j,{key:_}):e;let Q;Q=v?$?$():i.createElement(p,{prefixCls:z}):i.createElement(i.Fragment,null,J,q&&P);let ee=i.createElement("div",{style:{...B.styles.content,...h.content},className:o()(`${z}-content`,`${z}-content-${w}`,N&&`${z}-content-${N}`,B.classNames.content,g.content)},Q);return(T||I)&&(ee=i.createElement("div",{className:`${z}-content-wrapper`},T&&i.createElement("div",{className:o()(`${z}-header`,B.classNames.header,g.header),style:{...B.styles.header,...h.header}},K(T)),ee,I&&i.createElement("div",{className:o()(`${z}-footer`,B.classNames.footer,g.footer),style:{...B.styles.footer,...h.footer}},K(I)))),L(i.createElement("div",(0,r.Z)({style:{...B.style,...u},className:O},H,{ref:Z}),y&&i.createElement("div",{style:{...B.styles.avatar,...h.avatar},className:o()(`${z}-avatar`,B.classNames.avatar,g.avatar)},A),ee))};var N=i.forwardRef(w),R=n(56790),T=n(64217);const I=({_key:e,...t},n)=>i.createElement(N,(0,r.Z)({},t,{_key:e,ref:t=>{t?n.current[e]=t:delete n.current?.[e]}})),_=i.memo(i.forwardRef(I)),H=(e,t)=>{const{prefixCls:n,rootClassName:a,className:l,items:s,autoScroll:d=!0,roles:u,...m}=e,f=(0,T.Z)(m,{attr:!0,aria:!0}),p=i.useRef(null),g=i.useRef({}),{getPrefixCls:h}=(0,c.Z)(),y=h("bubble",n),b=`${y}-list`,[v,$,x]=C(y),[k,E]=i.useState(!1);i.useEffect((()=>(E(!0),()=>{E(!1)})),[]);const w=function(e,t){const n=i.useCallback(((e,n)=>"function"==typeof t?t(e,n):t&&t[e.role]||{}),[t]);return i.useMemo((()=>(e||[]).map(((e,t)=>{const r=e.key??`preset_${t}`;return{...n(e,t),...e,key:r}}))),[e,n])}(s,u),[N,I]=i.useState(!0),[H,M]=i.useState(0);i.useEffect((()=>{d&&p.current&&N&&p.current.scrollTo({top:p.current.scrollHeight})}),[H]),i.useEffect((()=>{if(d){const e=w[w.length-2]?.key,t=g.current[e];if(t){const{nativeElement:e}=t,{top:n,bottom:r}=e.getBoundingClientRect(),{top:a,bottom:o}=p.current.getBoundingClientRect();n<o&&r>a&&(M((e=>e+1)),I(!0))}}}),[w.length]),i.useImperativeHandle(t,(()=>({nativeElement:p.current,scrollTo:({key:e,offset:t,behavior:n="smooth",block:r})=>{if("number"==typeof t)p.current.scrollTo({top:t,behavior:n});else if(void 0!==e){const t=g.current[e];if(t){const a=w.findIndex((t=>t.key===e));I(a===w.length-1),t.nativeElement.scrollIntoView({behavior:n,block:r})}}}})));const Z=(0,R.zX)((()=>{d&&M((e=>e+1))})),X=i.useMemo((()=>({onUpdate:Z})),[]);return v(i.createElement(S.Provider,{value:X},i.createElement("div",(0,r.Z)({},f,{className:o()(b,a,l,$,x,{[`${b}-reach-end`]:N}),ref:p,onScroll:e=>{const t=e.target;I(t.scrollHeight-Math.abs(t.scrollTop)-t.clientHeight<=1)}}),w.map((({key:e,...t})=>i.createElement(_,(0,r.Z)({},t,{key:e,_key:e,ref:g,typing:!!k&&t.typing})))))))};var M=i.forwardRef(H);N.List=M;var Z=N}}]);