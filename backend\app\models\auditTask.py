from mongoengine import Document, StringField, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class AuditTask(Document):
    meta = {
        'collection': 'audit_tasks'
    }
    _id = ObjectIdField(primary_key=True, default=lambda: ObjectId())
    name = StringField(required=True)  # 任务名称
    type_ids = ListField(StringField(), required=True)  # 任务类型
    # type = StringField(required=True)  # 任务类型：contract_extraction, resume_parsing, pii_detection, other
    status = StringField(required=True, default='pending_upload')  # 任务状态：pending_upload, analyzing, completed, failed
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    description = StringField()  # 任务描述
    progress = IntField(default=0)  # 任务进度，0-100
    estimated_time = StringField()  # 预计剩余时间
    logs = ListField(default=[])  # 处理日志，包含时间和消息
    user_id = IntField(required=True)  # 创建者ID
    user_name = StringField()  # 创建者名称
    file_count = IntField(required=True, default=0)  # 关联文件数量
    file_ids = ListField(StringField(), default=[])  # 关联的文件ID列表
    result_data = ListField(default=[])  # 分析结果数据
    error_message = StringField()  # 错误信息（如果状态为failed）
    is_deleted = BooleanField(default=False)  # 软删除标记
    

# Pydantic 基础模型
class AuditTaskBase(BaseModel):
    name: str
    type: str
    user_id: int
    user_name: Optional[str] = None

# 创建任务请求模型
class AuditTaskCreate(BaseModel):
    name: str
    description:Optional[str] = None
    type: str  # contract_extraction, resume_parsing, pii_detection, other

# 更新任务请求模型
class AuditTaskUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    file_ids: Optional[List[str]] = None
    result_data: Optional[List[dict]] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True
        extra = "allow"

# 任务响应模型
class AuditTaskResponse(AuditTaskBase):
    id: str
    status: str
    created_at: datetime
    updated_at: datetime
    description: Optional[str] = None
    file_count: int
    result_data: Optional[List[dict]] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

# 文件上传响应模型
class AuditFileResponse(BaseModel):
    id: str
    name: str
    url: str
    task_id: str
    status: str  # pending, processed, failed
    result: Optional[dict] = None

    class Config:
        from_attributes = True
