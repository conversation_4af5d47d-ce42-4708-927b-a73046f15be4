"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2077],{80047:function(e,t,s){s.r(t);var n=s(97857),i=s.n(n),l=s(19632),r=s.n(l),a=s(5574),c=s.n(a),d=s(67294),o=s(71471),u=s(34041),x=s(66309),p=s(42075),f=s(55102),y=s(2487),h=s(32983),j=s(4393),g=s(83622),v=s(85418),m=s(50136),Z=s(63960),b=s(96074),w=s(74330),S=s(42119),T=s(85265),C=s(8751),k=s(98165),N=s(18429),z=s(48489),I=s(88284),B=s(28508),L=s(13923),R=s(40110),A=s(55287),D=s(43471),P=s(82061),W=s(37446),_=s(51042),O=s(31545),U=s(79090),q=s(85893),E=o.Z.Title,F=o.Z.Text,G=u.default.Option;t.default=function(){var e=(0,d.useState)(""),t=c()(e,2),s=t[0],n=t[1],l=(0,d.useState)(""),a=c()(l,2),o=a[0],H=a[1],J=(0,d.useState)(!1),K=c()(J,2),M=K[0],Q=K[1],V=(0,d.useState)(0),X=c()(V,2),Y=X[0],$=X[1],ee=(0,d.useState)([]),te=c()(ee,2),se=te[0],ne=te[1],ie=(0,d.useState)(""),le=c()(ie,2),re=le[0],ae=le[1],ce=(0,d.useState)(!1),de=c()(ce,2),oe=de[0],ue=de[1],xe=(0,d.useState)(""),pe=c()(xe,2),fe=pe[0],ye=pe[1],he=(0,d.useState)(!1),je=c()(he,2),ge=je[0],ve=je[1],me=(0,d.useState)(null),Ze=c()(me,2),be=Ze[0],we=Ze[1],Se=(0,d.useState)("all"),Te=c()(Se,2),Ce=Te[0],ke=Te[1],Ne=(0,d.useState)("all"),ze=c()(Ne,2),Ie=ze[0],Be=ze[1],Le=[{id:"1",entityName:"中国银行股份有限公司",analysisType:"反洗钱风险分析",status:"completed",createTime:"2023-10-15 14:30:22",result:"pass"},{id:"2",entityName:"建设银行金融科技有限公司",analysisType:"制裁名单核查",status:"completed",createTime:"2023-10-14 09:15:36",result:"pass"},{id:"3",entityName:"平安保险集团股份有限公司",analysisType:"全面风险评估",status:"processing",createTime:"2023-10-13 16:42:10",result:"pending"},{id:"4",entityName:"招商银行股份有限公司",analysisType:"行业政策合规性",status:"failed",createTime:"2023-10-12 11:08:47",result:"fail"},{id:"5",entityName:"华为技术有限公司",analysisType:"全面风险评估",status:"completed",createTime:"2023-10-10 15:23:19",result:"pass"},{id:"6",entityName:"腾讯科技有限公司",analysisType:"反洗钱风险分析",status:"completed",createTime:"2023-10-09 10:15:30",result:"fail"},{id:"7",entityName:"阿里巴巴集团",analysisType:"制裁名单核查",status:"processing",createTime:"2023-10-08 09:45:12",result:"pending"},{id:"8",entityName:"百度在线网络技术有限公司",analysisType:"行业政策合规性",status:"completed",createTime:"2023-10-07 16:20:45",result:"pass"}],Re={total:Le.length,completed:Le.filter((function(e){return"completed"===e.status})).length,processing:Le.filter((function(e){return"processing"===e.status})).length,failed:Le.filter((function(e){return"failed"===e.status})).length},Ae=Le.filter((function(e){var t=e.entityName.toLowerCase().includes(fe.toLowerCase()),s="all"===Ce||"pass"===Ce&&"pass"===e.result||"fail"===Ce&&"fail"===e.result||"pending"===Ce&&"pending"===e.result,n="all"===Ie||"completed"===Ie&&"completed"===e.status||"processing"===Ie&&"processing"===e.status||"failed"===Ie&&"failed"===e.status;return t&&s&&n})),De=function(e){switch(e){case"completed":return(0,q.jsx)(C.Z,{style:{color:"#52c41a"}});case"processing":return(0,q.jsx)(k.Z,{spin:!0,style:{color:"#1890ff"}});case"failed":return(0,q.jsx)(N.Z,{style:{color:"#ff4d4f"}});default:return(0,q.jsx)(z.Z,{style:{color:"#d9d9d9"}})}},Pe=function(e){switch(e){case"pass":return(0,q.jsx)(x.Z,{icon:(0,q.jsx)(I.Z,{}),color:"success",children:"通过"});case"fail":return(0,q.jsx)(x.Z,{icon:(0,q.jsx)(B.Z,{}),color:"error",children:"不通过"});case"pending":return(0,q.jsx)(x.Z,{icon:(0,q.jsx)(z.Z,{}),color:"warning",children:"待定"});default:return(0,q.jsx)(x.Z,{color:"default",children:"未知"})}};return(0,q.jsxs)("div",{style:{height:"100vh",display:"flex",flexDirection:"column"},children:[(0,q.jsx)("div",{style:{padding:"16px 24px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fff"},children:(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,q.jsx)(E,{level:4,style:{margin:0},children:"对公客户合规性审核"}),(0,q.jsxs)("div",{style:{display:"flex"},children:[(0,q.jsxs)("div",{style:{marginRight:24,textAlign:"center"},children:[(0,q.jsx)(F,{type:"secondary",style:{fontSize:12},children:"任务总量"}),(0,q.jsx)("div",{style:{fontSize:16,fontWeight:"bold"},children:Re.total})]}),(0,q.jsxs)("div",{style:{marginRight:24,textAlign:"center"},children:[(0,q.jsx)(F,{type:"secondary",style:{fontSize:12},children:"已完成"}),(0,q.jsx)("div",{style:{fontSize:16,fontWeight:"bold",color:"#52c41a"},children:Re.completed})]}),(0,q.jsxs)("div",{style:{marginRight:24,textAlign:"center"},children:[(0,q.jsx)(F,{type:"secondary",style:{fontSize:12},children:"进行中"}),(0,q.jsx)("div",{style:{fontSize:16,fontWeight:"bold",color:"#1890ff"},children:Re.processing})]}),(0,q.jsxs)("div",{style:{textAlign:"center"},children:[(0,q.jsx)(F,{type:"secondary",style:{fontSize:12},children:"失败"}),(0,q.jsx)("div",{style:{fontSize:16,fontWeight:"bold",color:"#ff4d4f"},children:Re.failed})]})]})]})}),(0,q.jsxs)("div",{style:{display:"flex",flex:1,overflow:"hidden"},children:[(0,q.jsxs)("div",{style:{width:"50%",padding:"16px 24px",overflow:"auto",borderRight:"1px solid #f0f0f0"},children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,q.jsxs)(E,{level:5,style:{margin:0},children:[(0,q.jsx)(L.Z,{})," 历史报告列表"]}),(0,q.jsxs)(p.Z,{children:[(0,q.jsxs)(u.default,{placeholder:"审核结果",style:{width:100},value:Ce,onChange:function(e){return ke(e)},children:[(0,q.jsx)(G,{value:"all",children:"全部结果"}),(0,q.jsx)(G,{value:"pass",children:"通过"}),(0,q.jsx)(G,{value:"fail",children:"不通过"}),(0,q.jsx)(G,{value:"pending",children:"待定"})]}),(0,q.jsxs)(u.default,{placeholder:"分析状态",style:{width:100},value:Ie,onChange:function(e){return Be(e)},children:[(0,q.jsx)(G,{value:"all",children:"全部状态"}),(0,q.jsx)(G,{value:"completed",children:"已完成"}),(0,q.jsx)(G,{value:"processing",children:"进行中"}),(0,q.jsx)(G,{value:"failed",children:"失败"})]}),(0,q.jsx)(f.Z.Search,{placeholder:"搜索企业名称",style:{width:200},onSearch:function(e){return ye(e)},onChange:function(e){return ye(e.target.value)},allowClear:!0,prefix:(0,q.jsx)(R.Z,{})})]})]}),(0,q.jsx)(y.Z,{itemLayout:"vertical",dataSource:Ae,locale:{emptyText:(0,q.jsx)(h.Z,{description:"没有找到匹配的记录"})},renderItem:function(e){return(0,q.jsx)(j.Z,{style:{marginBottom:16},bodyStyle:{padding:"12px 16px"},hoverable:!0,children:(0,q.jsxs)("div",{style:{display:"flex",flexDirection:"column"},children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:12},children:[(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[Pe(e.result),(0,q.jsx)(F,{strong:!0,style:{fontSize:"15px",marginLeft:8},children:e.entityName})]}),(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,q.jsx)(g.ZP,{type:"link",icon:(0,q.jsx)(A.Z,{}),onClick:function(){return we(e),void ve(!0)},style:{padding:"0 8px",fontSize:"13px"},children:"查看报告"}),(0,q.jsx)(v.Z,{overlay:(0,q.jsxs)(m.Z,{children:[(0,q.jsx)(m.Z.Item,{icon:(0,q.jsx)(D.Z,{}),onClick:function(){return t=e,void console.log("重新分析",t);var t},children:"重新分析"},"reanalyze"),(0,q.jsx)(m.Z.Divider,{}),(0,q.jsx)(m.Z.Item,{icon:(0,q.jsx)(P.Z,{}),danger:!0,onClick:function(){return t=e,void console.log("删除记录",t);var t},children:"删除"},"delete")]}),trigger:["click"],children:(0,q.jsx)(g.ZP,{type:"text",icon:(0,q.jsx)(W.Z,{}),style:{marginLeft:4}})})]})]}),(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"13px",color:"rgba(0,0,0,0.45)"},children:[(0,q.jsx)("div",{children:e.analysisType}),(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,q.jsxs)("div",{style:{marginRight:8},children:[e.createTime.split(" ")[0]," ",(0,q.jsx)("span",{style:{marginLeft:4},children:e.createTime.split(" ")[1]})]}),De(e.status)]})]})]})})}}),Ae.length>0&&(0,q.jsx)("div",{style:{textAlign:"center",marginTop:16,marginBottom:16},children:(0,q.jsx)(g.ZP,{onClick:function(){ue(!0),setTimeout((function(){ue(!1)}),1e3)},loading:oe,style:{width:"100%"},children:oe?"加载中...":"加载更多"})})]}),(0,q.jsxs)("div",{style:{width:"50%",padding:"24px",display:"flex",flexDirection:"column",overflow:"auto",backgroundColor:"#fff",borderLeft:"1px solid #f0f0f0"},children:[(0,q.jsxs)("div",{style:{marginBottom:24},children:[(0,q.jsxs)(E,{level:5,style:{marginBottom:16},children:[(0,q.jsx)(_.Z,{})," 新建合规审核任务"]}),(0,q.jsx)("div",{style:{padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",marginBottom:"16px"},children:(0,q.jsx)(F,{type:"secondary",children:"快速开始: 选择企业 → 选择分析类型 → 提交任务 → 查看分析结果"})}),(0,q.jsxs)(p.Z,{direction:"vertical",style:{width:"100%"},children:[(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{strong:!0,children:"分析对象"}),(0,q.jsx)(Z.Z,{style:{width:"100%"},options:[{value:"中国银行股份有限公司"},{value:"建设银行金融科技有限公司"},{value:"平安保险集团股份有限公司"},{value:"招商银行股份有限公司"},{value:"华为技术有限公司"}],placeholder:"请输入企业名称（支持模糊搜索）",value:s,onChange:n,filterOption:function(e,t){return-1!==(null==t?void 0:t.value.toUpperCase().indexOf(e.toUpperCase()))}})]}),(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{strong:!0,children:"分析类型"}),(0,q.jsx)(u.default,{style:{width:"100%"},placeholder:"请选择分析任务类型",options:[{value:"反洗钱风险分析",label:"反洗钱风险分析"},{value:"制裁名单核查",label:"制裁名单核查"},{value:"行业政策合规性",label:"行业政策合规性"},{value:"全面风险评估",label:"全面风险评估"}],value:o,onChange:H})]}),(0,q.jsx)(g.ZP,{type:"primary",icon:(0,q.jsx)(O.Z,{}),onClick:function(){if(s){Q(!0),$(0),ne([{title:"信息获取",description:"正在获取企业基本信息...",status:"process"},{title:"数据整合",description:"整合内外部数据...",status:"wait"},{title:"风险识别",description:"识别潜在风险点...",status:"wait"},{title:"风险评估",description:"综合评估风险等级...",status:"wait"},{title:"报告生成",description:"生成合规报告...",status:"wait"}]);var e=0,t=setInterval((function(){$(e+=5),e>20&&e<=40?ne((function(e){var t=r()(e);return t[0]=i()(i()({},t[0]),{},{status:"finish"}),t[1]=i()(i()({},t[1]),{},{status:"process"}),t})):e>40&&e<=60?ne((function(e){var t=r()(e);return t[1]=i()(i()({},t[1]),{},{status:"finish"}),t[2]=i()(i()({},t[2]),{},{status:"process"}),t})):e>60&&e<=80?ne((function(e){var t=r()(e);return t[2]=i()(i()({},t[2]),{},{status:"finish"}),t[3]=i()(i()({},t[3]),{},{status:"process"}),t})):e>80&&e<100?ne((function(e){var t=r()(e);return t[3]=i()(i()({},t[3]),{},{status:"finish"}),t[4]=i()(i()({},t[4]),{},{status:"process"}),t})):e>=100&&(ne((function(e){var t=r()(e);return t[4]=i()(i()({},t[4]),{},{status:"finish"}),t})),ae("\n## 合规性审核报告：".concat(s,"\n\n### 基本信息\n- 企业名称：").concat(s,"\n- 分析类型：").concat(o||"全面风险评估","\n- 分析时间：").concat((new Date).toLocaleString("zh-CN"),"\n\n### 风险点识别\n1. **反洗钱合规风险**：未发现明显风险\n2. **制裁名单检查**：未在国际制裁名单中发现匹配\n3. **行业政策合规**：符合当前行业监管要求\n4. **负面舆情**：近6个月内未发现重大负面舆情\n\n### 建议措施\n- 建议定期（每季度）进行合规性复查\n- 关注该企业相关行业政策变动\n- 持续监控企业舆情变化\n\n### 结论\n经综合评估，").concat(s,"当前合规风险较低，建议可以正常开展业务合作。\n        ")),clearInterval(t),setTimeout((function(){Q(!1)}),1e3))}),200)}},disabled:!s||M,style:{marginTop:"8px"},block:!0,children:"提交任务"})]})]}),(0,q.jsx)(b.Z,{style:{margin:"0 0 24px 0"}}),(0,q.jsxs)("div",{style:{flex:1},children:[(0,q.jsxs)(E,{level:5,children:[(0,q.jsx)(k.Z,{spin:M})," 任务执行状态"]}),s||M||re?M?(0,q.jsxs)("div",{children:[(0,q.jsx)(w.Z,{indicator:(0,q.jsx)(U.Z,{style:{fontSize:24},spin:!0})}),(0,q.jsx)("div",{style:{marginTop:"16px"},children:(0,q.jsxs)(F,{children:["正在分析 ",s,"，当前进度 ",Y,"%"]})}),(0,q.jsx)("div",{style:{marginTop:"24px",marginBottom:"24px"},children:(0,q.jsx)(S.Z,{direction:"vertical",current:se.findIndex((function(e){return"process"===e.status})),items:se.map((function(e){return{title:e.title,description:e.description,status:e.status}}))})})]}):re?(0,q.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"16px",borderRadius:"8px",border:"1px solid #f0f0f0",whiteSpace:"pre-line"},children:re}):(0,q.jsx)(h.Z,{description:"任务尚未开始执行"}):(0,q.jsx)(h.Z,{description:(0,q.jsxs)("div",{children:[(0,q.jsx)("div",{children:"请选择企业并提交任务"}),(0,q.jsx)(F,{type:"secondary",style:{fontSize:"12px"},children:"分析结果将在此处显示"})]})})]})]})]}),(0,q.jsx)(T.Z,{title:"".concat((null==be?void 0:be.entityName)||""," - 合规性审核报告"),placement:"right",width:600,onClose:function(){return ve(!1)},open:ge,children:be&&(0,q.jsxs)("div",{children:[(0,q.jsxs)("div",{style:{marginBottom:16},children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8},children:[(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{type:"secondary",children:"分析类型:"})," ",be.analysisType]}),(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{type:"secondary",children:"创建时间:"})," ",be.createTime]})]}),(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{type:"secondary",children:"分析状态:"})," ",De(be.status)]}),(0,q.jsxs)("div",{children:[(0,q.jsx)(F,{type:"secondary",children:"分析结果:"})," ",Pe(be.result)]})]})]}),(0,q.jsx)(b.Z,{style:{margin:"16px 0"}}),(0,q.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"16px",borderRadius:"8px",border:"1px solid #f0f0f0",whiteSpace:"pre-line"},children:re||"\n## 合规性审核报告：".concat(be.entityName,"\n\n### 基本信息\n- 企业名称：").concat(be.entityName,"\n- 分析类型：").concat(be.analysisType,"\n- 分析时间：").concat(be.createTime,"\n- 分析结果：").concat("pass"===be.result?"通过":"fail"===be.result?"不通过":"待定","\n\n### 风险点识别\n1. **反洗钱合规风险**：").concat("pass"===be.result?"未发现明显风险":"发现潜在风险","\n2. **制裁名单检查**：").concat("pass"===be.result?"未在国际制裁名单中发现匹配":"存在潜在匹配项","\n3. **行业政策合规**：").concat("pass"===be.result?"符合当前行业监管要求":"存在不符合项","\n4. **负面舆情**：").concat("pass"===be.result?"近6个月内未发现重大负面舆情":"存在负面舆情","\n\n### 建议措施\n").concat("pass"===be.result?"- 建议定期（每季度）进行合规性复查\n- 关注该企业相关行业政策变动\n- 持续监控企业舆情变化":"- 建议进一步核实风险点\n- 提高监控频率\n- 考虑实施额外的风险控制措施","\n\n### 结论\n").concat("pass"===be.result?"经综合评估，".concat(be.entityName,"当前合规风险较低，建议可以正常开展业务合作。"):"fail"===be.result?"经综合评估，".concat(be.entityName,"当前存在较高合规风险，建议谨慎开展业务合作。"):"经综合评估，".concat(be.entityName,"当前合规状况需进一步核查，建议暂缓业务合作。"),"\n              ")})]})})]})}}}]);