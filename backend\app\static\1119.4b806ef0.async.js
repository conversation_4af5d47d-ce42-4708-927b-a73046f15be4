"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1119],{93933:function(n,t,e){e.d(t,{$Z:function(){return T},$o:function(){return d},Db:function(){return v},Mw:function(){return i},SJ:function(){return w},X1:function(){return y},Xw:function(){return f},bk:function(){return E},fx:function(){return C},qP:function(){return P},tn:function(){return x},zl:function(){return j}});var r=e(15009),a=e.n(r),u=e(99289),o=e.n(u),s=e(78158),c=e(10981);function i(n){return p.apply(this,arguments)}function p(){return(p=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function f(n){return l.apply(this,arguments)}function l(){return(l=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function d(n,t){return h.apply(this,arguments)}function h(){return(h=o()(a()().mark((function n(t,e){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/conversations/".concat(t,"/active"),{method:"PUT",data:e}).then((function(n){if(n.success&&n.data){var t=n.data,e=t.messages,r=void 0===e?[]:e,a=t.knowledge_ids,u=void 0===a?[]:a,o=t.uploaded_files;return{messages:r,knowledge_ids:u,uploaded_files:void 0===o?[]:o}}return{messages:[],knowledge_ids:[],uploaded_files:[]}})));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function v(n){return m.apply(this,arguments)}function m(){return(m=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function w(n){return b.apply(this,arguments)}function b(){return(b=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function y(n,t){return k.apply(this,arguments)}function k(){return(k=o()(a()().mark((function n(t,e){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/conversations/".concat(t),{method:"PUT",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function x(n){return g.apply(this,arguments)}function g(){return(g=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function T(n){return _.apply(this,arguments)}function _(){return(_=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function P(n){return N.apply(this,arguments)}function N(){return(N=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function E(n){return S.apply(this,arguments)}function S(){return(S=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function C(n){return Z.apply(this,arguments)}function Z(){return(Z=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function j(n){return O.apply(this,arguments)}function O(){return(O=o()(a()().mark((function n(t){var e,r;return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return e=(0,c.bW)(),n.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});case 3:if((r=n.sent).ok){n.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return n.abrupt("return",r);case 7:case"end":return n.stop()}}),n)})))).apply(this,arguments)}},13973:function(n,t,e){e.d(t,{Z:function(){return k}});var r=e(15009),a=e.n(r),u=e(99289),o=e.n(u),s=e(5574),c=e.n(s),i=e(67294),p=e(55102),f=e(2453),l=e(17788),d=e(84567),h=e(78158);function v(n){return m.apply(this,arguments)}function m(){return(m=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,h.N)("/api/feedbacks",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}var w=e(85893),b=p.Z.TextArea,y=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],k=function(n){var t=n.visible,e=n.messageId,r=n.conversationId,u=n.appInfo,s=n.onClose,p=i.useState(""),h=c()(p,2),m=h[0],k=h[1],x=i.useState([]),g=c()(x,2),T=g[0],_=g[1],P=function(){var n=o()(a()().mark((function n(){var t;return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(0!==T.length){n.next=3;break}return f.ZP.error("请至少选择一个反馈类型"),n.abrupt("return");case 3:return n.prev=3,t={message_id:e,conversation_id:r,app_info:u,content:m,feedback_types:T},console.log("feedbackData===>",t),n.next=8,v(t);case 8:n.sent.success?(f.ZP.success("感谢您的反馈！"),N()):f.ZP.error("提交反馈失败，请稍后重试"),n.next=16;break;case 12:n.prev=12,n.t0=n.catch(3),console.error("提交反馈失败:",n.t0),f.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return n.stop()}}),n,null,[[3,12]])})));return function(){return n.apply(this,arguments)}}(),N=function(){k(""),_([]),s()};return(0,w.jsxs)(l.Z,{title:"反馈问题",open:t,onOk:P,onCancel:N,okText:"提交",cancelText:"取消",children:[(0,w.jsxs)("div",{style:{marginBottom:16},children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,w.jsx)(d.Z.Group,{options:y,value:T,onChange:function(n){return _(n)}})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,w.jsx)(b,{value:m,onChange:function(n){return k(n.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}}}]);