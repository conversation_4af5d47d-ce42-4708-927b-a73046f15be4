#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
1358数据ES到ES迁移与结构化处理脚本
基于LangGraph实现，包含读取、处理、写入三个节点
"""
import json
import logging
import traceback
import re
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
import requests
from elasticsearch import Elasticsearch
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END
import time

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
SOURCE_ES_HOST = "**********"
SOURCE_ES_PORT = 9201
SOURCE_ES_USER = "jinxu"
SOURCE_ES_PASSWORD = "WiseWeb@123"
SOURCE_ES_INDEX = "media_level_wiseweb_crawler_website"

TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_USER = None
TARGET_ES_PASSWORD = None
TARGET_ES_INDEX = "pro_mcp_data_1358"

BATCH_SIZE = 100  # 批处理大小

# 大模型参数
embedding_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings"
embedding_api_key = "ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=="
embedding_name = "bge-m3"

llm_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_4b/v1"
llm_api_key = "MzNlYmY1ZGM0ODYwMzc5MjFmZDM4NmEyY2I2ZTAzNWI3ZGZiYzhhOQ=="
OPENAI_MODEL = "Qwen3-4B"


llm_service_url_2 = "http://************:11434/v1/"
llm_api_key_2 = "MzNlYmY1ZGM0ODYwMzc5MjFmZDM4NmEyY2I2ZTAzNWI3ZGZiYzhhOQ=="
OPENAI_MODEL_2 = "qwen3:4b"

llm = ChatOpenAI(
    temperature=0,
    model=OPENAI_MODEL,
    openai_api_base=llm_service_url,
    api_key=llm_api_key
)

llm2 = ChatOpenAI(
    temperature=0,
    model=OPENAI_MODEL_2,
    openai_api_base=llm_service_url_2,
    api_key=llm_api_key_2
)


embedding_config = {
    "api_key": embedding_api_key,
    "service_url": embedding_service_url,
    "embedding_name": embedding_name
}

def get_embedding(text, embedding_config):
    """获取文本的向量嵌入"""
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            query_embedding = embdd_response.json()['data'][0]['embedding']
            DB_VECTOR_DIMENSION = 1536
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                    logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
                else:
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                    logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")
            logger.info(f"最终查询向量维度: {len(query_embedding)}")
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取embedding时出错: {str(e)}")
        return None

def build_body_for_index(start_time, end_time, site_ids=None):
    """构建ES查询语句
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        site_ids: 可选的站点ID列表
    """
    time_field = "publishtime"
    sort_field = "publishtime"
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            time_field: {
                                "gte": start_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "sort": [{sort_field: "desc"}]  # 按时间降序排序
    }
    
    # 如果指定了site_ids参数，则添加过滤条件
    if site_ids:
        # 如果site_ids是字符串，则使用term查询
        if isinstance(site_ids, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "site_id": {
                        "value": site_ids
                    }
                }
            })
        # 如果site_ids是列表，则使用terms查询
        elif isinstance(site_ids, list) and len(site_ids) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "site_id": site_ids
                }
            })
    
    return body

def get_es_scroll_data_batched(index, query_body, batch_size=100):
    """滚动查询ES数据，并以批次方式返回
    
    Args:
        index: ES索引名称
        query_body: 查询条件
        batch_size: 批处理大小
        
    Yields:
        每批查询结果
    """
    es = Elasticsearch(
        [f"{SOURCE_ES_HOST}:{SOURCE_ES_PORT}"],
        http_auth=(SOURCE_ES_USER, SOURCE_ES_PASSWORD) if SOURCE_ES_USER else None,
        timeout=3600
    )
    
    sid = None
    try:
        # 初始搜索
        result = es.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
        sid = result['_scroll_id']
        scroll_size = result['hits']['total']['value']
        print(f"索引 {index} 总数据量: {scroll_size}")
        
        # 如果有结果，返回第一批数据
        if len(result['hits']['hits']) > 0:
            yield result['hits']['hits']
        
        # 继续滚动直到没有更多数据
        scroll_count = len(result['hits']['hits'])
        while scroll_count > 0:
            result = es.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
            batch_data = result['hits']['hits']
            scroll_count = len(batch_data)
            if scroll_count == 0:
                break
            yield batch_data
            
    except Exception as e:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
        traceback.print_exc()
    finally:
        if sid:
            try:
                es.clear_scroll(scroll_id=sid)
            except:
                pass
        print(f"索引 {index} 查询完成")

def analyze_text(title: str, content: str) -> Dict[str, Any]:
    """分析文本内容，提取关键信息，返回结构化字段"""
    # 构建提示词模板
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content="""你是一个专业的文本分析助手，需要分析中文新闻文章并提取以下信息：
1. 作者态度 (authorAttitude): 必须是以下之一：积极、中立、消极
2. 作者观点 (authorViewpoint): 概括作者的核心观点，及观点依据，包括但不限于机构对宏观经济的预期、对宏观政策的预期、对利率走势的预测、对投资的建议等
3. 人物实体 (characterEntity): 提取所有人物名称列表
4. 机构实体 (institutionalEntities): 提取所有机构名称列表
5. 地点实体 (locationEntity): 提取所有地点名称列表
6. 事件信息 (eventInfo): 提取主要事实事件列表
7. 摘要事实 (summaryFacts): 提取3-5个关键事实
8. 摘要 (summary): 对文章内容进行300字以内的摘要，要包括内容的关键信息

请以JSON格式返回，包含以上所有字段。
如: {"authorAttitude": "积极", "authorViewpoint": "支持该政策的实施", "characterEntity": ["张三"], "institutionalEntities": ["某公司"], "locationEntity": ["北京"], "eventInfo": ["签署合同"], "summaryFacts": ["会议顺利召开"], "summary": "这篇文章主要讨论了..."}"""),
        HumanMessage(content=f"""请分析以下文章：
标题: {title}
内容: {content}
/no_think""")
    ])
    
    chain = prompt | llm

    try:
        result = chain.invoke({})
        # 获取内容：先尝试 .content 属性，如果是对象
        raw_content = getattr(result, 'content', result)
        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)

        # 匹配第一个 JSON 块
        json_match = re.search(r'({.*?})', text, flags=re.DOTALL)
        if not json_match:
            logger.error("未找到JSON内容")
            return get_default_result()
            
        json_str = json_match.group(1)

        # 尝试解析
        result_dict = json.loads(json_str)
        
        # 验证结果结构
        if not validate_result_structure(result_dict):
            logger.error("结果结构不符合预期")
            return get_default_result()
            
        return result_dict
    except Exception as e:
        traceback.print_exc()
        logger.error(f"分析文本时出错: {str(e)}")
        return get_default_result()

def validate_result_structure(data: Dict[str, Any]) -> bool:
    """验证结果结构是否符合预期"""
    required_fields = [
        "authorAttitude", 
        "authorViewpoint", 
        "characterEntity", 
        "institutionalEntities", 
        "locationEntity", 
        "eventInfo", 
        "summaryFacts", 
        "summary"
    ]
    
    for field in required_fields:
        if field not in data:
            logger.error(f"缺少必需字段: {field}")
            return False
    
    # 验证authorAttitude是否为预期值
    if data.get("authorAttitude") not in ["积极", "中立", "消极"]:
        logger.error("authorAttitude字段值必须为'积极'、'中立'或'消极'")
        data["authorAttitude"] = "中立"  # 设置默认值
    
    # 验证列表字段
    list_fields = ["characterEntity", "institutionalEntities", "locationEntity", "eventInfo", "summaryFacts"]
    for field in list_fields:
        if not isinstance(data.get(field), list):
            logger.error(f"{field}字段必须为列表类型")
            data[field] = []  # 设置默认值
    
    return True

def get_default_result() -> Dict[str, Any]:
    """返回默认的分析结果"""
    return {
        "authorAttitude": "中立",
        "authorViewpoint": "",
        "characterEntity": [],
        "institutionalEntities": [],
        "locationEntity": [],
        "eventInfo": [],
        "summaryFacts": [],
        "summary": ""
    }

def classify_finance_relevance(title: str, content: str) -> Dict[str, Any]:
    """
    判断文章是否与金融相关
    
    Args:
        title: 文章标题
        content: 文章内容
        
    Returns:
        包含金融相关性判断的字典，包括以下字段：
        - is_finance_related: 是否与金融相关（布尔值）
        - relevance_score: 相关性评分（0-100）
        - article_type: 文章类型
    """
    # 准备内容摘要（限制长度，避免token消耗过多）
    content_summary = content[:2000] + "..." if len(content) > 2000 else content
    
    # 构建提示词模板
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content="""你是一个专业的金融内容分析专家，需要判断文章是否与金融相关。
请根据以下标准进行判断：

1. 金融相关内容包括但不限于：
   - 宏观经济政策和分析
   - 金融市场动态（股票、债券、外汇等）
   - 银行、保险、证券等金融机构信息
   - 投资理财建议和分析
   - 企业财务报告和分析
   - 金融科技和创新
   - 金融监管和政策解读
   - 企业异常/风险信息
   - 涉及到企业舆情的信息、声誉风险信息
   - 企业重大活动
   - 行业产业数据发布、变化
   - 重要政策解读
   - 宏观经济数据发布、变化、
   - 设计信用风险、市场风险、流动性风险、操作风险、法律与合规防线、互联网金融风险、国别风险、地缘政治、跨境流动性风险                                

2. 明确不相关的内容包括：
   - 纯广告宣传内容
   - 招聘信息
   - 活动通知
   - 与金融无关的产品介绍
   - 纯社会新闻

请以JSON格式返回以下字段：
1. is_finance_related: 布尔值，true表示相关，false表示不相关
2. relevance_score: 相关性评分，0-100的整数
3. article_type: 文章类型，如"行业分析"、"政策解读"、"广告"、"活动通知"等

格式示例：{"is_finance_related": true, "relevance_score": 85, "article_type": "行业分析"}"""),
        HumanMessage(content=f"""请判断以下文章是否与金融相关：
标题: {title}
内容: {content_summary}
/no_think""")
    ])
    
    chain = prompt | llm2

    try:
        result = chain.invoke({})
        # 获取内容
        raw_content = getattr(result, 'content', result)
        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)

        # 匹配JSON块
        json_match = re.search(r'({.*?})', text, flags=re.DOTALL)
        if not json_match:
            logger.error("未找到JSON内容")
            return {
                "is_finance_related": False,
                "relevance_score": 0,
                "article_type": "未知",
                "reason": "分析失败"
            }
            
        json_str = json_match.group(1)

        # 尝试解析
        result_dict = json.loads(json_str)
        
        # 确保结果包含所需字段
        if "is_finance_related" not in result_dict:
            result_dict["is_finance_related"] = False
        if "relevance_score" not in result_dict:
            result_dict["relevance_score"] = 0
        if "article_type" not in result_dict:
            result_dict["article_type"] = "未知"
        if "reason" not in result_dict:
            result_dict["reason"] = "无判断依据"
            
        return result_dict
    except Exception as e:
        logger.error(f"分类判断时出错: {str(e)}")
        return {
            "is_finance_related": False,
            "relevance_score": 0,
            "article_type": "未知",
            "reason": f"分析出错: {str(e)}"
        }

def process_data(current_docs):
    """处理数据并写入目标ES"""
    if not current_docs or len(current_docs) < 1:
        print('无数据需要处理')
        return 0
        
    print('开始处理数据')
    
    if TARGET_ES_USER and TARGET_ES_PASSWORD:
        es = Elasticsearch(
            [f"http://{TARGET_ES_HOST}:{TARGET_ES_PORT}"],
            http_auth=(TARGET_ES_USER, TARGET_ES_PASSWORD),
            timeout=3600
        )
    else:
        es = Elasticsearch([f"http://{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
    
    if not es.ping():
        logger.error(f"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
        return 0
    
    logger.info(f"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
    
    try:
        processed_count = 0
        skipped_count = 0
        for source in current_docs:
            try:
                _id = source['_id']
                row = source['_source']
                
                # 获取标题和内容
                title = row.get('title', '')
                content = row.get('content', '')
                
                # 判断文章是否与金融相关
                finance_classification = classify_finance_relevance(title, content)
                
                # 如果不相关或是广告类内容，则跳过
                if not finance_classification.get("is_finance_related") or finance_classification.get("relevance_score", 0) < 30 or finance_classification.get("article_type", "").lower() in ["广告", "活动通知", "招聘信息"]:
                    logger.info(f"跳过不相关内容 - ID: {_id}, 标题: {title[:30]}..., " 
                            f"类型: {finance_classification.get('article_type')}, " )
                    skipped_count += 1
                    continue
                
                # 使用LLM分析文本
                analysis_result = analyze_text(title, content)
                
                # 创建目标文档
                target_doc = {
                    # 直接复制的字段
                    "author": row.get("author", ""),
                    "area": row.get("area", ""),
                    "source": row.get("source", ""),
                    "publishtime": row.get("publishtime", ""),
                    "site_media_nature": row.get("site_media_nature", ""),
                    "site_id": row.get("site_id", ""),
                    "site_level": row.get("site_level", ""),
                    "new_author": row.get("new_author", ""),
                    "reply": row.get("reply", ""),
                    "imgs": row.get("imgs", ""),
                    "site_area_code": row.get("site_area_code", ""),
                    "site_name": row.get("site_name", ""),
                    "content": row.get("content", ""),
                    "new_site_name": row.get("new_site_name", ""),
                    "agg_domain_1": row.get("agg_domain_1", ""),
                    "title": row.get("title", ""),
                    "url": row.get("url", ""),
                    "correlationOrg": row.get("correlationOrg", []),
                    "tag": row.get("tag", []),
                    
                    # LLM分析的字段
                    "authorAttitude": analysis_result.get("authorAttitude", "中立"),
                    "authorViewpoint": analysis_result.get("authorViewpoint", ""),
                    "characterEntity": analysis_result.get("characterEntity", []),
                    "institutionalEntities": analysis_result.get("institutionalEntities", []),
                    "locationEntity": analysis_result.get("locationEntity", []),
                    "eventInfo": analysis_result.get("eventInfo", []),
                    "summaryFacts": analysis_result.get("summaryFacts", []),
                    "summary": analysis_result.get("summary", ""),
                    
                    # 金融相关性分类字段
                    "financeRelevance": finance_classification.get("relevance_score", 0),
                    "articleType": finance_classification.get("article_type", "未知"),
                    "financeRelevanceReason": finance_classification.get("reason", ""),
                    
                    # 创建时间
                    "createTimeES": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                # 获取嵌入向量 - 使用文本摘要生成向量
                target_doc['embedding'] = get_embedding(analysis_result.get("summary", ""), embedding_config)
                
                # 写入目标ES
                try:
                    es.index(
                        index=TARGET_ES_INDEX,
                        id=_id,
                        body=target_doc
                    )
                    print(f'插入：{_id}, {row.get("publishtime", "未知时间")}, 相关度: {finance_classification.get("relevance_score")}%, 类型: {finance_classification.get("article_type")}')
                    processed_count += 1
                except Exception as e:
                    print(f"保存文档 {_id} 时出错: {type(e).__name__}: {str(e)}")
                    logger.error(f"保存文档 {_id} 时出错: {str(e)}")
            except Exception as item_error:
                logger.error(f"处理单条数据时出错: {str(item_error)}")
                continue
        
        logger.info(f"处理文档: {processed_count}, 跳过不相关文档: {skipped_count}")
        return processed_count
    except Exception as e:
        traceback.print_exc()
        logger.error(f"处理数据时出错: {str(e)}")
        return 0
    finally:
        if es:
            try:
                es.close()
                logger.debug("ES客户端已关闭")
            except Exception as close_error:
                logger.warning(f"关闭ES客户端时出错: {str(close_error)}")

# LangGraph工作流定义
class AgentState(TypedDict):
    current_docs: List[Dict]
    start_time: Optional[str]
    end_time: Optional[str]

def fetch_data(state):
    """从源ES获取数据"""
    try:
        print('开始获取数据')
        body = build_body_for_index(state["start_time"], state["end_time"])
        
        all_batches = []
        for batch in get_es_scroll_data_batched(SOURCE_ES_INDEX, body, BATCH_SIZE):
            all_batches.append(batch)
            
        state["current_docs"] = all_batches
        print(f'获取到数据批次数量：{len(all_batches)}')
        return state
            
    except Exception as e:
        logger.error(f"获取数据时出错: {str(e)}")
        state["error"] = f"获取数据出错: {str(e)}"
        return state

def process_batch(state):
    """处理批次数据"""
    processed_count = 0
    if state["current_docs"]:
        for batch in state["current_docs"]:
            batch_count = process_data(batch)
            processed_count += batch_count
    
    print(f"总处理文档数: {processed_count}")
    return state

def get_hour_range(end_time_str: str = None, minutes=30):
    """
    根据给定的结束时间，计算下一个时间段的开始和结束时间
    
    Args:
        end_time_str: 上一次处理的结束时间
        minutes: 每个时间段的分钟数
        
    Returns:
        下一个时间段的开始时间和结束时间
    """
    if not end_time_str or end_time_str.strip() == "":
        # 如果没有指定结束时间，使用当前时间前30分钟作为开始
        start_dt = datetime.now() - timedelta(minutes=minutes)
        end_dt = datetime.now()
    else:
        # 使用上一次的结束时间作为新的开始时间
        start_dt = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
        end_dt = start_dt + timedelta(minutes=minutes)
        
        # 确保结束时间不超过当前时间
        current_dt = datetime.now()
        if end_dt > current_dt:
            end_dt = current_dt
    
    start_time = start_dt.strftime("%Y-%m-%d %H:%M:%S")
    end_time = end_dt.strftime("%Y-%m-%d %H:%M:%S")
    
    return start_time, end_time


# 创建工作流
workflow = StateGraph(AgentState)
workflow.add_node("fetch_data", fetch_data)
workflow.add_node("process_batch", process_batch)
workflow.add_edge("fetch_data", "process_batch")
workflow.add_edge("process_batch", END)
workflow.set_entry_point("fetch_data")
workflow = workflow.compile()

if __name__ == "__main__":
    # 是否持续运行
    continuous_mode = True
    # 无数据时等待时间(秒)
    wait_time = 60
    
    if continuous_mode:
        # 初始时间设置：从30分钟前开始
        start = (datetime.now() - timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
        end = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("启动持续同步模式")
        print(f"初始同步时间段: {start} 到 {end}")
        
        while True:
            print("\n" + "="*50)
            print(f"开始处理时间段: {start} 到 {end}")
            print("="*50)
            
            initial_state = {
                "current_docs": [],
                "start_time": start,
                "end_time": end
            }
            
            # 执行工作流获取数据
            state = fetch_data(initial_state)
            
            if not state["current_docs"] or len(state["current_docs"]) == 0:
                print(f"当前时间段无数据，等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
            else:
                # 处理数据
                print(f"获取到 {len(state['current_docs'])} 批数据，开始处理...")
                processed_count = 0
                for batch in state["current_docs"]:
                    # 直接处理批次数据，不需要再创建新的状态对象
                    batch_processed = process_data(batch)
                    processed_count += batch_processed
                print(f"总处理文档数: {processed_count}")
            
            # 计算下一个时间段
            start, end = get_hour_range(end)
            
            # 检查是否已经同步到当前时间
            if datetime.strptime(start, "%Y-%m-%d %H:%M:%S") >= datetime.now():
                print("已同步至最新数据，休眠60秒后继续...")
                time.sleep(wait_time)
            else:
                print(f"继续同步下一时间段: {start} 到 {end}")
    else:
        # 单次同步模式
        # 默认同步最近30分钟的数据
        start_date = (datetime.now() - timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
        end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"开始单次同步 {start_date} 到 {end_date} 的数据")
        initial_state = {
            "current_docs": [],
            "start_time": start_date,
            "end_time": end_date
        }
        
        # 执行工作流
        workflow.invoke(initial_state)
        
        print("数据同步完成") 