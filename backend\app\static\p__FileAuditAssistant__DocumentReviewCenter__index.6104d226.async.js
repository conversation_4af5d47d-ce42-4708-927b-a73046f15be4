"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6605],{46687:function(e,r,t){t.r(r),t.d(r,{default:function(){return xe}});var n=t(64599),i=t.n(n),s=t(19632),c=t.n(s),a=t(15009),u=t.n(a),o=t(97857),l=t.n(o),p=t(99289),d=t.n(p),f=t(5574),h=t.n(f),x=t(67294),m=t(11941),v=t(55102),Z=t(8232),y=t(2453),j=t(17788),g=t(83062),k=t(66309),w=t(42075),S=t(83622),P=t(86738),b=t(4393),_=t(71230),T=t(15746),N=t(55054),C=t(67839),z=t(11550),I=t(34041),F=t(71471),E=t(97131),q=t(47389),O=t(82061),J=t(29158),U=t(13520),V=t(90389),A=t(51042),L=t(88484),K=t(69753),B=t(35312);function D(){return G.apply(this,arguments)}function G(){return G=d()(u()().mark((function e(){var r,t=arguments;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.length>0&&void 0!==t[0]?t[0]:{},e.abrupt("return",(0,B.request)("/api/consumer-protection-rules",{method:"GET",params:l()(l()({},r),{},{current:r.current||1,pageSize:r.pageSize||10})}));case 2:case"end":return e.stop()}}),e)}))),G.apply(this,arguments)}function M(e){return Q.apply(this,arguments)}function Q(){return(Q=d()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer-protection-rules",{method:"POST",data:{ruleName:r.ruleName,ruleType:r.ruleType,description:r.description,source:r.source,logic:r.logic,result_definition:r.result_definition}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,r){return W.apply(this,arguments)}function W(){return(W=d()(u()().mark((function e(r,t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer-protection-rules/".concat(r),{method:"PUT",data:{ruleName:t.ruleName,ruleType:t.ruleType,description:t.description,source:t.source,logic:t.logic,result_definition:t.result_definition}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function H(e){return X.apply(this,arguments)}function X(){return(X=d()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer-protection-rules/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Y(){return $.apply(this,arguments)}function $(){return $=d()(u()().mark((function e(){var r,t=arguments;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.length>0&&void 0!==t[0]?t[0]:{},e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types",{method:"GET",params:r}));case 2:case"end":return e.stop()}}),e)}))),$.apply(this,arguments)}function ee(e){return re.apply(this,arguments)}function re(){return(re=d()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types",{method:"POST",data:{name:r.name,description:r.description||"",is_active:void 0===r.is_active||r.is_active,rules:[]}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function te(e,r){return ne.apply(this,arguments)}function ne(){return(ne=d()(u()().mark((function e(r,t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types/".concat(r),{method:"PUT",data:{name:t.name,description:t.description||"",is_active:t.is_active,rules:t.rules||[]}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ie(e){return se.apply(this,arguments)}function se(){return(se=d()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ce(e){return ae.apply(this,arguments)}function ae(){return(ae=d()(u()().mark((function e(r){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types/".concat(r,"/rules"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ue(e,r){return oe.apply(this,arguments)}function oe(){return(oe=d()(u()().mark((function e(r,t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types/".concat(r,"/rules"),{method:"PUT",data:{rule_id:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function le(e,r){return pe.apply(this,arguments)}function pe(){return(pe=d()(u()().mark((function e(r,t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,B.request)("/api/consumer_protection/protection-types/".concat(r,"/rules/").concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var de=t(85893),fe=m.Z.TabPane,he=v.Z.TextArea,xe=function(){var e=(0,x.useState)([]),r=h()(e,2),t=r[0],n=r[1],s=(0,x.useState)(!1),a=h()(s,2),o=a[0],p=a[1],f=(0,x.useState)(0),B=h()(f,2),G=B[0],Q=B[1],W=(0,x.useState)({current:1,pageSize:10}),X=h()(W,2),$=X[0],re=X[1],ne=(0,x.useState)("all"),se=h()(ne,2),ae=se[0],oe=(se[1],(0,x.useState)([])),pe=h()(oe,2),xe=pe[0],me=pe[1],ve=(0,x.useState)(!1),Ze=h()(ve,2),ye=Ze[0],je=Ze[1],ge=(0,x.useState)(0),ke=h()(ge,2),we=ke[0],Se=ke[1],Pe=(0,x.useState)({current:1,pageSize:10}),be=h()(Pe,2),_e=be[0],Te=be[1],Ne=(0,x.useState)(!1),Ce=h()(Ne,2),ze=Ce[0],Ie=Ce[1],Fe=(0,x.useState)(null),Ee=h()(Fe,2),qe=Ee[0],Oe=Ee[1],Je=Z.Z.useForm(),Ue=h()(Je,1)[0],Ve=(0,x.useState)(!1),Ae=h()(Ve,2),Le=Ae[0],Ke=Ae[1],Be=(0,x.useState)(""),De=h()(Be,2),Ge=De[0],Me=De[1],Qe=(0,x.useState)(""),Re=h()(Qe,2),We=Re[0],He=Re[1],Xe=(0,x.useState)([]),Ye=h()(Xe,2),$e=Ye[0],er=Ye[1],rr=(0,x.useState)([]),tr=h()(rr,2),nr=tr[0],ir=tr[1],sr=(0,x.useState)(!1),cr=h()(sr,2),ar=cr[0],ur=cr[1],or=(0,x.useState)(""),lr=h()(or,2),pr=lr[0],dr=lr[1],fr=(0,x.useState)(!1),hr=h()(fr,2),xr=hr[0],mr=hr[1],vr=Z.Z.useForm(),Zr=h()(vr,1)[0],yr=(0,x.useState)([]),jr=h()(yr,2),gr=jr[0],kr=jr[1],wr=(0,x.useState)(""),Sr=h()(wr,2),Pr=(Sr[0],Sr[1],(0,x.useState)(null)),br=h()(Pr,2),_r=br[0],Tr=br[1],Nr=(0,x.useState)(!1),Cr=h()(Nr,2),zr=Cr[0],Ir=Cr[1],Fr=(0,x.useState)([]),Er=h()(Fr,2),qr=Er[0],Or=Er[1],Jr=(0,x.useState)([]),Ur=h()(Jr,2),Vr=Ur[0],Ar=Ur[1],Lr=(0,x.useState)(!1),Kr=h()(Lr,2),Br=Kr[0],Dr=Kr[1],Gr=((0,x.useMemo)((function(){return{total:t.length,auto:t.filter((function(e){return"auto"===e.source})).length,manual:t.filter((function(e){return"manual"===e.source})).length}}),[t]),(0,x.useMemo)((function(){return{total:xe.length,active:xe.filter((function(e){return e.is_active})).length,inactive:xe.filter((function(e){return!e.is_active})).length}}),[xe])),Mr=((0,x.useMemo)((function(){return xe.map((function(e){return{value:e.id,label:e.name}}))}),[xe]),function(){var e=d()(u()().mark((function e(){var r,t,i,s;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p(!0),e.prev=1,r=$.current,t=$.pageSize,i=l()({current:r,pageSize:t},"all"!==ae?{source:ae}:{}),e.next=6,D(i);case 6:(s=e.sent).success?(n(s.data),Q(s.total)):y.ZP.error("获取规则列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取规则列表出错:",e.t0),y.ZP.error("获取规则列表出错");case 14:return e.prev=14,p(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}()),Qr=function(){var e=d()(u()().mark((function e(){var r,t,n,i;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return je(!0),e.prev=1,r=_e.current,t=_e.pageSize,n={skip:(r-1)*t,limit:t},e.next=6,Y(n);case 6:(i=e.sent).success?(me(i.data),Se(i.total)):y.ZP.error("获取类型列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("获取类型列表出错:",e.t0),y.ZP.error("获取类型列表出错");case 14:return e.prev=14,je(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(){return e.apply(this,arguments)}}(),Rr=function(){var e=d()(u()().mark((function e(r,t){var n,i,s,c;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ur(!0),e.prev=1,e.next=4,ce(r);case 4:if(n=e.sent,console.log("关联规则响应:",n),!n||!n.success){e.next=18;break}return er(n.data||[]),Me(r),He(t),e.next=12,D({pageSize:1e3});case 12:i=e.sent,console.log("所有规则响应:",i),i&&i.success?(s=n.data?n.data.map((function(e){return e.id})):[],c=i.data.filter((function(e){return!s.includes(e.id)})),console.log("过滤后的可用规则:",c),ir(c),dr("")):(ir([]),y.ZP.error("获取可用规则失败")),Ke(!0),e.next=19;break;case 18:y.ZP.error("获取关联规则失败");case 19:e.next=26;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("获取关联规则出错:",e.t0),y.ZP.error("获取关联规则出错"),ir([]);case 26:return e.prev=26,ur(!1),e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[1,21,26,29]])})));return function(r,t){return e.apply(this,arguments)}}(),Wr=function(){var e=d()(u()().mark((function e(){var r;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Y({limit:1e3});case 3:(r=e.sent).success&&me(r.data),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取所有类型失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();(0,x.useEffect)((function(){Mr()}),[$.current,$.pageSize,ae]),(0,x.useEffect)((function(){Qr()}),[_e.current,_e.pageSize]),(0,x.useEffect)((function(){Wr()}),[]);var Hr=function(){var e=d()(u()().mark((function e(){var r,t;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Ue.validateFields();case 3:if(r=e.sent,console.log("表单提交的值:",r),!qe){e.next=12;break}return e.next=8,te(qe.id,{name:r.name,description:r.description,is_active:r.is_active});case 8:e.sent?(y.ZP.success("更新类型成功"),Ie(!1),Ue.resetFields(),Qr(),Wr()):y.ZP.error("更新类型失败"),e.next=18;break;case 12:return t={name:r.name,description:r.description||"",is_active:void 0===r.is_active||r.is_active},console.log("创建类型数据:",t),e.next=16,ee(t);case 16:e.sent?(y.ZP.success("创建类型成功"),Ie(!1),Ue.resetFields(),Qr(),Wr()):y.ZP.error("创建类型失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(0),console.error("表单提交出错:",e.t0),y.ZP.error("表单验证失败，请检查必填字段");case 24:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),Xr=function(){var e=d()(u()().mark((function e(){var r,t;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(pr&&Ge){e.next=3;break}return y.ZP.warning("请选择要添加的规则"),e.abrupt("return");case 3:return e.prev=3,e.next=6,ue(Ge,pr);case 6:if(!(r=e.sent).success){e.next=15;break}return y.ZP.success("规则添加成功"),e.next=11,ce(Ge);case 11:(t=e.sent).success&&(er(t.data),ir(nr.filter((function(e){return e.id!==pr}))),dr("")),e.next=16;break;case 15:y.ZP.error(r.message||"规则添加失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("添加规则出错:",e.t0),y.ZP.error("添加规则出错");case 22:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),Yr=function(){var e=d()(u()().mark((function e(r){var t,n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,le(Ge,r);case 3:(t=e.sent).success?(y.ZP.success("规则移除成功"),er($e.filter((function(e){return e.id!==r}))),(n=$e.find((function(e){return e.id===r})))&&ir([].concat(c()(nr),[n]))):y.ZP.error(t.message||"规则移除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("移除规则出错:",e.t0),y.ZP.error("移除规则出错");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),$r=function(){var e=d()(u()().mark((function e(r){var n;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(n=t.find((function(e){return e.id===r})))&&(Tr(n),Zr.setFieldsValue({ruleName:n.ruleName,description:n.description,logic:n.logic}),kr(n.result_definition||[]),mr(!0))}catch(e){y.ZP.error("加载规则详情失败")}case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),et=function(){var e=d()(u()().mark((function e(){var r,t;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Zr.validateFields();case 3:if(r=e.sent,t={ruleName:r.ruleName,ruleType:"",description:r.description,source:"manual",logic:r.logic,result_definition:gr},!_r){e.next=12;break}return e.next=8,R(_r.id,t);case 8:e.sent?(y.ZP.success("编辑规则成功"),mr(!1),Tr(null),Zr.resetFields(),kr([]),Mr()):y.ZP.error("编辑规则失败"),e.next=16;break;case 12:return e.next=14,M(t);case 14:e.sent?(y.ZP.success("创建规则成功"),mr(!1),Zr.resetFields(),kr([]),Mr()):y.ZP.error("创建规则失败");case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(0),y.ZP.error("表单验证失败，请检查必填字段");case 21:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(){return e.apply(this,arguments)}}(),rt=function(e){var r=new FileReader;return r.onload=function(r){try{var t;if(!e.name.endsWith(".json"))return y.ZP.error("不支持的文件格式，请上传JSON文件"),!1;var n=null===(t=r.target)||void 0===t?void 0:t.result,i=JSON.parse(n);if(!Array.isArray(i))return y.ZP.error("JSON文件格式不正确，应为数组格式"),!1;var s=i.map((function(e){return{ruleName:e.ruleName||"",ruleType:e.ruleType||"",description:e.description||"",source:"manual",logic:e.logic||"",result_definition:e.result_definition||[]}})).filter((function(e){return e.ruleName&&e.description&&e.logic}));if(0===s.length)return y.ZP.error("没有找到有效的规则数据"),!1;Or(s),Ar(s.map((function(e,r){return r.toString()}))),Ir(!0)}catch(e){console.error("解析文件出错:",e),y.ZP.error("解析文件失败，请检查文件格式")}},e.name.endsWith(".json")?(r.readAsText(e),!1):(y.ZP.error("不支持的文件格式，请上传JSON文件"),!1)},tt=function(){var e=d()(u()().mark((function e(){var r,t,n,s,c,a;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Vr.length){e.next=3;break}return y.ZP.warning("请选择要导入的规则"),e.abrupt("return");case 3:Dr(!0),e.prev=4,r=Vr.map((function(e){return qr[parseInt(e)]})),t=0,n=0,s=i()(r),e.prev=9,s.s();case 11:if((c=s.n()).done){e.next=26;break}return a=c.value,e.prev=13,e.next=16,M(a);case 16:e.sent?t++:n++,e.next=24;break;case 20:e.prev=20,e.t0=e.catch(13),console.error("导入规则失败:",e.t0),n++;case 24:e.next=11;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(9),s.e(e.t1);case 31:return e.prev=31,s.f(),e.finish(31);case 34:t>0?(y.ZP.success("成功导入".concat(t,"条规则").concat(n>0?"，".concat(n,"条失败"):"")),Ir(!1),Mr()):y.ZP.error("导入失败，请检查数据格式"),e.next=41;break;case 37:e.prev=37,e.t2=e.catch(4),console.error("批量导入出错:",e.t2),y.ZP.error("批量导入出错");case 41:return e.prev=41,Dr(!1),e.finish(41);case 44:case"end":return e.stop()}}),e,null,[[4,37,41,44],[9,28,31,34],[13,20]])})));return function(){return e.apply(this,arguments)}}(),nt=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:250,ellipsis:!0,render:function(e,r){return(0,de.jsx)(g.Z,{title:r.description,children:(0,de.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"ruleType",key:"ruleType",width:100},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,de.jsx)(k.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:150,render:function(e,r){return(0,de.jsxs)(w.Z,{size:"small",children:[(0,de.jsx)(S.ZP,{type:"link",icon:(0,de.jsx)(q.Z,{}),onClick:function(){return $r(r.id)},children:"编辑"}),(0,de.jsx)(P.Z,{title:"确定删除此规则?",onConfirm:function(){return e=r.id,void j.Z.confirm({title:"确认删除规则？",content:"此操作不可撤销",onOk:(t=d()(u()().mark((function r(){var t;return u()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,H(e);case 3:(t=r.sent).success?(y.ZP.success("删除成功"),Mr()):y.ZP.error(t.message||"删除失败"),r.next=11;break;case 7:r.prev=7,r.t0=r.catch(0),console.error("删除规则出错:",r.t0),y.ZP.error("删除规则出错");case 11:case"end":return r.stop()}}),r,null,[[0,7]])}))),function(){return t.apply(this,arguments)})});var e,t},okText:"确定",cancelText:"取消",children:(0,de.jsx)(S.ZP,{type:"link",danger:!0,icon:(0,de.jsx)(O.Z,{}),children:"删除"})})]})}}],it=[{title:"名称",dataIndex:"name",key:"name",width:200,render:function(e,r){return(0,de.jsx)(g.Z,{title:r.description,children:(0,de.jsx)("span",{children:e})})}},{title:"规则数量",dataIndex:"rules_count",key:"rules_count",width:100},{title:"状态",dataIndex:"is_active",key:"is_active",width:100,render:function(e){return(0,de.jsx)(k.Z,{color:e?"green":"red",children:e?"启用":"禁用"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:180},{title:"操作",key:"action",width:220,render:function(e,r){return(0,de.jsxs)(w.Z,{size:"small",children:[(0,de.jsx)(S.ZP,{type:"link",icon:(0,de.jsx)(J.Z,{}),onClick:function(){return Rr(r.id,r.name)},children:"关联规则"}),(0,de.jsx)(S.ZP,{type:"link",icon:(0,de.jsx)(q.Z,{}),onClick:function(){return Oe(e=r),Ue.setFieldsValue({name:e.name,description:e.description,is_active:e.is_active}),void Ie(!0);var e},children:"编辑"}),(0,de.jsx)(P.Z,{title:"确定删除此类型?",onConfirm:function(){return e=r.id,void j.Z.confirm({title:"确认删除类型？",content:"此操作不可撤销，且会解除与所有规则的关联",onOk:(t=d()(u()().mark((function r(){var t;return u()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,ie(e);case 3:(t=r.sent).success?(y.ZP.success("删除成功"),Qr()):y.ZP.error(t.message||"删除失败"),r.next=11;break;case 7:r.prev=7,r.t0=r.catch(0),console.error("删除类型出错:",r.t0),y.ZP.error("删除类型出错");case 11:case"end":return r.stop()}}),r,null,[[0,7]])}))),function(){return t.apply(this,arguments)})});var e,t},okText:"确定",cancelText:"取消",children:(0,de.jsx)(S.ZP,{type:"link",danger:!0,icon:(0,de.jsx)(O.Z,{}),children:"删除"})})]})}}],st=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:250,ellipsis:!0},{title:"来源",dataIndex:"source",key:"source",width:100,render:function(e){return(0,de.jsx)(k.Z,{color:"auto"===e?"blue":"green",children:"auto"===e?"自动生成":"手动创建"})}},{title:"操作",key:"action",width:100,render:function(e,r){return(0,de.jsx)(P.Z,{title:"确定移除此规则?",onConfirm:function(){return Yr(r.id)},okText:"确定",cancelText:"取消",children:(0,de.jsx)(S.ZP,{type:"link",danger:!0,children:"移除"})})}}],ct=[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:200,ellipsis:!0,render:function(e,r){return(0,de.jsx)(g.Z,{title:r.description,children:(0,de.jsx)("span",{children:e})})}},{title:"规则类型",dataIndex:"ruleType",key:"ruleType",width:150,ellipsis:!0},{title:"分析逻辑",dataIndex:"logic",key:"logic",width:250,ellipsis:!0}];return(0,de.jsxs)(E._z,{children:[(0,de.jsxs)(m.Z,{defaultActiveKey:"types",children:[(0,de.jsx)(fe,{tab:(0,de.jsxs)("span",{children:[(0,de.jsx)(U.Z,{}),"场景管理"]}),children:(0,de.jsxs)(b.Z,{children:[(0,de.jsxs)(_.Z,{gutter:16,style:{marginBottom:16},children:[(0,de.jsx)(T.Z,{span:6,children:(0,de.jsx)(N.Z,{title:"类型总数",value:Gr.total,prefix:(0,de.jsx)(U.Z,{})})}),(0,de.jsx)(T.Z,{span:6,children:(0,de.jsx)(N.Z,{title:"启用状态",value:Gr.active,prefix:(0,de.jsx)(V.Z,{})})}),(0,de.jsx)(T.Z,{span:6,children:(0,de.jsx)(N.Z,{title:"禁用状态",value:Gr.inactive,prefix:(0,de.jsx)(V.Z,{})})}),(0,de.jsx)(T.Z,{span:6,style:{textAlign:"right"},children:(0,de.jsx)(S.ZP,{type:"primary",icon:(0,de.jsx)(A.Z,{}),onClick:function(){Oe(null),Ue.resetFields(),Ue.setFieldsValue({is_active:!0}),Ie(!0)},children:"新建场景"})})]}),(0,de.jsx)(C.Z,{columns:it,dataSource:xe,rowKey:"id",loading:ye,pagination:{current:_e.current,pageSize:_e.pageSize,total:we,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条类型")},onChange:function(e,r){Te({current:e,pageSize:r})}}})]})},"types"),(0,de.jsx)(fe,{tab:(0,de.jsxs)("span",{children:[(0,de.jsx)(V.Z,{}),"规则管理"]}),children:(0,de.jsxs)(b.Z,{children:[(0,de.jsx)(_.Z,{gutter:16,style:{marginBottom:16},children:(0,de.jsx)(T.Z,{span:12,children:(0,de.jsxs)(w.Z,{children:[(0,de.jsx)(S.ZP,{type:"primary",icon:(0,de.jsx)(A.Z,{}),onClick:function(){Tr(null),Zr.resetFields(),kr([]),mr(!0)},children:"新建规则"}),(0,de.jsx)(z.Z,{beforeUpload:rt,showUploadList:!1,accept:".json",children:(0,de.jsx)(S.ZP,{icon:(0,de.jsx)(L.Z,{}),children:"批量导入"})}),(0,de.jsx)(S.ZP,{icon:(0,de.jsx)(K.Z,{}),onClick:function(){return window.open("/static/样例.json")},children:"导入文件样例"})]})})}),(0,de.jsx)(C.Z,{columns:nt,dataSource:t,rowKey:"id",loading:o,scroll:{x:800},pagination:{current:$.current,pageSize:$.pageSize,total:G,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条规则")},onChange:function(e,r){re({current:e,pageSize:r})}}})]})},"rules")]}),(0,de.jsx)(j.Z,{title:_r?"编辑规则":"新建规则",open:xr,onOk:et,onCancel:function(){mr(!1),Tr(null),Zr.resetFields(),kr([])},width:800,children:(0,de.jsxs)(Z.Z,{form:Zr,layout:"vertical",initialValues:{source:"manual"},children:[(0,de.jsx)(Z.Z.Item,{name:"ruleName",label:"规则名称",rules:[{required:!0,message:"请输入规则名称"}],children:(0,de.jsx)(v.Z,{placeholder:"请输入规则名称"})}),(0,de.jsx)(Z.Z.Item,{name:"description",label:"规则描述",rules:[{required:!0,message:"请输入规则描述"}],children:(0,de.jsx)(he,{rows:4,placeholder:"请输入规则描述"})})]})}),(0,de.jsx)(j.Z,{title:qe?"编辑场景":"新建场景",open:ze,onOk:Hr,onCancel:function(){Ie(!1),Ue.resetFields()},width:600,children:(0,de.jsxs)(Z.Z,{form:Ue,layout:"vertical",initialValues:{is_active:!0},children:[(0,de.jsx)(Z.Z.Item,{name:"name",label:"场景名称",rules:[{required:!0,message:"请输入场景名称"}],children:(0,de.jsx)(v.Z,{placeholder:"请输入场景名称"})}),(0,de.jsx)(Z.Z.Item,{name:"description",label:"场景描述",children:(0,de.jsx)(he,{rows:4,placeholder:"请输入场景描述"})}),(0,de.jsx)(Z.Z.Item,{name:"is_active",label:"启用状态",initialValue:!0,children:(0,de.jsx)(I.default,{defaultValue:!0,options:[{value:!0,label:"启用"},{value:!1,label:"禁用"}]})})]})}),(0,de.jsxs)(j.Z,{title:"".concat(We||"未知类型"," - 关联规则管理"),open:Le,onCancel:function(){return Ke(!1)},footer:null,width:900,children:[(0,de.jsx)("div",{style:{marginBottom:16},children:(0,de.jsxs)(_.Z,{gutter:16,children:[(0,de.jsx)(T.Z,{span:16,children:(0,de.jsx)(I.default,{style:{width:"100%"},placeholder:"选择要添加的规则",value:pr,onChange:function(e){return dr(e)},options:nr.map((function(e){return console.log("规则详情:",e),{value:e.id,label:e.ruleType+"-"+(e.ruleName||"未命名规则")}})),notFoundContent:0===nr.length?"没有可添加的规则":void 0})}),(0,de.jsx)(T.Z,{span:8,children:(0,de.jsx)(S.ZP,{type:"primary",onClick:Xr,disabled:!pr,children:"添加规则"})})]})}),(0,de.jsx)(C.Z,{columns:st,dataSource:$e,rowKey:"id",loading:ar,scroll:{x:600},pagination:!1})]}),(0,de.jsxs)(j.Z,{title:"批量导入规则",open:zr,onOk:tt,onCancel:function(){return Ir(!1)},width:900,confirmLoading:Br,okText:"导入选中规则",cancelText:"取消",children:[(0,de.jsx)("div",{style:{marginBottom:16},children:(0,de.jsxs)("span",{children:["共解析到 ",qr.length," 条规则，已选择 ",Vr.length," 条"]})}),(0,de.jsx)(C.Z,{rowSelection:{selectedRowKeys:Vr,onChange:function(e){Ar(e.map((function(e){return e.toString()})))}},columns:ct,dataSource:qr.map((function(e,r){return l()(l()({},e),{},{key:r.toString()})})),scroll:{y:400},pagination:!1}),(0,de.jsxs)("div",{style:{marginTop:16},children:[(0,de.jsx)(z.Z,{beforeUpload:rt,showUploadList:!1,accept:".json",children:(0,de.jsx)(S.ZP,{icon:(0,de.jsx)(L.Z,{}),children:"重新上传"})}),(0,de.jsx)("div",{style:{marginTop:8},children:(0,de.jsx)(F.Z.Text,{type:"secondary",children:"支持的文件格式：JSON(.json)"})})]})]})]})}}}]);