"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6297],{33367:function(e,t,r){r.r(t);var n=r(19632),s=r.n(n),a=r(13769),i=r.n(a),c=r(9783),o=r.n(c),l=r(64599),u=r.n(l),p=r(97857),d=r.n(p),f=r(15009),h=r.n(f),x=r(99289),y=r.n(x),m=r(5574),v=r.n(m),g=r(67294),j=r(97131),k=r(12453),Z=r(71471),b=r(55102),w=r(17788),S=r(8232),_=r(2453),P=r(42075),C=r(66309),T=r(83622),N=r(67839),z=r(11550),I=r(34041),E=r(72269),A=r(96074),F=r(47389),O=r(85175),B=r(87784),J=r(8751),q=r(82061),L=r(51042),R=r(88484),V=r(77880),W=r(85893),D=["current","pageSize","is_system"],H=Z.Z.Title,U=Z.Z.Text,G=Z.Z.Paragraph,Y=b.Z.TextArea,K=w.Z.confirm;t.default=function(){var e=(0,g.useRef)(),t=S.Z.useForm(),r=v()(t,1)[0],n=(0,g.useState)(!1),a=v()(n,2),c=a[0],l=a[1],p=(0,g.useState)(null),f=v()(p,2),x=f[0],m=f[1],Z=(0,g.useState)(!1),$=v()(Z,2),Q=$[0],M=$[1],X=(0,g.useState)(null),ee=v()(X,2),te=ee[0],re=ee[1],ne=(0,g.useState)([]),se=v()(ne,2),ae=se[0],ie=se[1],ce=(0,g.useState)(!1),oe=v()(ce,2),le=oe[0],ue=oe[1],pe=(0,g.useState)(!1),de=v()(pe,2),fe=de[0],he=de[1],xe=(0,g.useState)([]),ye=v()(xe,2),me=ye[0],ve=ye[1],ge=(0,g.useState)([]),je=v()(ge,2),ke=je[0],Ze=je[1],be=(0,g.useState)(!1),we=v()(be,2),Se=we[0],_e=we[1],Pe=[{label:"全部",desc:"所有模型",key:"all"},{label:"语言模型",desc:"文本处理",key:"language_model"},{label:"多模态模型",desc:"多种数据类型处理",key:"multimodal_model"}];(0,g.useEffect)((function(){var e=function(){var e=y()(h()().mark((function e(){var t;return h()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,V.qB)();case 3:(t=e.sent).success&&t.data&&ie(t.data),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),_.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Ce=function(e){m(e||null),e?r.setFieldsValue(d()(d()({},e),{},{title:e.title,content:e.content,category:e.category,positions:e.positions,models:e.models,language:e.language||"zh-CN",is_system:e.is_system,is_active:e.is_active})):(r.resetFields(),r.setFieldsValue({models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0})),l(!0)},Te=function(){var e=y()(h()().mark((function e(t){var r;return h()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,V.WJ)(t.id);case 3:(r=e.sent).success&&r.data&&(re(r.data),M(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),_.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Ne=function(){var t=y()(h()().mark((function t(){var n,s,a,i;return h()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,r.validateFields();case 3:if(n=t.sent,ue(!0),s=d()({},n),!x){t.next=12;break}return t.next=9,(0,V.Fu)(x.id,s);case 9:a=t.sent,t.next=15;break;case 12:return t.next=14,(0,V.Fh)(s);case 14:a=t.sent;case 15:a.success?(_.ZP.success("".concat(x?"更新":"创建","提示词成功")),l(!1),r.resetFields(),null===(i=e.current)||void 0===i||i.reload()):_.ZP.error(a.message||"".concat(x?"更新":"创建","提示词失败")),t.next=22;break;case 18:t.prev=18,t.t0=t.catch(0),console.error("表单验证或提交失败:",t.t0),_.ZP.error("".concat(x?"更新":"创建","提示词失败"));case 22:return t.prev=22,ue(!1),t.finish(22);case 25:case"end":return t.stop()}}),t,null,[[0,18,22,25]])})));return function(){return t.apply(this,arguments)}}(),ze=function(){var t=y()(h()().mark((function t(r){var n,s;return h()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,ue(!0),t.next=4,(0,V.Yb)(r);case 4:(n=t.sent).success?(_.ZP.success("复制提示词成功"),null===(s=e.current)||void 0===s||s.reload()):_.ZP.error(n.message||"复制提示词失败"),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("复制失败:",t.t0),_.ZP.error("复制提示词失败");case 12:return t.prev=12,ue(!1),t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])})));return function(e){return t.apply(this,arguments)}}(),Ie=function(){var t=y()(h()().mark((function t(r,n){var s,a;return h()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,ue(!0),t.next=4,(0,V.Fu)(r,{is_active:!n});case 4:(s=t.sent).success?(_.ZP.success("".concat(n?"禁用":"启用","提示词成功")),null===(a=e.current)||void 0===a||a.reload()):_.ZP.error(s.message||"".concat(n?"禁用":"启用","提示词失败")),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("状态更新失败:",t.t0),_.ZP.error("".concat(n?"禁用":"启用","提示词失败"));case 12:return t.prev=12,ue(!1),t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])})));return function(e,r){return t.apply(this,arguments)}}(),Ee=function(){var t=y()(h()().mark((function t(){var r,n,s,a,i,c,o,l;return h()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==ke.length){t.next=3;break}return _.ZP.warning("请至少选择一条数据进行导入"),t.abrupt("return");case 3:_e(!0),r=me.filter((function(e){return ke.includes(e.key)})),n=0,s=0,a=u()(r),t.prev=8,a.s();case 10:if((i=a.n()).done){t.next=28;break}if(c=i.value,t.prev=12,(o={title:c.title,content:c.content,category:c.category||[],positions:c.positions||[],is_system:!0,is_active:!0,models:c.models||["gpt4"],language:c.language||"zh-CN"}).title&&o.content){t.next=17;break}return s++,t.abrupt("continue",26);case 17:return t.next=19,(0,V.Fh)(o);case 19:n++,t.next=26;break;case 22:t.prev=22,t.t0=t.catch(12),s++,console.error("导入失败:",c,t.t0);case 26:t.next=10;break;case 28:t.next=33;break;case 30:t.prev=30,t.t1=t.catch(8),a.e(t.t1);case 33:return t.prev=33,a.f(),t.finish(33);case 36:_e(!1),_.ZP.success("导入完成！成功 ".concat(n," 条，失败 ").concat(s," 条。")),n>0&&(null===(l=e.current)||void 0===l||l.reload()),he(!1),ve([]),Ze([]);case 42:case"end":return t.stop()}}),t,null,[[8,30,33,36],[12,22]])})));return function(){return t.apply(this,arguments)}}(),Ae=[{title:"提示词名称",dataIndex:"title",valueType:"text",render:function(e,t){return(0,W.jsx)("a",{onClick:function(){return Te(t)},children:t.title})}},{title:"分类",dataIndex:"category",valueType:"text",render:function(e,t){return(0,W.jsx)(P.Z,{children:Array.isArray(t.category)?t.category.map((function(e){return(0,W.jsx)(C.Z,{color:"blue",children:e},e)})):t.category&&(0,W.jsx)(C.Z,{color:"blue",children:t.category})})},valueEnum:ae.reduce((function(e,t){return d()(d()({},e),{},o()({},t,{text:t}))}),{})},{title:"类型",dataIndex:"is_system",valueType:"select",width:100,render:function(e,t){return(0,W.jsx)(C.Z,{color:t.is_system?"success":"default",children:t.is_system?"系统提示词":"个人提示词"})},valueEnum:{true:{text:"系统提示词",status:"Success"},false:{text:"个人提示词",status:"Default"}}},{title:"创建者",dataIndex:"user",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,t){return(0,W.jsx)(C.Z,{color:t.is_active?"success":"error",children:t.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",width:240,render:function(t,r){return[(0,W.jsx)(T.ZP,{type:"link",icon:(0,W.jsx)(F.Z,{}),onClick:function(){return Ce(r)},children:"编辑"},"edit"),(0,W.jsx)(T.ZP,{type:"link",icon:(0,W.jsx)(O.Z,{}),onClick:function(){return ze(r.id)},children:"复制"},"copy"),(0,W.jsx)(T.ZP,{type:"link",icon:r.is_active?(0,W.jsx)(B.Z,{}):(0,W.jsx)(J.Z,{}),danger:r.is_active,onClick:function(){return Ie(r.id,r.is_active)},children:r.is_active?"禁用":"启用"},"status"),(0,W.jsx)(T.ZP,{type:"link",danger:!0,icon:(0,W.jsx)(q.Z,{}),onClick:function(){return t=r.id,void K({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=y()(h()().mark((function r(){var n,s;return h()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,ue(!0),r.next=4,(0,V.$j)(t);case 4:(n=r.sent).success?(_.ZP.success("删除提示词成功"),null===(s=e.current)||void 0===s||s.reload()):_.ZP.error(n.message||"删除提示词失败"),r.next=12;break;case 8:r.prev=8,r.t0=r.catch(0),console.error("删除失败:",r.t0),_.ZP.error("删除提示词失败");case 12:return r.prev=12,ue(!1),r.finish(12);case 15:case"end":return r.stop()}}),r,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var t,n},children:"删除"},"delete")]}}];return(0,W.jsxs)(j._z,{children:[(0,W.jsx)(k.Z,{headerTitle:"提示词管理",actionRef:e,rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,W.jsx)(T.ZP,{type:"primary",onClick:function(){return Ce()},icon:(0,W.jsx)(L.Z,{}),children:"新建提示词"},"create"),(0,W.jsx)(T.ZP,{onClick:function(){return he(!0)},icon:(0,W.jsx)(R.Z,{}),children:"批量导入系统提示词"},"import")]},request:y()(h()().mark((function e(){var t,r,n,a,c,o,l,u,p=arguments;return h()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=p.length>0&&void 0!==p[0]?p[0]:{},r=t.current,n=t.pageSize,a=t.is_system,c=i()(t,D),e.prev=2,void 0!==a){e.next=13;break}return e.next=6,(0,V.V7)(d()({current:r,pageSize:n},c));case 6:return l=e.sent,e.next=9,(0,V.He)(d()({current:r,pageSize:n},c));case 9:u=e.sent,o={data:[].concat(s()(l.data||[]),s()(u.data||[])),success:l.success&&u.success,total:(l.total||0)+(u.total||0)},e.next=22;break;case 13:if("true"!==a){e.next=19;break}return e.next=16,(0,V.V7)(d()({current:r,pageSize:n},c));case 16:o=e.sent,e.next=22;break;case 19:return e.next=21,(0,V.He)(d()({current:r,pageSize:n},c));case 21:o=e.sent;case 22:return e.abrupt("return",{data:o.data||[],success:o.success,total:o.total||0});case 25:return e.prev=25,e.t0=e.catch(2),console.error("获取提示词失败:",e.t0),_.ZP.error("获取提示词列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 30:case"end":return e.stop()}}),e,null,[[2,25]])}))),columns:Ae,rowSelection:{selections:[N.Z.SELECTION_ALL,N.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,W.jsxs)(w.Z,{title:"批量导入系统提示词",open:fe,onCancel:function(){he(!1),ve([]),Ze([])},width:1e3,destroyOnClose:!0,footer:[(0,W.jsx)(T.ZP,{onClick:function(){return he(!1)},children:"取消"},"back"),(0,W.jsxs)(T.ZP,{type:"primary",loading:Se,onClick:Ee,children:["保存选中的 ",ke.length," 条"]},"submit")],children:[(0,W.jsxs)(z.Z.Dragger,{name:"file",multiple:!1,beforeUpload:function(e){var t=new FileReader;return t.onload=function(t){try{var r;if(!e.name.endsWith(".json"))return void _.ZP.error("请上传JSON格式的文件");var n=null===(r=t.target)||void 0===r?void 0:r.result,s=JSON.parse(n);if(!Array.isArray(s))return void _.ZP.error("JSON文件内容必须是数组格式");var a=s.map((function(e,t){var r=e.positions;if("string"==typeof r)if(r.startsWith("[")&&r.endsWith("]"))try{r=JSON.parse(r)}catch(e){r=r.split(",").map((function(e){return e.trim()}))}else r=r.split(",").map((function(e){return e.trim()}));return Array.isArray(r)||(r=["analyst"]),d()(d()({},e),{},{positions:r,key:t})}));ve(a),Ze(a.map((function(e){return e.key})))}catch(e){_.ZP.error("文件解析失败，请检查是否为合法的JSON格式")}},t.readAsText(e,"UTF-8"),!1},onRemove:function(){return ve([])},accept:".json",style:{marginBottom:20},children:[(0,W.jsx)("p",{className:"ant-upload-drag-icon",children:(0,W.jsx)(R.Z,{})}),(0,W.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽 JSON 文件到此区域进行上传"}),(0,W.jsx)("p",{className:"ant-upload-hint",children:"仅支持JSON格式。文件内容应为数组，数组项包含 'title', 'content', 'category', 'positions' 等字段。"})]}),me.length>0&&(0,W.jsx)(N.Z,{rowSelection:{selectedRowKeys:ke,onChange:Ze},dataSource:me,columns:[{title:"提示词名称",dataIndex:"title",key:"title",width:"20%"},{title:"提示词内容",dataIndex:"content",key:"content",width:"40%",ellipsis:!0},{title:"分类",dataIndex:"category",key:"category",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}},{title:"适用岗位",dataIndex:"positions",key:"positions",width:"20%",render:function(e){return Array.isArray(e)?e.join(", "):e}}],pagination:{pageSize:5}})]}),(0,W.jsx)(w.Z,{title:x?"编辑提示词":"新建提示词",open:c,onCancel:function(){return l(!1)},destroyOnClose:!0,footer:[(0,W.jsx)(T.ZP,{onClick:function(){return l(!1)},children:"取消"},"cancel"),(0,W.jsx)(T.ZP,{type:"primary",onClick:Ne,loading:le,children:"确定"},"submit")],width:600,children:(0,W.jsxs)(S.Z,{form:r,layout:"vertical",initialValues:{models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0},children:[(0,W.jsx)(S.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,W.jsx)(b.Z,{placeholder:"请输入提示词名称"})}),(0,W.jsx)(S.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,W.jsx)(Y,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,W.jsx)(S.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,W.jsx)(I.default,{mode:"tags",options:ae.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,W.jsx)(S.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,W.jsx)(I.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,W.jsx)(S.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,W.jsx)(I.default,{mode:"multiple",options:Pe.filter((function(e){return"all"!==e.key})).map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,W.jsx)(S.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,W.jsx)(I.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})}),(0,W.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,W.jsx)(S.Z.Item,{name:"is_system",label:"是否为系统提示词",valuePropName:"checked",children:(0,W.jsx)(E.Z,{checkedChildren:"是",unCheckedChildren:"否"})}),(0,W.jsx)(S.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,W.jsx)(E.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})]})]})}),te&&(0,W.jsxs)(w.Z,{title:(0,W.jsx)(H,{level:5,style:{margin:0},children:te.title}),open:Q,onCancel:function(){M(!1),re(null)},width:700,footer:[(0,W.jsx)(T.ZP,{icon:(0,W.jsx)(O.Z,{}),onClick:function(){navigator.clipboard.writeText(te.content),_.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,W.jsx)(T.ZP,{type:"primary",onClick:function(){M(!1),re(null)},children:"关闭"},"close")],children:[(0,W.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,W.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,W.jsx)("div",{children:Array.isArray(te.category)?te.category.map((function(e){return(0,W.jsx)(C.Z,{color:"blue",children:e},e)})):te.category&&(0,W.jsx)(C.Z,{color:"blue",children:te.category})})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,W.jsx)("div",{children:te.models&&te.models.map((function(e){var t;return(0,W.jsx)(C.Z,{color:"green",children:(null===(t=Pe.find((function(t){return t.key===e})))||void 0===t?void 0:t.label)||e},e)}))})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,W.jsx)("div",{children:te.positions&&te.positions.map((function(e){return(0,W.jsx)(C.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,W.jsx)("div",{children:(0,W.jsx)(C.Z,{color:"purple",children:"zh-CN"===te.language?"中文":"en-US"===te.language?"英文":te.language})})]})]}),(0,W.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"类型"}),(0,W.jsx)(C.Z,{color:te.is_system?"success":"default",children:te.is_system?"系统提示词":"个人提示词"})]}),(0,W.jsxs)("div",{children:[(0,W.jsx)(U,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,W.jsx)(C.Z,{color:te.is_active?"success":"error",children:te.is_active?"已启用":"已禁用"})]})]})]}),(0,W.jsx)(A.Z,{orientation:"left",children:"提示词内容"}),(0,W.jsx)(G,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:te.content}),(0,W.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,W.jsxs)("div",{children:[(0,W.jsxs)(U,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(te.created_at).toLocaleString()]}),te.updated_at&&te.updated_at!==te.created_at&&(0,W.jsxs)(U,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(te.updated_at).toLocaleString()]})]}),(0,W.jsxs)(U,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",te.user]})]})]})]})}},77880:function(e,t,r){r.d(t,{$j:function(){return v},Fh:function(){return h},Fu:function(){return y},He:function(){return o},V7:function(){return u},WJ:function(){return d},Yb:function(){return j},qB:function(){return Z}});var n=r(15009),s=r.n(n),a=r(99289),i=r.n(a),c=r(78158);function o(e){return l.apply(this,arguments)}function l(){return(l=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/user-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return p.apply(this,arguments)}function p(){return(p=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-prompts",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return f.apply(this,arguments)}function f(){return(f=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return x.apply(this,arguments)}function x(){return(x=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,t){return m.apply(this,arguments)}function m(){return(m=i()(s()().mark((function e(t,r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return g.apply(this,arguments)}function g(){return(g=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return k.apply(this,arguments)}function k(){return(k=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(t,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(){return b.apply(this,arguments)}function b(){return(b=i()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompt_categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);