from mongoengine import Document, <PERSON>Field, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class KnowledgeBase(Document):
    meta = {
        'collection': 'knowledge_bases'
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    last_updated = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    file_count = IntField(required=True, default=0)  # 存储关联的文件ID列表
    chunk_count = IntField(required=True, default=0)  # 存储关联的文本块数量
    index_count = IntField(required=True, default=0)  # 存储关联的索引数量
    is_active = BooleanField(required=True, default=True)
    embedding_model_id = IntField()
    embedding_model = StringField()
    # reRank_model_id = IntField()
    # reRank_model = StringField()
    administrators_id = ListField(required=True, default=[])
    users_id = ListField(required=True, default=[])
    basic_index = BooleanField(required=True, default=True)# 基础索引-向量检索
    graph_index = BooleanField(required=True, default=False)# 图索引
    semantic_index = BooleanField(required=True, default=False) # 语义索引

    # app_info = StringField()

# Pydantic 模型
class KnowledgeBaseBase(BaseModel):
    name: str
    description: Optional[str] = None
    user_id: int
    user_name: Optional[str] = None
    file_count: int = 0
    chunk_count: int = 0
    is_active: bool = True
    tags: Optional[List[str]] = None
    # app_info: Optional[str] = None
    is_conversation_kb: bool = False

class KnowledgeBaseCreate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    embedding_model_id: Optional[str] = None
    embedding_model: Optional[str] = None
    reRank_model_id: Optional[str] = None
    reRank_model: Optional[str] = None
    basic_index: bool = True
    graph_index: bool = False
    semantic_index: bool = False

class ConversationKnowledgeBaseCreate(BaseModel):
    conversation_id: str
    name: Optional[str] = None

class ConversationKnowledgeBaseResponse(BaseModel):
    id: str
    name: Optional[str] = None
    description: Optional[str] = None
    conversation_id: Optional[str] = None
    is_conversation_kb: Optional[bool] = None
    

class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    embedding_model: Optional[str] = None
    basic_index: Optional[bool] = None
    graph_index: Optional[bool] = None
    semantic_index: Optional[bool] = None
    embedding_model_id: Optional[str] = None
    reRank_model_id: Optional[str] = None
    reRank_model: Optional[str] = None
    tags: Optional[List[str]] = None

    class Config:
        # 允许从ORM对象创建模型
        from_attributes = True
        # 允许额外字段
        extra = "allow"
        # 支持类型强制转换
        json_encoders = {
            int: lambda v: int(v) if v is not None else None,
            bool: lambda v: bool(v) if v is not None else None
        }

class KnowledgeBaseResponse(KnowledgeBaseBase):
    id: str
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True

class KnowledgeFileResponse(BaseModel):
    id: str
    name: str
    url: str

class UploadFolderRequest(BaseModel):
    folder_name: str
    knowledgeId: str
    parentId: str
    action: str
    folder_id: str