from abc import ABC, abstractmethod
from typing import List, Dict, Any
from logging_config import setup_logging, get_logger
from enums import FileStatus,FileStorageType
from markitdown import MarkItDown
from global_interface import chunk,chunk_index
from enums import chunk_type,graphStatus
import traceback
from bson import ObjectId
from embedding_utils import get_embedding
# from app.utils.enums import FileStatus,FileStorageType
import hashlib
from datetime import datetime
from app.engines.retrieval.base_retriever import es_data_ingester, update_by_query, es_data_deleter
setup_logging()
logger = get_logger(__name__)

def md5(data):
    return hashlib.md5(data.encode(encoding='UTF-8')).hexdigest()

def build_chunk_index(file_data: Dict[str, Any], kb_info: Dict[str, Any], all_chunk_index_list: List[chunk_index],db,ES_HOST,ES_INDEX)->int:
    
    logger.info(f"准备构建的索引数量: {len(all_chunk_index_list)}")
    index_count = 0
    embedding_id = kb_info.get("embedding_model_id",None)
    logger.info(f"embedding_id: {embedding_id}")
    # print(f"======>embedding_id: {embedding_id}")
    # print("======>embedding_id类型:", type(embedding_id))
    if not embedding_id:
        raise Exception("embedding_model_id is not set")
    embedding_config = db['embeddings'].find_one({"id": int(embedding_id)})
    if not embedding_config:
        raise Exception("embedding_model is not exist")
    
    for one_index in all_chunk_index_list:
        try:
            # 根据最新参数结构创建chunk对象
            logger.info(f"one_index: {one_index}")
            new_chunk = {
                "tokens_count":one_index['tokens_count'],
                "file_name": file_data["name"],
                "file_id":ObjectId(file_data["_id"]),
                "knowledge_base_id":ObjectId(kb_info["_id"]),
                "chunk_index":one_index['chunk_index'],
                "answer":one_index['answer'],  # 直接使用传入的answer内容
                "question":one_index['question'],
                "metadata":{
                    "file_name": file_data["name"],
                    # "knowledge_base_name": kb_info["name"],
                    # "app_info": kb_info.get("app_info", "")
                },
                "chunk_type":one_index['chunk_type'],
                "created_at": datetime.now(),
                "last_updated": datetime.now(),
                "is_expired": False,
                "status": 1,
                "graph_build_status": graphStatus.PENDING
            }

            # 保存顺序保持不变
            # 1. 先存chunk_index到MongoDB
            chunk_id = db.chunks.insert_one(new_chunk).inserted_id
            logger.info(f"插入==》chunk_id: {chunk_id}")
            for chunk_index in one_index['chunk_index_list']:
                embedding = get_embedding(chunk_index.get('index_content'), embedding_config)
                if embedding:
                # 如果embedding的长度不足1536，用0填充
                    if len(embedding) < 1536:
                            embedding.extend([0] * (1536 - len(embedding)))
                    logger.info(f"embedding 长度: {len(embedding)}")
                            
                    chunk_index_obj = {
                            "file_id":ObjectId(file_data["_id"]),
                            "file_name": file_data["name"],
                            "chunk_id":chunk_id,
                            "knowledge_base_id":ObjectId(kb_info["_id"]),
                            "index_content":chunk_index.get('index_content'),
                            "chunk_type":chunk_type.BASE,
                            "chunk_index":one_index.get('chunk_index'),
                            "embedding":embedding,
                            "is_expired": False,
                            "created_at": datetime.now()
                    }
                    index_id = db.chunks_index.insert_one(chunk_index_obj).inserted_id
                    try:
                        es_update = {
                            "id": str(index_id),
                            "chunk_id":str(chunk_id),
                            "file_id": str(file_data["_id"]),
                            "file_name": file_data["name"],
                            "knowledge_base_id": str(kb_info["_id"]),
                            "chunk_index": chunk_index_obj.get('chunk_index'),
                            "index_content": chunk_index_obj.get('index_content'),
                            "embedding": embedding,
                            "chunk_type": 'base',
                            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        # es_data_ingester(chunk_id,es_update
                        # print(es_update)
                        print(f"es_update:{str(chunk_id)}, {str(index_id)}==>{ES_INDEX}")
                        print(es_data_ingester(str(index_id), es_update, ES_HOST, ES_INDEX))
                        # es.index(index=ES_INDEX, body=es_update, id=es_update["id"])
                        index_count += 1        
                    except Exception as e:
                            traceback.print_exc()
                            logger.error(f"es 插入失败 : {str(e)}")
                            traceback.print_exc()
                            raise
                else:
                     raise Exception(f"Embedding异常")
            logger.info(f"索引处理成功: {file_data.get('_id')} 索引数量: {index_count}")
        except Exception as e:
            logger.error(f"处理块索引失败 : {str(e)}")
            traceback.print_exc()
        finally:
            logger.info(f"当前块处理完成，累计索引数量: {index_count}")

    logger.info(f"全部索引处理完成: {file_data.get('_id')} 总索引数量: {index_count}")
    return index_count

