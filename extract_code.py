#!/usr/bin/env python3
"""
代码提取工具 - 从项目中提取代码并去掉注释
"""

import os
import re
import ast
import tokenize
from io import StringIO
from pathlib import Path

class CodeExtractor:
    def __init__(self):
        self.code_extensions = {
            '.py': self.remove_python_comments,
            '.js': self.remove_js_comments,
            '.ts': self.remove_js_comments,
            '.jsx': self.remove_js_comments,
            '.tsx': self.remove_js_comments,
            '.java': self.remove_java_comments,
            '.cpp': self.remove_cpp_comments,
            '.c': self.remove_cpp_comments,
            '.h': self.remove_cpp_comments,
            '.hpp': self.remove_cpp_comments,
            '.go': self.remove_go_comments,
            '.rs': self.remove_rust_comments,
            '.php': self.remove_php_comments,
            '.rb': self.remove_ruby_comments,
            '.sql': self.remove_sql_comments,
            '.sh': self.remove_shell_comments,
            '.yaml': self.remove_yaml_comments,
            '.yml': self.remove_yaml_comments,
            '.json': None,  # JSON文件不需要去注释
        }
        
        self.exclude_dirs = {
            '__pycache__', '.git', '.svn', 'node_modules', 'venv', 'env',
            '.venv', 'dist', 'build', '.pytest_cache', '.mypy_cache',
            'static', 'uploads', 'temp_downloads', 'data_images'
        }
        
        self.exclude_files = {
            '.pyc', '.pyo', '.pyd', '.so', '.dll', '.exe', '.bin',
            '.log', '.tmp', '.cache', '.DS_Store', 'Thumbs.db'
        }

    def remove_python_comments(self, code):
        """去掉Python代码中的注释"""
        try:
            # 使用tokenize模块来正确处理Python注释
            result = []
            tokens = tokenize.generate_tokens(StringIO(code).readline)
            
            for token in tokens:
                if token.type not in (tokenize.COMMENT, tokenize.NL):
                    if token.type == tokenize.NEWLINE:
                        result.append('\n')
                    elif token.type != tokenize.ENCODING:
                        result.append(token.string)
            
            return ''.join(result)
        except:
            # 如果tokenize失败，使用简单的正则表达式
            lines = code.split('\n')
            result = []
            in_multiline_string = False
            string_delimiter = None
            
            for line in lines:
                if not line.strip():
                    continue
                    
                # 简单处理单行注释
                if line.strip().startswith('#'):
                    continue
                    
                # 去掉行末注释（简单处理）
                if '#' in line and not in_multiline_string:
                    # 检查是否在字符串中
                    quote_count = line.count('"') + line.count("'")
                    if quote_count % 2 == 0:  # 偶数个引号，可能不在字符串中
                        comment_pos = line.find('#')
                        if comment_pos > 0:
                            line = line[:comment_pos].rstrip()
                
                if line.strip():
                    result.append(line)
            
            return '\n'.join(result)

    def remove_js_comments(self, code):
        """去掉JavaScript/TypeScript代码中的注释"""
        # 去掉单行注释
        code = re.sub(r'//.*$', '', code, flags=re.MULTILINE)
        # 去掉多行注释
        code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)
        # 去掉空行
        lines = [line for line in code.split('\n') if line.strip()]
        return '\n'.join(lines)

    def remove_java_comments(self, code):
        """去掉Java代码中的注释"""
        return self.remove_js_comments(code)  # Java注释语法与JS相同

    def remove_cpp_comments(self, code):
        """去掉C/C++代码中的注释"""
        return self.remove_js_comments(code)  # C++注释语法与JS相同

    def remove_go_comments(self, code):
        """去掉Go代码中的注释"""
        return self.remove_js_comments(code)  # Go注释语法与JS相同

    def remove_rust_comments(self, code):
        """去掉Rust代码中的注释"""
        return self.remove_js_comments(code)  # Rust注释语法与JS相同

    def remove_php_comments(self, code):
        """去掉PHP代码中的注释"""
        # 去掉单行注释
        code = re.sub(r'//.*$', '', code, flags=re.MULTILINE)
        code = re.sub(r'#.*$', '', code, flags=re.MULTILINE)
        # 去掉多行注释
        code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)
        # 去掉空行
        lines = [line for line in code.split('\n') if line.strip()]
        return '\n'.join(lines)

    def remove_ruby_comments(self, code):
        """去掉Ruby代码中的注释"""
        # 去掉单行注释
        code = re.sub(r'#.*$', '', code, flags=re.MULTILINE)
        # 去掉多行注释
        code = re.sub(r'=begin.*?=end', '', code, flags=re.DOTALL)
        # 去掉空行
        lines = [line for line in code.split('\n') if line.strip()]
        return '\n'.join(lines)

    def remove_sql_comments(self, code):
        """去掉SQL代码中的注释"""
        # 去掉单行注释
        code = re.sub(r'--.*$', '', code, flags=re.MULTILINE)
        # 去掉多行注释
        code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)
        # 去掉空行
        lines = [line for line in code.split('\n') if line.strip()]
        return '\n'.join(lines)

    def remove_shell_comments(self, code):
        """去掉Shell脚本中的注释"""
        # 去掉单行注释
        code = re.sub(r'#.*$', '', code, flags=re.MULTILINE)
        # 去掉空行
        lines = [line for line in code.split('\n') if line.strip()]
        return '\n'.join(lines)

    def remove_yaml_comments(self, code):
        """去掉YAML文件中的注释"""
        return self.remove_shell_comments(code)  # YAML注释语法与Shell相同

    def should_exclude_dir(self, dir_path):
        """检查是否应该排除目录"""
        dir_name = os.path.basename(dir_path)
        return dir_name in self.exclude_dirs

    def should_exclude_file(self, file_path):
        """检查是否应该排除文件"""
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.exclude_files

    def extract_code_from_file(self, file_path):
        """从单个文件中提取代码"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in self.code_extensions:
                processor = self.code_extensions[file_ext]
                if processor:
                    content = processor(content)
                
                return content
            
            return None
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None

    def extract_code_from_directory(self, root_dir, target_lines=7000):
        """从目录中提取代码"""
        all_code = []
        total_lines = 0
        processed_files = []
        
        for root, dirs, files in os.walk(root_dir):
            # 排除不需要的目录
            dirs[:] = [d for d in dirs if not self.should_exclude_dir(os.path.join(root, d))]
            
            for file in files:
                if total_lines >= target_lines:
                    break
                    
                file_path = os.path.join(root, file)
                
                # 排除不需要的文件
                if self.should_exclude_file(file_path):
                    continue
                
                # 检查文件扩展名
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext not in self.code_extensions:
                    continue
                
                code = self.extract_code_from_file(file_path)
                if code and code.strip():
                    relative_path = os.path.relpath(file_path, root_dir)
                    
                    # 添加文件头注释
                    file_header = f"\n{'='*60}\n文件: {relative_path}\n{'='*60}\n"
                    
                    code_lines = code.count('\n')
                    if total_lines + code_lines <= target_lines:
                        all_code.append(file_header)
                        all_code.append(code)
                        total_lines += code_lines + 4  # 包括文件头的行数
                        processed_files.append((relative_path, code_lines))
                    else:
                        # 如果添加整个文件会超过目标行数，只添加部分内容
                        remaining_lines = target_lines - total_lines - 4
                        if remaining_lines > 10:  # 至少保留10行才有意义
                            truncated_code = '\n'.join(code.split('\n')[:remaining_lines])
                            all_code.append(file_header)
                            all_code.append(truncated_code)
                            all_code.append(f"\n... (文件被截断，原文件共{code_lines}行) ...\n")
                            processed_files.append((relative_path, remaining_lines))
                        break
            
            if total_lines >= target_lines:
                break
        
        return '\n'.join(all_code), processed_files, total_lines

def main():
    extractor = CodeExtractor()
    
    # 当前工作目录
    root_dir = '.'
    
    print("开始提取代码...")
    code_content, processed_files, total_lines = extractor.extract_code_from_directory(root_dir, 7000)
    
    # 保存到文件
    output_file = 'extracted_code.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"项目代码提取结果\n")
        f.write(f"提取时间: {__import__('datetime').datetime.now()}\n")
        f.write(f"总行数: {total_lines}\n")
        f.write(f"处理文件数: {len(processed_files)}\n")
        f.write("="*80 + "\n\n")
        
        f.write("处理的文件列表:\n")
        for file_path, lines in processed_files:
            f.write(f"  {file_path} ({lines} 行)\n")
        f.write("\n" + "="*80 + "\n")
        
        f.write(code_content)
    
    print(f"代码提取完成!")
    print(f"输出文件: {output_file}")
    print(f"总行数: {total_lines}")
    print(f"处理文件数: {len(processed_files)}")
    
    print("\n处理的文件:")
    for file_path, lines in processed_files:
        print(f"  {file_path} ({lines} 行)")

if __name__ == "__main__":
    main()
