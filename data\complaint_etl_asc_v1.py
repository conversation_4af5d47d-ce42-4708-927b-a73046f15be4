#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
投诉数据ES到ES迁移与结构化处理脚本
基于LangGraph实现，包含读取、处理、写入三个节点
"""
import json
import logging
import traceback
import re
import pandas as pd
import time
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
import requests
from elasticsearch import Elasticsearch
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_INDEX = "pro_mcp_data_complaint_v1"

SRC_ES_HOST = 'http://**********:9200'
SRC_ES_PORT = 9200
SRC_ES_AUTH = ('chenchao', 'GhW9U28REX$l')
SRC_INDEX = "wzty_cp_merchant_problem"

BATCH_SIZE = 1000

# 大模型参数
embedding_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings"
embedding_api_key = "ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=="
embedding_name = "bge-m3"

# llm_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1"
# llm_api_key = "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
# OPENAI_MODEL = "Qwen2-14B-Instruct"

llm_service_url = "http://************:11434/v1/"
llm_api_key = "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
OPENAI_MODEL = "qwen3:8b"

llm = ChatOpenAI(
    temperature=0,
    model=OPENAI_MODEL,
    openai_api_base=llm_service_url,
    api_key=llm_api_key
)

embedding_config = {
    "api_key": embedding_api_key,
    "service_url": embedding_service_url,
    "embedding_name": embedding_name
}

def get_embedding(text, embedding_config):
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            query_embedding = embdd_response.json()['data'][0]['embedding']
            DB_VECTOR_DIMENSION = 1536
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                    logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
                else:
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                    logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")
            logger.info(f"最终查询向量维度: {len(query_embedding)}")
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取embedding时出错: {str(e)}")
        return None

from html import unescape
from bs4 import BeautifulSoup

def html_to_markdown(html):
    if not html:
        return ''
    soup = BeautifulSoup(html, 'html.parser')
    for script in soup(["script", "style"]):
        script.extract()
    for i in range(1, 7):
        for tag in soup.find_all(f'h{i}'):
            tag.replace_with(f"{'#' * i} {tag.get_text().strip()}\n\n")
    for tag in soup.find_all('p'):
        tag.replace_with(f"{tag.get_text().strip()}\n\n")
    for ul in soup.find_all('ul'):
        for li in ul.find_all('li'):
            li.replace_with(f"* {li.get_text().strip()}\n")
    for ol in soup.find_all('ol'):
        for i, li in enumerate(ol.find_all('li')):
            li.replace_with(f"{i+1}. {li.get_text().strip()}\n")
    for a in soup.find_all('a', href=True):
        text = a.get_text().strip()
        href = a['href']
        a.replace_with(f"[{text}]({href})")
    for img in soup.find_all('img', src=True):
        alt = img.get('alt', '')
        src = img['src']
        img.replace_with(f"![{alt}]({src})")
    for strong in soup.find_all(['strong', 'b']):
        strong.replace_with(f"**{strong.get_text().strip()}**")
    for em in soup.find_all(['em', 'i']):
        em.replace_with(f"*{em.get_text().strip()}*")
    for blockquote in soup.find_all('blockquote'):
        lines = blockquote.get_text().strip().split('\n')
        quoted_text = '\n'.join([f"> {line}" for line in lines])
        blockquote.replace_with(f"{quoted_text}\n\n")
    for pre in soup.find_all('pre'):
        code = pre.get_text().strip()
        pre.replace_with(f"```\n{code}\n```\n\n")
    for code in soup.find_all('code'):
        code.replace_with(f"`{code.get_text().strip()}`")
    for table in soup.find_all('table'):
        md_table = []
        headers = []
        for th in table.find_all('th'):
            headers.append(th.get_text().strip())
        if headers:
            md_table.append('| ' + ' | '.join(headers) + ' |')
            md_table.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')
        for tr in table.find_all('tr'):
            row = []
            for td in tr.find_all('td'):
                row.append(td.get_text().strip())
            if row:
                md_table.append('| ' + ' | '.join(row) + ' |')
        table.replace_with('\n'.join(md_table) + '\n\n')
    for hr in soup.find_all('hr'):
        hr.replace_with('---\n\n')
    markdown = soup.get_text()
    markdown = re.sub(r'\n{3,}', '\n\n', markdown)
    markdown = markdown.strip()
    return markdown

def analyze_text(title: str, content: str) -> Dict[str, Any]:
    """分析投诉文本内容，提取关键信息，返回结构化字段"""
    if not content or len(content.strip()) < 10:
        logger.warning(f"投诉内容为空或过短 (长度: {len(content) if content else 0})")
        return get_default_result()
    if not title:
        logger.warning("投诉标题为空，使用默认标题")
        title = "无标题投诉"
    if len(content) > 15000:
        logger.warning(f"投诉内容过长 ({len(content)}字符)，将截断")
        content = content[:15000] + "...(内容已截断)"
    logger.info(f"处理投诉 - 标题: {title[:30]}... | 内容长度: {len(content)}")
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content="""你是一位经验丰富的互联网用户投诉分析专家，擅长从用户反馈中提取关键信息并进行深度洞察。\n请仔细阅读以下用户投诉原文，并根据要求提取或生成以下信息。\n输出格式要求为JSON，并且JSON的键（key）必须是以下指定的英文名称：\n\n请分析并输出以下字段（注意，JSON的键名请使用对应的英文名称）：\n\n1.  `core_issue`: (用一句话或简短几句话概括用户投诉的核心内容。)\n2.  `product_service_involved`: (明确指出投诉涉及的具体产品名称、服务环节或功能模块。如果原文未明确提及，请根据上下文推断，如果无法推断，则填写\"未知\"。)\n3.  `user_demand`: (用户希望得到什么样的解决方案或结果？例如：退款、道歉、修复、解释、赔偿等。如果用户未明确提出，请根据问题性质推断最可能的诉求。)\n4.  `sentiment`: (判断用户的主要情绪，例如：愤怒、失望、焦虑、急切、平静等。选择最主要的一个或两个，以字符串或字符串数组给出。)\n5.  `severity`: (评估问题对用户造成的影响程度或问题的紧急性，从\"低\"、\"中\"、\"高\"、\"紧急\"中选择一个。)\n6.  `potential_risks`: (从投诉内容中识别可能存在的风险，例如：法律风险、声誉风险、监管风险、用户流失风险、群体性事件风险等。如果无明显风险，则填写\"暂无明显风险\"或输出空数组。如果存在多个风险，请以字符串数组形式输出。)\n7.  `potential_causes`: (基于投诉描述，初步推断导致问题的最可能的一个或多个原因，例如：产品缺陷、流程不合理、信息不清、客服失误、系统故障等。请以字符串数组形式输出。)\n8.  `improvement_suggestions`: (基于投诉和可能原因，提出1-2条最直接相关的产品、服务或流程的改进建议。如果用户已提出明确建议，可直接采纳或优化表述。请以字符串数组形式输出。)\n9.  `complaint_category`: (请从以下预设分类中选择最合适的1-2个分类，并以字符串数组形式输出。预设分类：[\"功能BUG\", \"界面体验\", \"计费问题\", \"客服态度\", \"账号安全\", \"隐私泄露\", \"虚假宣传\", \"活动规则\", \"物流配送\", \"其他\"]。如果都不太合适，可以选择[\"其他\"]。)\n10. `keywords`: (提取3-5个最能代表投诉内容的关键词或短语，以字符串数组形式输出。)\n11. `summary`: (用300字以内概括投诉内容、分析和建议，这将用于生成向量嵌入)\n\n输出格式要求为JSON，且JSON的键（key）必须是上述指定的英文名称。"""),
        HumanMessage(content=f"""请分析以下用户投诉：\n标题: {title}\n投诉原文: {content}\n/no_think""")
    ])
    chain = prompt | llm
    try:
        result = chain.invoke({})
        raw_content = getattr(result, 'content', result)
        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)
        json_match = re.search(r'```json\s*({.*?})\s*```', text, flags=re.DOTALL)
        if not json_match:
            json_match = re.search(r'```\s*({.*?})\s*```', text, flags=re.DOTALL)
        if not json_match:
            json_match = re.search(r'({[\s\S]*?"core_issue"[\s\S]*?})', text, flags=re.DOTALL)
        if not json_match:
            json_match = re.search(r'({[\s\S]*?"keywords"[\s\S]*?})', text, flags=re.DOTALL)
        if not json_match:
            json_match = re.search(r'({.*})', text, flags=re.DOTALL)
        if not json_match:
            logger.error(f"未找到有效的JSON内容，原始内容前200字符: {raw_content[:200]}...")
            return get_default_result()
        json_str = json_match.group(1)
        json_str = json_str.replace('\\n', ' ').replace('\\r', '')
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        try:
            result = json.loads(json_str)
            if not validate_result_structure(result):
                logger.error("JSON结构不符合预期")
                return get_default_result()
            return result
        except json.JSONDecodeError as je:
            logger.error(f"JSON解析错误: {str(je)}")
            return get_default_result()
    except Exception as e:
        logger.error(f"分析文本时出错: {str(e)}")
        return get_default_result()

def validate_result_structure(data: Dict[str, Any]) -> bool:
    try:
        required_fields = {
            "core_issue": str,
            "product_service_involved": str,
            "user_demand": str,
            "sentiment": [str, list],
            "severity": str,
            "potential_risks": list,
            "potential_causes": list,
            "improvement_suggestions": list,
            "complaint_category": list,
            "keywords": list,
            "summary": str
        }
        for field, field_type in required_fields.items():
            if field not in data:
                logger.error(f"缺少必需字段: {field}")
                return False
            if field == "sentiment" and isinstance(field_type, list):
                if not any(isinstance(data[field], t) for t in field_type):
                    logger.error(f"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}")
                    return False
            elif not isinstance(data[field], field_type):
                logger.error(f"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}")
                return False
        if data["severity"] not in ["低", "中", "高", "紧急"]:
            logger.error("severity 字段值必须为 '低'、'中'、'高'或'紧急'")
            return False
        return True
    except Exception as e:
        logger.error(f"验证JSON结构时发生错误: {str(e)}")
        return False

def get_default_result() -> Dict[str, Any]:
    return {
        "core_issue": "",
        "product_service_involved": "未知",
        "user_demand": "",
        "sentiment": "未知",
        "severity": "中",
        "potential_risks": [],
        "potential_causes": [],
        "improvement_suggestions": [],
        "complaint_category": ["其他"],
        "keywords": [],
        "summary": ""
    }

def format_date(date_value) -> str:
    try:
        if pd.isna(date_value) or date_value is None:
            return "1970-01-01 00:00:00"
        if isinstance(date_value, pd.Timestamp):
            return date_value.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(date_value, datetime):
            return date_value.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(date_value, str):
            if date_value == "1970-01-01 00:00:00":
                return date_value
            if 'T' in date_value:
                date_value = date_value.replace('T', ' ')
            return date_value
        return str(date_value)
    except Exception as e:
        logger.warning(f"日期格式转换失败: {date_value}, 类型: {type(date_value)}, 错误: {str(e)}")
        return "1970-01-01 00:00:00"

def build_body_for_index(start_time, end_time):
    time_field = "last_gather_time"
    sort_field = "last_gather_time"
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            time_field: {
                                "gte": start_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "sort": [{sort_field: "asc"}]
    }
    return body

def get_es_scroll_data_batched(index, query_body, batch_size=1000):
    es7 = Elasticsearch(
        SRC_ES_HOST,
        http_auth=SRC_ES_AUTH,
        port=SRC_ES_PORT,
        timeout=3600,
        scheme="http",
        verify_certs=False
    )
    try:
        result = es7.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
        sid = result['_scroll_id']
        scroll_size = result['hits']['total']['value']
        print(f"索引 {index} 总数据量: {scroll_size}")
        if len(result['hits']['hits']) > 0:
            yield result['hits']['hits']
        scroll_count = len(result['hits']['hits'])
        while scroll_count > 0:
            result = es7.scroll(scroll_id=sid, scroll='10m')
            batch_data = result['hits']['hits']
            scroll_count = len(batch_data)
            if scroll_count == 0:
                break
            yield batch_data
    except Exception as e:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
        traceback.print_exc()
    finally:
        try:
            es7.clear_scroll(scroll_id=sid)
        except:
            pass
        print(f"索引 {index} 查询完成")

def process_data(current_docs):
    es = Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
    if es.ping():
        print(f"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
        es_info = es.info()
        logger.info(f"ES版本: {es_info['version']['number']}")
    else:
        print(f"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
    allowed_fields = {
        "appeal", "applbasques", "area", "author", "author_avatar", 
        "collective_amount", "collective_per", "collective_sn", "comment_amount", 
        "comment_id", "cost", "cotitle", "couid", "cp_type", "crawler_interval",
        "crawler_time", "created_at", "handle_depart", "handle_result", "id", 
        "indtypes", "industry", "issue", "last_gather_comment_time", 
        "last_gather_default_time", "last_gather_jiti_time", "last_gather_time",
        "last_gather_time _2", "meger_data_time", "obj_id", "product_model",
        "product_type", "satisfaction_level", "service_used", "share_amount",
        "site_name", "sn", "status", "summary", "title", "update_time",
        "upvote_amount", "url", 
        "core_issue", "product_service_involved", "user_demand", "sentiment",
        "severity", "potential_risks", "potential_causes", "improvement_suggestions",
        "complaint_category", "keywords", "embedding"
    }
    def clean_document(doc):
        cleaned = {}
        for key, value in doc.items():
            if key not in allowed_fields:
                continue
            if value is None:
                cleaned[key] = ""
            elif isinstance(value, (datetime, pd.Timestamp)):
                cleaned[key] = value.isoformat()
            elif hasattr(value, "is_nan") and value.is_nan():
                cleaned[key] = None
            else:
                cleaned[key] = value
        return cleaned
    try:
        processed_count = 0
        for source in current_docs:
            _id = source['_id']
            row = source['_source']
            content = (row.get("complaint_content", "") or 
                      row.get("content", "") or 
                      row.get("complaintContent", "") or 
                      row.get("complaint_desc", "") or
                      row.get("description", ""))
            title = (row.get("complaint_title", "") or 
                    row.get("title", "") or 
                    row.get("complaintTitle", "") or
                    row.get("subject", ""))
            if not content or len(content.strip()) < 10:
                potential_content_fields = {k: v for k, v in row.items() if isinstance(v, str) and len(v) > 50}
                if potential_content_fields:
                    longest_field = max(potential_content_fields.items(), key=lambda x: len(x[1]))
                    content = longest_field[1]
            clean_text = html_to_markdown(content)
            analysis_result = analyze_text(title, clean_text)
            target_doc = {}
            target_doc["core_issue"] = analysis_result.get("core_issue", "")
            target_doc["product_service_involved"] = analysis_result.get("product_service_involved", "")
            target_doc["user_demand"] = analysis_result.get("user_demand", "")
            sentiment = analysis_result.get("sentiment", "")
            if isinstance(sentiment, list) and sentiment:
                target_doc["sentiment"] = sentiment[0]
            else:
                target_doc["sentiment"] = sentiment if sentiment else ""
            target_doc["severity"] = analysis_result.get("severity", "")
            target_doc["potential_risks"] = analysis_result.get("potential_risks", [])
            target_doc["potential_causes"] = analysis_result.get("potential_causes", [])
            target_doc["improvement_suggestions"] = analysis_result.get("improvement_suggestions", [])
            target_doc["complaint_category"] = analysis_result.get("complaint_category", [])
            target_doc["keywords"] = analysis_result.get("keywords", [])
            target_doc["embedding"] = get_embedding(analysis_result.get("core_issue", ""), embedding_config)
            for field in allowed_fields:
                if field in row and field not in target_doc:
                    target_doc[field] = row[field]
            target_doc = clean_document(target_doc)
            try:
                es.index(
                    index=TARGET_ES_INDEX,
                    id=_id,
                    body=target_doc
                )
                print(f'插入：{_id},{target_doc["last_gather_time"]}')
                processed_count += 1
            except Exception as e:
                print(f"保存文档 {_id} 时出错: {type(e).__name__}: {str(e)}")
                try:
                    print(f"文档部分内容: {json.dumps(dict(list(target_doc.items())[:5]), default=str)}")
                except:
                    print("无法打印文档内容")
                logger.error(f"保存文档 {_id} 时出错: {str(e)}")
        logger.info(f"处理文档: {processed_count}")
        return processed_count
    except Exception as e:
        traceback.print_exc()
        logger.error(f"处理数据时出错: {str(e)}")
    finally:
        if es:
            try:
                es.close()
                logger.debug("ES客户端已关闭")
            except Exception as close_error:
                traceback.print_exc()
                logger.warning(f"关闭ES客户端时出错: {str(close_error)}")

# LangGraph工作流定义
class AgentState(TypedDict):
    current_docs: List[Dict]
    start_time: Optional[str]
    end_time: Optional[str]

def fetch_data(state):
    body = build_body_for_index(state["start_time"], state["end_time"])
    all_batches = []
    for batch in get_es_scroll_data_batched(SRC_INDEX, body, BATCH_SIZE):
        all_batches.append(batch)
    state["current_docs"] = all_batches
    return state

def process_batch(state):
    sum0dcl = process_data(state["current_docs"])
    print(sum0dcl)
    return state

def get_hour_range(start_time_str: str = None):
    """
    输入：开始时间字符串，为空则用当前时间
    输出：该小时的开始时间和结束时间（'2024-05-19 14:00:00', '2024-05-19 14:59:59'）
    """
    if not start_time_str or start_time_str.strip() == "":
        dt = datetime.now()
    else:
        dt = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
    
    # 如果要处理前一小时，需要减去一小时
    # dt = dt - timedelta(hours=1)  # 如果要处理前一小时，取消注释
    
    hour_str = dt.strftime("%Y-%m-%d %H")
    start_time = f"{hour_str}:00:00"  # 小时开始
    end_time = f"{hour_str}:59:59"    # 小时结束
    return start_time, end_time

workflow = StateGraph(AgentState)
workflow.add_node("fetch_data", fetch_data)
workflow.add_node("process_batch", process_batch)
workflow.add_edge("fetch_data", "process_batch")
workflow.add_edge("process_batch", END)
workflow.set_entry_point("fetch_data")
workflow = workflow.compile()

if __name__ == "__main__":

    start, end = get_hour_range('2025-05-24 18:10:58') ## 长相
    while True:
        print("开始时间:", start)
        print("结束时间:", end)
        initial_state = {
            "current_docs": [],
            "start_time": start,
            "end_time": end
        }
        state = fetch_data(initial_state)
        if not state["current_docs"]:
            print("无数据，跳过")
            if datetime.strptime(start, "%Y-%m-%d %H:%M:%S") >= datetime.now():
                print("已同步至最新数据，休眠60秒后继续...")
                time.sleep(60)
            else:
                print(f"继续同步下一时间段: {start} 到 {end}")
        else:
            for batch in state["current_docs"]:
                batch_state = {
                    "current_docs": batch,
                    "start_time": state["start_time"],
                    "end_time": state["end_time"]
                }
                process_batch(batch_state)
            start, end = get_hour_range(end)
            if datetime.strptime(end, "%Y-%m-%d %H:%M:%S") > datetime.now():
                print("已同步至最新数据，任务完成")
                break 

# # 可以使用一个简单的文件或数据库表记录已处理的时间段
# def is_time_range_processed(start, end):
#     # 检查是否已处理
#     return False  # 实现检查逻辑

# def mark_time_range_processed(start, end):
#     # 标记为已处理
#     pass  # 实现标记逻辑

# # 在处理前检查
# if not is_time_range_processed(start, end):
#     # 处理
#     mark_time_range_processed(start, end)
# else:
#     print(f"时间段 {start} - {end} 已处理，跳过") 

# # 可以添加心跳机制
# last_heartbeat = datetime.now()
# def update_heartbeat():
#     global last_heartbeat
#     last_heartbeat = datetime.now()
#     # 将心跳写入某个文件或数据库

# # 在循环内定期更新
# update_heartbeat() 