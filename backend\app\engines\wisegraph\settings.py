"""
WiseGraph配置模块
从环境变量和现有配置中读取wisegraph相关配置
"""

from app.utils.config import settings


class WiseGraphSettings:
    """WiseGraph配置类"""
    
    def __init__(self):
        # Neo4j配置
        self.neo4j_config = {
            "uri": settings.NEO4J_URI,
            "username": settings.NEO4J_USERNAME,
            "password": settings.NEO4J_PASSWORD,
            "database": settings.NEO4J_DATABASE
        }
        
        # OpenAI配置（从环境变量读取）
        self.openai_config = {
            "api_key": settings.OPENAI_API_KEY,
            "base_url": settings.OPENAI_BASE_URL,
            "model": settings.OPENAI_MODEL,
            "temperature": settings.OPENAI_TEMPERATURE,
            "max_tokens": settings.OPENAI_MAX_TOKENS
        }
        
        # MongoDB配置（复用现有配置）
        self.mongo_config = {
            "url": settings.MONGODB_URL,
            "database": settings.DATABASE_NAME
        }
        
        # 图查询配置
        self.graph_query_config = {
            "max_depth": settings.GRAPH_QUERY_MAX_DEPTH,
            "max_results": settings.GRAPH_QUERY_MAX_RESULTS,
            "similarity_threshold": settings.GRAPH_QUERY_SIMILARITY_THRESHOLD
        }
    
    @property
    def neo4j_uri(self):
        return self.neo4j_config["uri"]
    
    @property
    def neo4j_username(self):
        return self.neo4j_config["username"]
    
    @property
    def neo4j_password(self):
        return self.neo4j_config["password"]
    
    @property
    def neo4j_database(self):
        return self.neo4j_config["database"]
    
    @property
    def openai_api_key(self):
        return self.openai_config["api_key"]
    
    @property
    def openai_base_url(self):
        return self.openai_config["base_url"]
    
    @property
    def openai_model(self):
        return self.openai_config["model"]


# 全局配置实例
wisegraph_settings = WiseGraphSettings()


# 为了保持向后兼容，提供原有的配置字典格式
def get_neo4j_config():
    """获取Neo4j配置"""
    return wisegraph_settings.neo4j_config


def get_openai_config():
    """获取OpenAI配置"""
    return wisegraph_settings.openai_config


def get_mongo_config():
    """获取MongoDB配置"""
    return wisegraph_settings.mongo_config


def get_graph_query_config():
    """获取图查询配置"""
    return wisegraph_settings.graph_query_config
