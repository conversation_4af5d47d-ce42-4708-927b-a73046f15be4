import time
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from app.utils.logging_config import get_logger
import psutil
import os

logger = get_logger(__name__)

class PerformanceMonitorMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.process = psutil.Process(os.getpid())
        self.total_requests = 0
        self.slow_requests = 0
        self.error_requests = 0
        self.last_log_time = time.time()
        self.log_interval = 300  # 每5分钟记录一次性能统计

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        self.total_requests += 1
        
        # 记录请求前的内存使用
        mem_before = self.process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            process_time = (time.time() - start_time) * 1000
            
            # 记录慢请求
            if process_time > 1000:  # 超过1秒
                self.slow_requests += 1
                
            # 定期记录性能统计
            current_time = time.time()
            if current_time - self.last_log_time > self.log_interval:
                self.log_performance_stats()
                self.last_log_time = current_time
                
            return response
            
        except Exception as e:
            # 记录错误请求
            self.error_requests += 1
            raise
            
    def log_performance_stats(self):
        """记录性能统计信息"""
        try:
            # 获取系统性能指标
            cpu_percent = self.process.cpu_percent()
            mem_usage = self.process.memory_info().rss / 1024 / 1024  # MB
            
            # 记录性能日志
            logger.info(f"性能统计: 总请求数={self.total_requests}, 慢请求数={self.slow_requests}, "
                       f"错误请求数={self.error_requests}, CPU使用率={cpu_percent}%, "
                       f"内存使用={mem_usage:.2f}MB")
        except Exception as e:
            logger.error(f"记录性能统计失败: {str(e)}")
