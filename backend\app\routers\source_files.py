from fastapi import APIRouter, HTTPException, Depends, Query, Body
from typing import Optional, List, Dict, Any
from ..models.source_files import SourceFileResponse, SourceFileInfoResponse
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime, timedelta
from ..db.mongodb import db
from ..db.miniIO import minio
from app.utils.enums import FileStatus, chunk_type
import traceback
from app.engines.embedding.embedding_utils import get_embedding
from app.engines.retrieval.base_retriever import es_data_ingester, update_by_query, es_data_deleter

from app.utils.config import settings

from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

import mimetypes
from urllib.parse import quote
import httpx
from fastapi.responses import StreamingResponse
import os
import aiofiles

router = APIRouter(
    prefix="/api",
    tags=["source_files"]
)

# 获取文件列表
@router.get("/source_files", response_model=Dict[str, Any])
async def get_source_files(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    knowledge_base_id: Optional[str] = None,
    filename: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"flg": 1}
    
    if knowledge_base_id:
        query["knowledge_base_id"] = ObjectId(knowledge_base_id)
    if filename:
        query["filename"] = {"$regex": filename, "$options": "i"}

    # 获取文件列表
    files = await db.source_files.find(query).sort(
        "created_at", -1  # 按创建时间倒序
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db.source_files.count_documents(query)
    
    # 计算文本块总数
    total_chunks = sum(file.get("chunk_count", 0) for file in files)
    
    # 处理ObjectId
    for file in files:
        file["id"] = str(file["_id"])
        del file["_id"]
        if "knowledge_base_id" in file:
            file["knowledge_base_id"] = str(file["knowledge_base_id"])
    
    return {
        "data": [SourceFileResponse(**file) for file in files],
        "total": total,
        "total_chunks": total_chunks,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }


# 获取文件列表
@router.get("/conversation_source_files", response_model=Dict[str, Any])
async def get_source_files(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    knowledge_base_id: Optional[str] = None,
    filename: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    query = {
             "files_source_type": "chat"
             }
    
    if knowledge_base_id:
        query["knowledge_base_id"] = ObjectId(knowledge_base_id)
    if filename:
        query["filename"] = {"$regex": filename, "$options": "i"}



    uploaded_files = await db["source_files"].find(query).to_list(1000)
    logger.info(f"uploaded_files: {uploaded_files}")
        
    # 处理文件信息
    files_info = []
    for file in uploaded_files:
        file_info = {
            "id": str(file["_id"]),
            "filename": file.get("name", "未命名文件"),
            "data_type": file.get("data_type", "unknown"),
            "size": file.get("size", 0),
            "processing_status": file.get("flg", 0),
            "created_at": file["created_at"].strftime("%Y-%m-%d %H:%M:%S") if file.get("created_at") else None,
            "chunk_count": file.get("chunk_count", 0),
            "url": file.get("url", "")
        }
        files_info.append(file_info)
    
    return {
        "data": files_info,
        "success": True,
    }



# 获取单个文件详情
@router.get("/source_file/{file_id}", response_model=SourceFileInfoResponse)
async def get_source_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    file = await db.source_files.find_one({"_id": ObjectId(file_id)})

    if not file:
        raise HTTPException(status_code=404, detail="File not found")
    else:
        file["id"] = str(file["_id"])
        del file["_id"]
        if "knowledge_base_id" in file:
            file["knowledge_base_id"] = str(file["knowledge_base_id"])
        # logger.info(f"file: {file}")
        return SourceFileInfoResponse(**file)


# 删除文件（软删除）
@router.delete("/source_files/{file_id}")
async def delete_source_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    file = await db.source_files.find_one({"_id": ObjectId(file_id)})
    if not file:
        raise HTTPException(status_code=404, detail="File not found")
    
    # 软删除文件
    result = await db.source_files.update_one(
        {"_id": ObjectId(file_id)},
        {
            "$set": {
                "flg": FileStatus.DELETED,
                "deleted_by": current_user["id"]
            }
        }
    )
    
    # 更新知识库的文件计数
    if "knowledge_base_id" in file:
        await db.knowledge_bases.update_one(
            {"_id": file["knowledge_base_id"]},
            {"$inc": {"file_count": -1}}
        )

    # 查找与文件关联的所有chunks，获取它们的ID以便从ES中删除
    chunks = await db.chunks.find({"file_id": ObjectId(file_id)}).to_list(length=None)
    chunk_ids = [str(chunk["_id"]) for chunk in chunks]
    
    # 删除关联的chunks
    await db.chunks.delete_many({"file_id": ObjectId(file_id)})
    
    # 删除chunk_index
    await db.chunks_index.delete_many({"file_id": ObjectId(file_id)})
    
    # 删除Elasticsearch中的索引
    if chunk_ids and "knowledge_base_id" in file:
        try:
            es_data_deleter(
                ids=chunk_ids,
                chunk_type=chunk_type.BASE,
                kb_id=str(file["knowledge_base_id"]),
                es_host=settings.ES_HOST,
                index_name=settings.ES_INDEX
            )
            logger.info(f"ES数据删除成功，删除了{len(chunk_ids)}条索引")
        except Exception as e:
            logger.error(f"ES数据删除失败: {str(e)}")
            # 即使ES删除失败，也继续执行
    
    if result.modified_count == 0:
        raise HTTPException(status_code=404, detail="File not found")
    
    return {"success": True}



@router.get("/source_files/reset/{file_id}")
async def reset_source_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        file = await db.source_files.find_one({"_id": ObjectId(file_id)})
        if not file:
            raise HTTPException(status_code=404, detail="File not found")
        
        # 软删除文件
        result = await db.source_files.update_one(
            {"_id": ObjectId(file_id)},
            {"$set": {"flg":FileStatus.WAITING}}
        )
        
        # 查找与文件关联的所有chunks，获取它们的ID以便从ES中删除
        chunks = await db.chunks.find({"file_id": ObjectId(file_id)}).to_list(length=None)
        chunk_ids = [str(chunk["_id"]) for chunk in chunks]
        
        # 删除关联的chunks并获取删除数量
        chunks_result = await db.chunks.delete_many({"file_id": ObjectId(file_id)})
        
        # 删除chunk_index并获取删除数量
        chunks_index_result = await db.chunks_index.delete_many(
            {"file_id": ObjectId(file_id)})
        
        # 删除Elasticsearch中的索引
        if chunk_ids and "knowledge_base_id" in file:
            try:
                es_data_deleter(
                    ids=chunk_ids,
                    chunk_type=chunk_type.BASE,
                    kb_id=str(file["knowledge_base_id"]),
                    es_host=settings.ES_HOST,
                    index_name=settings.ES_INDEX
                )
                logger.info(f"ES数据删除成功，删除了{len(chunk_ids)}条索引")
            except Exception as e:
                logger.error(f"ES数据删除失败: {str(e)}")
                # 即使ES删除失败，也继续执行
        
        # 计算总删除数量
        
        return {
            "success": True,
            "deleted_chunk": chunks_result.deleted_count,
            "deleted_chunk_index":chunks_index_result.deleted_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置失败: {str(e)}")

# 获取文件分块列表
@router.get("/source_files/chunks/{file_id}", response_model=Dict[str, Any])
async def get_file_chunks(
    file_id: str,
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    searchText: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    try:
        skip = (current - 1) * pageSize
        query = {"file_id": ObjectId(file_id)}
        
        # 添加搜索条件
        if searchText:
            query["$or"] = [
                {"answer": {"$regex": searchText, "$options": "i"}},
                {"question": {"$regex": searchText, "$options": "i"}}
            ]
        
        # 获取分块列表
        chunks = await db.chunks.find(query,{
            "tokens_count":1,
            "chunk_index":1,
            "answer":1,
            "question":1,
            "created_at":1,
            "last_updated":1,
            "is_expired":1,
            "status":1,
        }).sort(
            "created_at", -1  # 按分块索引升序排序
        ).skip(skip).limit(pageSize).to_list(length=pageSize)
        
        # 获取总数
        total = await db.chunks.count_documents(query)
        
        # 处理ObjectId
        for chunk in chunks:
            chunk["id"] = str(chunk["_id"])
            del chunk["_id"]

        # logger.info(chunks)
        
        return {
            "data": chunks,
            "total": total,
            "success": True,
            "pageSize": pageSize,
            "current": current,
            "message": "获取文件分块列表成功"
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取文件分块列表失败: {str(e)}")

# 获取文件页面列表
@router.get("/source_files/pages/{file_id}", response_model=Dict[str, Any])
async def get_file_pages(
    file_id: str,
    current: int = Query(1, ge=1, description="当前页码"),
    pageSize: int = Query(10, ge=1, description="每页数量"),
    content: Optional[str] = None,
    page_num: Optional[int] = None,
    current_user: dict = Depends(verify_token)
    
):
    skip = (current - 1) * pageSize
    query = {"file_id": ObjectId(file_id)}
    if content:
        query["content"] = {"$regex": content, "$options": "i"}
    if page_num:
        query["page_num"] = page_num
    
    # 获取页面列表
    pages = await db.source_files_pages.find(query).sort(
        "page_num", 1  # 按页码升序排序
    ).skip(skip).limit(pageSize).to_list(None)
    
    # 获取总数
    total = await db.source_files_pages.count_documents(query)
    
    # 处理ObjectId
    for page in pages:
        page["id"] = str(page["_id"])
        del page["_id"]
        page["file_id"] = str(page["file_id"])
    
    return {
        "data": pages,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取文件预览URL
@router.get("/source_files/preview/{file_id}")
async def get_file_preview(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    file = await db.source_files.find_one({"_id": ObjectId(file_id), "flg": 1})
    if not file:
        raise HTTPException(status_code=404, detail="File not found")
    
    return {
        "success": True,
        "data": {
            "url": file["storage_path"]
        }
    }

# 添加新的文件内容转发接口
@router.get("/source_files/content/{file_id}")
async def get_file_content(
    file_id: str,
    download: bool = Query(False, description="Force download instead of preview"),
    current_user: dict = Depends(verify_token)
):
    try:
        file_info = await db.source_files.find_one({"_id": ObjectId(file_id)})
        logger.info(f"file_info: {file_info}")
        if not file_info:
            raise HTTPException(status_code=404, detail="File not found")
            
        storage_path = file_info.get("storage_path")
        original_filename = file_info.get("name")  # 获取原始文件名
        if not original_filename:
            raise HTTPException(status_code=400, detail="File name not found")
            
        logger.info(f"original_filename: {original_filename}")
        
        if not storage_path:
            raise HTTPException(status_code=404, detail="File path not found")

        # 优先使用 data_type 确定 MIME 类型
        data_type = file_info.get("data_type", "").upper()
        mime_type = "application/octet-stream"
        logger.info(f"data_type: {data_type}")
        
        # 根据 data_type 映射 MIME 类型
        mime_type_map = {
            'PDF': 'application/pdf',
            'JPG': 'image/jpeg',
            'JPEG': 'image/jpeg',
            'PNG': 'image/png',
            'TXT': 'text/plain',
            'MD': 'text/markdown',
            'JSON': 'application/json',
            'XML': 'application/xml',
            'CSV': 'text/csv',
            'HTML': 'text/html',
            'HTM': 'text/html',
            'DOC': 'application/msword',
            'DOCX': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'XLS': 'application/vnd.ms-excel',
            'XLSX': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'PPTX': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'WAV': 'audio/wav',
            'MP3': 'audio/mpeg'
        }

        if data_type in mime_type_map:
            mime_type = mime_type_map[data_type]
            logger.info(f"Using data_type for MIME type: {data_type} -> {mime_type}")
        else:
            # 如果没有 data_type 或不在映射表中，才使用文件扩展名猜测
            mime_type = mimetypes.guess_type(original_filename)[0] or "application/octet-stream"
            logger.info(f"Guessed MIME type from filename: {mime_type}")

        # 设置 Content-Disposition
        disposition_type = 'attachment' if download else 'inline'
        # 对文件名进行URL编码，解决特殊字符问题
        encoded_filename = quote(original_filename.encode('utf-8'))
        content_disposition = f'{disposition_type}; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        
        logger.info(f"Content-Disposition: {content_disposition}")

        # 首先判断文件的存储类型
        storage_type = file_info.get("storage_type")
        logger.info(f"Storage type: {storage_type}")
        
        # 如果文件存储类型是 MinIO 或者全局设置启用了 MinIO
        if storage_type == 'minio' or (settings.MINIO_ENABLE and storage_type is None):
            try:
                # 从存储路径中提取对象名称
                object_name = None
                if '/' in storage_path:
                    # 如果是完整URL，解析对象名称
                    parts = storage_path.split('/')
                    object_name = parts[-1]
                    if '?' in object_name:
                        object_name = object_name.split('?')[0]
                else:
                    # 如果只是对象名称
                    object_name = storage_path
                
                logger.info(f"MinIO object name: {object_name}")
                
                # 获取临时URL，确保它是有效的
                try:
                    presigned_url = minio.client.presigned_get_object(
                        settings.MINIO_BUCKET,
                        object_name,
                        expires=timedelta(hours=1)
                    )
                    logger.info(f"Generated presigned URL: {presigned_url}")
                except Exception as e:
                    logger.error(f"Failed to generate presigned URL: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Failed to generate file URL: {str(e)}")
                
                # 使用临时URL获取文件内容
                async with httpx.AsyncClient(timeout=30.0) as client:
                    try:
                        response = await client.get(presigned_url)
                        response.raise_for_status()
                        logger.info(f"Using data_type for MIME type: {data_type} -> {mime_type}")
                        
                        # 强制设置响应的 Content-Type
                        headers = {
                            "Content-Type": mime_type,
                            "Content-Disposition": content_disposition,
                            "Cache-Control": "no-cache",
                            "X-Content-Type-Options": "nosniff",
                        }
                        
                        return StreamingResponse(
                            content=response.iter_bytes(),
                            media_type=mime_type,
                            headers=headers
                        )
                    except httpx.HTTPError as http_err:
                        logger.error(f"HTTP error occurred: {str(http_err)}")
                        raise HTTPException(status_code=500, detail=f"Failed to download file: {str(http_err)}")
                        
            except Exception as e:
                logger.error(f"Download error: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to download file: {str(e)}")
        else:
            # 本地文件处理部分也使用原始文件名
            file_path = os.path.join(settings.FILE_UPLOAD_PATH, os.path.basename(storage_path))
            logger.info(f"Local file path: {file_path}")
            
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail=f"File not found on disk: {file_path}")
            
            if not os.path.isfile(file_path):
                raise HTTPException(status_code=400, detail="Invalid file path")
                
            async def file_iterator():
                async with aiofiles.open(file_path, 'rb') as f:
                    while chunk := await f.read(8192):
                        yield chunk
            logger.info(f"Using data_type for MIME 2222: {data_type} -> {mime_type}")
            return StreamingResponse(
                content=file_iterator(),
                media_type=mime_type,
                headers={
                    "Content-Type": mime_type,
                    "Content-Disposition": content_disposition,
                    "Cache-Control": "no-cache",
                    "X-Content-Type-Options": "nosniff",
                }
            )
            
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error previewing file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error previewing file: {str(e)}")

# 批量删除文件（软删除）
@router.post("/source_files/batchDelete")
async def batch_delete_source_files(
    ids: List[str],
    current_user: dict = Depends(verify_token)
):
    try:
        # 转换ID为ObjectId
        object_ids = [ObjectId(id) for id in ids]
        
        # 获取文件信息，用于后续更新知识库计数和ES删除
        files = await db.source_files.find({"_id": {"$in": object_ids}}).to_list(length=None)
        
        # 按知识库ID分组文件
        files_by_kb = {}
        for file in files:
            if "knowledge_base_id" in file:
                kb_id = str(file["knowledge_base_id"])
                if kb_id not in files_by_kb:
                    files_by_kb[kb_id] = []
                files_by_kb[kb_id].append(str(file["_id"]))
        
        # 查找所有相关chunks的ID，用于从ES删除
        all_chunk_ids = []
        for file_id in object_ids:
            chunks = await db.chunks.find({"file_id": file_id}).to_list(length=None)
            chunk_ids = [str(chunk["_id"]) for chunk in chunks]
            all_chunk_ids.extend(chunk_ids)
        
        # 软删除文件
        result = await db.source_files.update_many(
            {"_id": {"$in": object_ids}},
            {
                "$set": {
                    "flg": FileStatus.DELETED,
                    "deleted_by": current_user["id"]
                }
            }
        )
        
        # 更新知识库的文件计数
        for kb_id, file_ids in files_by_kb.items():
            await db.knowledge_bases.update_one(
                {"_id": ObjectId(kb_id)},
                {"$inc": {"file_count": -len(file_ids)}}
            )
        
        # 删除关联的chunks
        await db.chunks.delete_many({"file_id": {"$in": object_ids}})
        
        # 删除chunk_index
        await db.chunks_index.delete_many({"file_id": {"$in": object_ids}})
        
        # 删除Elasticsearch中的索引
        if all_chunk_ids:
            for kb_id in files_by_kb.keys():
                try:
                    es_data_deleter(
                        ids=all_chunk_ids,
                        chunk_type=chunk_type.BASE,
                        kb_id=kb_id,
                        es_host=settings.ES_HOST,
                        index_name=settings.ES_INDEX
                    )
                    logger.info(f"ES数据删除成功，删除了{len(all_chunk_ids)}条索引")
                except Exception as e:
                    logger.error(f"ES数据删除失败: {str(e)}")
                    # 即使ES删除失败，也继续执行
        
        return {
            "success": True,
            "deleted_count": result.modified_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

# 批量重置文件
@router.post("/source_files/batchReset")
async def batch_reset_source_files(
    ids: List[str],
    current_user: dict = Depends(verify_token)
):
    try:
        object_ids = [ObjectId(id) for id in ids]
        
        # 获取文件信息，用于后续ES删除
        files = await db.source_files.find({"_id": {"$in": object_ids}}).to_list(length=None)
        
        # 按知识库ID分组文件
        files_by_kb = {}
        for file in files:
            if "knowledge_base_id" in file:
                kb_id = str(file["knowledge_base_id"])
                if kb_id not in files_by_kb:
                    files_by_kb[kb_id] = []
                files_by_kb[kb_id].append(str(file["_id"]))
        
        # 查找所有相关chunks的ID，用于从ES删除
        all_chunk_ids = []
        for file_id in object_ids:
            chunks = await db.chunks.find({"file_id": file_id}).to_list(length=None)
            chunk_ids = [str(chunk["_id"]) for chunk in chunks]
            all_chunk_ids.extend(chunk_ids)
        
        # 重置文件状态
        result = await db.source_files.update_many(
            {"_id": {"$in": object_ids}},
            {"$set": {"flg": FileStatus.WAITING}}
        )
        
        # 删除关联的chunks
        chunks_result = await db.chunks.delete_many({"file_id": {"$in": object_ids}})
        
        # 删除chunk_index
        chunks_index_result = await db.chunks_index.delete_many({"file_id": {"$in": object_ids}})
        
        # 删除Elasticsearch中的索引
        if all_chunk_ids:
            for kb_id in files_by_kb.keys():
                try:
                    es_data_deleter(
                        ids=all_chunk_ids,
                        chunk_type=chunk_type.BASE,
                        kb_id=kb_id,
                        es_host=settings.ES_HOST,
                        index_name=settings.ES_INDEX
                    )
                    logger.info(f"ES数据删除成功，删除了{len(all_chunk_ids)}条索引")
                except Exception as e:
                    logger.error(f"ES数据删除失败: {str(e)}")
                    # 即使ES删除失败，也继续执行
        
        return {
            "success": True,
            "deleted_chunk": chunks_result.deleted_count,
            "deleted_chunk_index": chunks_index_result.deleted_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量重置失败: {str(e)}")

async def get_content_embedding(knowledge_base_id, content):
    """
    根据知识库ID和内容生成embedding
    """
    try:
        # 查找知识库配置
        kb_info = await db.knowledge_bases.find_one({"_id": knowledge_base_id})
        if not kb_info:
            raise Exception("知识库不存在")
        embedding_id = kb_info.get("embedding_model_id")
        if not embedding_id:
            raise Exception("知识库未配置 embedding_model_id")
        embedding_config = await db.embeddings.find_one({"id": int(embedding_id)})
        if not embedding_config:
            raise Exception("embedding 配置不存在")
        embedding = get_embedding(content, embedding_config)
        if embedding and len(embedding) < 1536:
            embedding.extend([0] * (1536 - len(embedding)))
        return embedding
    except Exception as e:
        traceback.print_exc()
        raise Exception(f"embedding生成失败: {str(e)}")

@router.post("/source_files/chunks/{file_id}")
async def create_file_chunk(
    file_id: str,
    data: dict = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    新增文件分段（chunk），file_id在路径，answer/question在body，插入chunk后自动插入chunk_index
    """
    try:
        answer = data.get("answer", "")
        question = data.get("question", "")

        # 检查文件是否存在
        file = await db.source_files.find_one({"_id": ObjectId(file_id)})
        if not file:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 自动分配 chunk_index
        chunk_count = await db.chunks.count_documents({"file_id": ObjectId(file_id)})
        chunk_index_num = chunk_count + 1

        chunk_doc = {
            "file_id": ObjectId(file_id),
            "answer": answer,
            "question": question,
            "chunk_index": chunk_index_num,
            "knowledge_base_id": file.get("knowledge_base_id"),
            "tokens_count": len(answer) + len(question),
            "created_at": datetime.utcnow(),
            "last_updated": datetime.utcnow(),
            "is_expired": False,
            "status": 1,
        }
        chunk_result = await db.chunks.insert_one(chunk_doc)
        chunk_id = chunk_result.inserted_id

        # 获取知识库ID
        knowledge_base_id = file.get("knowledge_base_id")
        if not knowledge_base_id:
            raise HTTPException(status_code=400, detail="文件未关联知识库")

        # 生成 embedding（调用封装方法）
        embedding = await get_content_embedding(knowledge_base_id, answer)

        chunk_index_doc = {
            "file_id": ObjectId(file_id),
            "chunk_id": chunk_id,
            "knowledge_base_id": knowledge_base_id,
            "index_content": answer,
            "chunk_type": 0,
            "chunk_index": chunk_index_num,
            "embedding": embedding,
            "is_expired": False,
            "created_at": datetime.utcnow(),
        }
        chunk_index_result = await db.chunks_index.insert_one(chunk_index_doc)
        # 插入ES
        es_host = settings.ES_HOST
        index_name = settings.ES_INDEX
        es_doc_id = str(chunk_index_result.inserted_id)

        def convert_all_datetime(obj):
            if isinstance(obj, dict):
                return {k: convert_all_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_all_datetime(i) for i in obj]
            elif isinstance(obj, datetime):
                return obj.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(obj, ObjectId):
                return str(obj)
            else:
                return obj

        es_doc_body = convert_all_datetime(chunk_index_doc)
        es_doc_body["id"] = es_doc_id
        try:
            logger.info(f"插入ES: {es_doc_body}")
            es_data_ingester(es_doc_id, es_doc_body, es_host, index_name)
        except Exception as e:
            logger.error(f"插入ES失败: {e}")

        return {
            "success": True,
            "id": str(chunk_id),
            "message": "分段及索引添加成功"
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"添加分段失败: {str(e)}")




