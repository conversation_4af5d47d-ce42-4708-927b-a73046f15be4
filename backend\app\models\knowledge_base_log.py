from mongoengine import Document, StringField, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class KnowledgeBase(Document):
    meta = {
        'collection': 'knowledge_bases_log'
    }
    _id = ObjectIdField(required=True)
    knowledge_base_id = StringField(required=True)
    created_at = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    query = StringField(required=True)
    type = StringField(required=True)
    
