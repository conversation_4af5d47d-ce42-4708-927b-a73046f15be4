"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3325],{85673:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"}},77171:function(e,t,n){n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"},a=n(91146),s=function(e,t){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:i}))};var c=o.forwardRef(s)},14409:function(e,t,n){var r=n(1413),o=n(67294),i=n(85673),a=n(91146),s=function(e,t){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:i.Z}))},c=o.forwardRef(s);t.Z=c},46466:function(e,t,n){n.r(t),n.d(t,{default:function(){return M}});var r=n(15009),o=n.n(r),i=n(99289),a=n.n(i),s=n(5574),c=n.n(s),u=n(67294),l=n(97131),d=n(98137),f=n(74330),p=n(71230),h=n(15746),v=n(4393),y=n(14409),b=n(77171),m=n(97582),g=n(60614),x=n(18587);function w(e,t){var n={};return t.forEach((function(t){n[t]=e[t]})),n}function S(e){return"function"==typeof e}function j(e){return"string"==typeof e}var O=n(64063),E=n.n(O),_=function(e){function t(t){var n=e.call(this,t)||this;return n.echarts=g,n}return(0,m.ZT)(t,e),t}(function(e){function t(t){var n=e.call(this,t)||this;return n.echarts=t.echarts,n.ele=null,n.isInitialResize=!0,n}return(0,m.ZT)(t,e),t.prototype.componentDidMount=function(){this.renderNewEcharts()},t.prototype.componentDidUpdate=function(e){var t=this.props.shouldSetOption;if(!S(t)||t(e,this.props)){if(!E()(e.theme,this.props.theme)||!E()(e.opts,this.props.opts))return this.dispose(),void this.renderNewEcharts();var n=this.getEchartsInstance();E()(e.onEvents,this.props.onEvents)||(this.offEvents(n,e.onEvents),this.bindEvents(n,this.props.onEvents));var r=["option","notMerge","replaceMerge","lazyUpdate","showLoading","loadingOption"];E()(w(this.props,r),w(e,r))||this.updateEChartsOption(),E()(e.style,this.props.style)&&E()(e.className,this.props.className)||this.resize()}},t.prototype.componentWillUnmount=function(){this.dispose()},t.prototype.initEchartsInstance=function(){return(0,m.mG)(this,void 0,void 0,(function(){var e=this;return(0,m.Jh)(this,(function(t){return[2,new Promise((function(t){e.echarts.init(e.ele,e.props.theme,e.props.opts),e.getEchartsInstance().on("finished",(function(){var n=e.ele.clientWidth,r=e.ele.clientHeight;e.echarts.dispose(e.ele);var o=(0,m.pi)({width:n,height:r},e.props.opts);t(e.echarts.init(e.ele,e.props.theme,o))}))}))]}))}))},t.prototype.getEchartsInstance=function(){return this.echarts.getInstanceByDom(this.ele)},t.prototype.dispose=function(){if(this.ele){try{(0,x.ZH)(this.ele)}catch(e){console.warn(e)}this.echarts.dispose(this.ele)}},t.prototype.renderNewEcharts=function(){return(0,m.mG)(this,void 0,void 0,(function(){var e,t,n,r,o,i,a=this;return(0,m.Jh)(this,(function(s){switch(s.label){case 0:return e=this.props,t=e.onEvents,n=e.onChartReady,r=e.autoResize,o=void 0===r||r,[4,this.initEchartsInstance()];case 1:return s.sent(),i=this.updateEChartsOption(),this.bindEvents(i,t||{}),S(n)&&n(i),this.ele&&o&&(0,x.ak)(this.ele,(function(){a.resize()})),[2]}}))}))},t.prototype.bindEvents=function(e,t){function n(t,n){j(t)&&S(n)&&e.on(t,(function(t){n(t,e)}))}for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n(r,t[r])},t.prototype.offEvents=function(e,t){if(t)for(var n in t)j(n)&&e.off(n)},t.prototype.updateEChartsOption=function(){var e=this.props,t=e.option,n=e.notMerge,r=void 0!==n&&n,o=e.replaceMerge,i=void 0===o?null:o,a=e.lazyUpdate,s=void 0!==a&&a,c=e.showLoading,u=e.loadingOption,l=void 0===u?null:u,d=this.getEchartsInstance();return d.setOption(t,{notMerge:r,replaceMerge:i,lazyUpdate:s}),c?d.showLoading(l):d.hideLoading(),d},t.prototype.resize=function(){var e=this.getEchartsInstance();if(!this.isInitialResize)try{e.resize({width:"auto",height:"auto"})}catch(e){console.warn(e)}this.isInitialResize=!1},t.prototype.render=function(){var e=this,t=this.props,n=t.style,r=t.className,o=void 0===r?"":r,i=(0,m.pi)({height:300},n);return u.createElement("div",{ref:function(t){e.ele=t},style:i,className:"echarts-for-react ".concat(o)})},t}(u.PureComponent)),N=n(78158);function Z(){return z.apply(this,arguments)}function z(){return(z=a()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,N.N)("/api/knowledge_base/statistics/overall",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(){return I.apply(this,arguments)}function I(){return(I=a()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,N.N)("/api/knowledge_base/statistics/files",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return C.apply(this,arguments)}function C(){return(C=a()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,N.N)("/api/knowledge_base/statistics/access",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var P=n(85893),M=(d.default.RangePicker,function(){var e=(0,u.useState)(!0),t=c()(e,2),n=t[0],r=t[1],i=(0,u.useState)({knowledge_bases:0,files:0,chunks:0}),s=c()(i,2),d=s[0],m=s[1],g=(0,u.useState)([]),x=c()(g,2),w=x[0],S=x[1],j=(0,u.useState)([]),O=c()(j,2),E=O[0],N=O[1],z=(0,u.useState)([]),I=c()(z,2),C=I[0],M=I[1];(0,u.useEffect)((function(){var e=function(){var e=a()(o()().mark((function e(){var t,n,i,a,s;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r(!0),e.next=4,Promise.all([Z(),k(),L({days:30})]);case 4:t=e.sent,n=c()(t,3),i=n[0],a=n[1],s=n[2],i.success&&m(i.data),a.success&&(S(a.data.by_status.map((function(e){return{name:e.status||"未知",value:e.count}}))),N(a.data.by_type.map((function(e){return{name:e.type||"未知",value:e.count}})))),s.success&&M(s.data),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("获取仪表盘数据失败:",e.t0);case 17:return e.prev=17,r(!1),e.finish(17);case 20:case"end":return e.stop()}}),e,null,[[0,14,17,20]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var T=[{title:"知识库总数",value:d.knowledge_bases.toLocaleString()},{title:"文件总数",value:d.files.toLocaleString()},{title:"Chunk总量",value:d.chunks.toLocaleString()}],A={grid:{left:"8%",right:"3%",bottom:"15%",top:"3%",containLabel:!1},xAxis:{type:"category",data:C.map((function(e){return e.date})),boundaryGap:!0,axisLabel:{fontSize:12,color:"#666"},axisLine:{lineStyle:{color:"#e8e8e8"}}},yAxis:{type:"value",splitNumber:7,axisLabel:{fontSize:12,color:"#666"},splitLine:{lineStyle:{color:"#f0f0f0"}}},series:[{data:C.map((function(e){return e.count})),type:"bar",barWidth:"50%",itemStyle:{color:"#1890ff",borderRadius:[2,2,0,0]}}],tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",textStyle:{color:"#fff"}}},R={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:20,top:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"处理状态",type:"pie",radius:["40%","65%"],center:["35%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:4,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!1},data:w}]},W={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:20,top:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"文件类型",type:"pie",radius:["40%","65%"],center:["35%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:4,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!1},data:E}]};return(0,P.jsx)(l._z,{children:(0,P.jsx)(f.Z,{spinning:n,size:"large",children:(0,P.jsxs)("div",{className:"dashboard-container",children:[(0,P.jsx)(p.Z,{gutter:16,className:"stat-cards",children:T.map((function(e,t){return(0,P.jsx)(h.Z,{xs:24,sm:12,md:12,lg:8,children:(0,P.jsxs)(v.Z,{bordered:!1,className:"stat-card",children:[(0,P.jsx)("div",{className:"card-header",children:(0,P.jsx)("span",{children:e.title})}),(0,P.jsx)("div",{className:"card-content",children:(0,P.jsx)("div",{className:"card-main",children:(0,P.jsxs)("div",{className:"card-left",children:[(0,P.jsx)("div",{className:"card-value",children:e.value}),e.rate&&(0,P.jsxs)("div",{className:"card-rates",children:[(0,P.jsxs)("span",{children:[e.rate.text,(0,P.jsxs)("span",{className:e.rate.up?"rate-up":"rate-down",children:[e.rate.value," ",e.rate.up?(0,P.jsx)(y.Z,{}):(0,P.jsx)(b.Z,{})]})]}),e.rate2&&(0,P.jsxs)("span",{children:[e.rate2.text,(0,P.jsxs)("span",{className:e.rate2.up?"rate-up":"rate-down",children:[e.rate2.value," ",e.rate2.up?(0,P.jsx)(y.Z,{}):(0,P.jsx)(b.Z,{})]})]})]})]})})}),(0,P.jsx)("div",{className:"card-footer",children:e.footer&&(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("span",{children:e.footer.text}),(0,P.jsx)("span",{children:e.footer.value})]})})]})},t)}))}),(0,P.jsx)(p.Z,{gutter:16,className:"chart-section",children:(0,P.jsx)(h.Z,{xs:24,md:24,children:(0,P.jsx)(v.Z,{bordered:!1,className:"chart-card",title:"知识库访问量 (最近30天)",children:(0,P.jsx)("div",{className:"chart-content",children:(0,P.jsx)(_,{option:A,style:{height:"100%",width:"100%"}})})})})}),(0,P.jsxs)(p.Z,{gutter:16,className:"bottom-section",children:[(0,P.jsx)(h.Z,{xs:24,md:12,children:(0,P.jsx)(v.Z,{title:"文件处理状态",bordered:!1,className:"category-card",children:(0,P.jsx)("div",{className:"pie-chart",children:(0,P.jsx)(_,{option:R,style:{height:320,width:"100%"}})})})}),(0,P.jsx)(h.Z,{xs:24,md:12,children:(0,P.jsx)(v.Z,{title:"文件类型分布",bordered:!1,className:"category-card",children:(0,P.jsx)("div",{className:"pie-chart",children:(0,P.jsx)(_,{option:W,style:{height:320,width:"100%"}})})})})]})]})})})})},15746:function(e,t,n){var r=n(21584);t.Z=r.Z},71230:function(e,t,n){var r=n(17621);t.Z=r.Z},55168:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SizeSensorId=t.SensorTabIndex=t.SensorClassName=void 0;t.SizeSensorId="size-sensor-id";t.SensorClassName="size-sensor-object";t.SensorTabIndex="-1"},12177:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,n=null;return function(){for(var r=this,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(n),n=setTimeout((function(){e.apply(r,i)}),t)}}},96340:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=1;t.default=function(){return"".concat(n++)}},18587:function(e,t,n){t.ZH=t.ak=void 0;var r=n(12955);t.ak=function(e,t){var n=(0,r.getSensor)(e);return n.bind(t),function(){n.unbind(t)}};t.ZH=function(e){var t=(0,r.getSensor)(e);(0,r.removeSensor)(t)}},12955:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.removeSensor=t.getSensor=t.Sensors=void 0;var r,o=(r=n(96340))&&r.__esModule?r:{default:r},i=n(82578),a=n(55168);var s={};function c(e){e&&s[e]&&delete s[e]}t.Sensors=s;t.getSensor=function(e){var t=e.getAttribute(a.SizeSensorId);if(t&&s[t])return s[t];var n=(0,o.default)();e.setAttribute(a.SizeSensorId,n);var r=(0,i.createSensor)(e,(function(){return c(n)}));return s[n]=r,r};t.removeSensor=function(e){var t=e.element.getAttribute(a.SizeSensorId);e.destroy(),c(t)}},82578:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createSensor=void 0;var r=n(27643),o=n(31743),i="undefined"!=typeof ResizeObserver?o.createSensor:r.createSensor;t.createSensor=i},27643:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createSensor=void 0;var r,o=(r=n(12177))&&r.__esModule?r:{default:r},i=n(55168);t.createSensor=function(e,t){var n=void 0,r=[],a=(0,o.default)((function(){r.forEach((function(t){t(e)}))})),s=function(){n&&n.parentNode&&(n.contentDocument&&n.contentDocument.defaultView.removeEventListener("resize",a),n.parentNode.removeChild(n),e.removeAttribute(i.SizeSensorId),n=void 0,r=[],t&&t())};return{element:e,bind:function(t){n||(n=function(){"static"===getComputedStyle(e).position&&(e.style.position="relative");var t=document.createElement("object");return t.onload=function(){t.contentDocument.defaultView.addEventListener("resize",a),a()},t.style.display="block",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.height="100%",t.style.width="100%",t.style.overflow="hidden",t.style.pointerEvents="none",t.style.zIndex="-1",t.style.opacity="0",t.setAttribute("class",i.SensorClassName),t.setAttribute("tabindex",i.SensorTabIndex),t.type="text/html",e.appendChild(t),t.data="about:blank",t}()),-1===r.indexOf(t)&&r.push(t)},destroy:s,unbind:function(e){var t=r.indexOf(e);-1!==t&&r.splice(t,1),0===r.length&&n&&s()}}}},31743:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createSensor=void 0;var r,o=n(55168),i=(r=n(12177))&&r.__esModule?r:{default:r};t.createSensor=function(e,t){var n=void 0,r=[],a=(0,i.default)((function(){r.forEach((function(t){t(e)}))})),s=function(){n.disconnect(),r=[],n=void 0,e.removeAttribute(o.SizeSensorId),t&&t()};return{element:e,bind:function(t){var o;n||((o=new ResizeObserver(a)).observe(e),a(),n=o),-1===r.indexOf(t)&&r.push(t)},destroy:s,unbind:function(e){var t=r.indexOf(e);-1!==t&&r.splice(t,1),0===r.length&&n&&s()}}}},97582:function(e,t,n){n.d(t,{CR:function(){return l},Jh:function(){return c},Q_:function(){return f},XA:function(){return u},ZT:function(){return o},_T:function(){return a},ev:function(){return d},mG:function(){return s},pi:function(){return i}});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function s(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function c(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,r=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){i=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){i.label=s[1];break}if(6===s[0]&&i.label<o[1]){i.label=o[1],o=s;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(s);break}o[2]&&i.ops.pop(),i.trys.pop();continue}s=t.call(e,i)}catch(e){s=[6,e],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}Object.create;function u(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function l(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function d(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;function f(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}"function"==typeof SuppressedError&&SuppressedError}}]);