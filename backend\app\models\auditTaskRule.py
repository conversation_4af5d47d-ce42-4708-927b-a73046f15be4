from mongoengine import Document, <PERSON>Field, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class AuditTaskRule(Document):
    meta = {
        'collection': 'audit_task_rules'
    }
    _id = ObjectIdField(primary_key=True, default=lambda: ObjectId())
    name = StringField(required=True)  # 规则名称
    type = StringField(required=True)  # 规则类型
    content = StringField()  # 规则内容
    # prompt = StringField()  # 规则内容
    few_shot = ListField()  # 规则内容
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    is_deleted = BooleanField(default=False)  # 软删除标记
    user_id = IntField(required=True)  # 创建者ID
    user_name = StringField()  # 创建者名称

# Pydantic 基础模型
class AuditTaskRuleBase(BaseModel):
    name: str
    content: str
    type: str

# 创建规则请求模型
class AuditTaskRuleCreate(BaseModel):
    name: str
    type: str
    content: str
    is_active: bool = True

# 更新规则请求模型
class AuditTaskRuleUpdate(BaseModel):
    name: Optional[str] = None
    content: Optional[str] = None
    type: Optional[str] = None
    class Config:
        from_attributes = True
        extra = "allow"

# 规则响应模型
class AuditTaskRuleResponse(AuditTaskRuleBase):
    id: str
    created_at: datetime
    updated_at: datetime
    user_id: int
    user_name: Optional[str] = None

    class Config:
        from_attributes = True

# 规则列表响应模型
class AuditTaskRuleListResponse(BaseModel):
    id: str
    name: str
    content: str
    created_at: datetime

    class Config:
        from_attributes = True
