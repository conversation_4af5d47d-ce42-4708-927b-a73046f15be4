from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
from bson.objectid import ObjectId
from app.db.mongodb import db
import traceback
from app.utils.auth import  verify_token
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["access_logs"]
)

@router.get("/access-logs", response_model=Dict[str, Any])
async def get_access_logs(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    user_id: Optional[str] = None,
    path: Optional[str] = None,
    method: Optional[str] = None,
    status_code: Optional[int] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    success: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    """获取访问日志列表，支持分页和多条件查询"""
    skip = (current - 1) * pageSize
    
    # 构建查询条件
    query = {}
    if user_id:
        query["user_id"] = user_id
    if path:
        query["path"] = {"$regex": path, "$options": "i"}
    if method:
        query["method"] = method
    if status_code:
        query["status_code"] = status_code
    if success is not None:
        query["success"] = success
        
    # 处理时间范围
    if start_time or end_time:
        query["timestamp"] = {}
        if start_time:
            try:
                start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                query["timestamp"]["$gte"] = start_datetime
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用ISO格式")
        if end_time:
            try:
                end_datetime = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                query["timestamp"]["$lte"] = end_datetime
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用ISO格式")
    
    # 获取日志数据
    logs = await db["system_logs"].find(
        query
    ).sort("_id", -1).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["system_logs"].count_documents(query)
    
    # 处理ObjectId
    for log in logs:
        if "_id" in log:
            log["id"] = str(log["_id"])
            del log["_id"]

        if "created_at" in log:
            log["created_at"] = log["created_at"].isoformat()
        # 处理嵌套的ObjectId
        if "error" in log and isinstance(log["error"], dict):
            for key, value in log["error"].items():
                if isinstance(value, ObjectId):
                    log["error"][key] = str(value)
    
    return {
        "data": logs,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.get("/performance", response_model=Dict[str, Any])
async def get_performance_stats(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    path: Optional[str] = None,
    method: Optional[str] = None,
    min_total_count: int = Query(0, ge=0, description="最小请求总数"),
    min_slow_count: int = Query(0, ge=0, description="最小慢请求数"),
    min_error_count: int = Query(0, ge=0, description="最小错误数"),
    sort_by: str = Query("total_time", description="排序字段"),
    sort_order: int = Query(-1, description="排序方向: 1升序, -1降序"),
    current_user: dict = Depends(verify_token)
):
    """获取API性能统计数据，支持分页和多条件查询"""
    skip = (current - 1) * pageSize
    
    # 构建查询条件
    query = {}
    if path:
        query["path"] = {"$regex": path, "$options": "i"}
    if method:
        query["method"] = method
    if min_total_count > 0:
        query["total_count"] = {"$gte": min_total_count}
    if min_slow_count > 0:
        query["slow_count"] = {"$gte": min_slow_count}
    if min_error_count > 0:
        query["error_count"] = {"$gte": min_error_count}
    
    # 获取性能统计数据
    stats = await db["performance_stats"].find(
        query
    ).sort(sort_by, sort_order).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["performance_stats"].count_documents(query)
    
    # 处理ObjectId和时间格式
    for stat in stats:
        if "_id" in stat:
            stat["id"] = str(stat["_id"])
            del stat["_id"]
        # 处理时间格式
        for time_field in ["last_slow_request", "last_error"]:
            if time_field in stat and stat[time_field]:
                stat[time_field] = stat[time_field].isoformat()
    
    return {
        "data": stats,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.get("/slow-requests", response_model=Dict[str, Any])
async def get_slow_requests(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    path: Optional[str] = None,
    method: Optional[str] = None,
    user_id: Optional[str] = None,
    min_execution_time: float = Query(1000, ge=0, description="最小执行时间(毫秒)"),
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    """获取慢请求列表，支持分页和多条件查询"""
    skip = (current - 1) * pageSize
    
    # 构建查询条件
    query = {"execution_time": {"$gte": min_execution_time}}
    if path:
        query["path"] = {"$regex": path, "$options": "i"}
    if method:
        query["method"] = method
    if user_id:
        query["user_id"] = user_id
    
    # 处理时间范围
    if start_time or end_time:
        query["timestamp"] = {}
        if start_time:
            try:
                start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                query["timestamp"]["$gte"] = start_datetime
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用ISO格式")
        if end_time:
            try:
                end_datetime = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                query["timestamp"]["$lte"] = end_datetime
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用ISO格式")
    
    # 获取慢请求数据
    slow_requests = await db["slow_requests"].find(
        query
    ).sort("execution_time", -1).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["slow_requests"].count_documents(query)
    
    # 处理ObjectId和时间格式
    for req in slow_requests:
        if "_id" in req:
            req["id"] = str(req["_id"])
            del req["_id"]
        # 处理时间格式
        if "timestamp" in req:
            req["timestamp"] = req["timestamp"].isoformat()
    
    return {
        "data": slow_requests,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.get("/access-logs/{log_id}", response_model=Dict[str, Any])
async def get_access_log(
    log_id: str,
    current_user: dict = Depends(verify_token)
):
    """获取单个访问日志详情"""
    try:
        log = await db["system_logs"].find_one({"_id": ObjectId(log_id)})
        if not log:
            raise HTTPException(status_code=404, detail="访问日志不存在")
        
        # 处理ObjectId
        log["id"] = str(log["_id"])
        del log["_id"]
        

        if "created_at" in log:
            log["created_at"] = log["created_at"].isoformat()
            
        return {
            "data": log,
            "success": True
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(e)
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")

@router.delete("/access-logs/{log_id}")
async def delete_access_log(
    log_id: str,
    current_user: dict = Depends(verify_token)
):
    """删除访问日志"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="仅管理员可以删除日志")
    
    try:
        result = await db["system_logs"].delete_one({"_id": ObjectId(log_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="访问日志不存在")
        return {"success": True, "message": "日志删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除日志失败: {str(e)}")

@router.delete("/access-logs")
async def clear_access_logs(
    days: int = Query(..., description="删除多少天前的日志"),
    current_user: dict = Depends(verify_token)
):
    """清理指定天数前的访问日志"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="仅管理员可以清理日志")
    
    import datetime as dt
    cutoff_date = datetime.utcnow() - dt.timedelta(days=days)
    try:
        result = await db["system_logs"].delete_many({"timestamp": {"$lt": cutoff_date}})
        return {"success": True, "message": f"成功清理 {result.deleted_count} 条日志记录"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")

@router.get("/stats/summary", response_model=Dict[str, Any])
async def get_access_logs_summary(
    current_user: dict = Depends(verify_token)
):
    """获取访问日志统计摘要"""
    try:
        # 获取总访问量
        total_requests = await db["system_logs"].count_documents({})
        
        # 获取今日访问量
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        today_requests = await db["system_logs"].count_documents({"timestamp": {"$gte": today}})
        
        # 获取慢请求总数
        slow_requests = await db["slow_requests"].count_documents({})
        
        # 获取错误请求总数
        error_requests = await db["system_logs"].count_documents({"success": False})
        
        # 获取今日错误请求数
        today_errors = await db["system_logs"].count_documents({
            "timestamp": {"$gte": today},
            "success": False
        })
        
        # 获取平均响应时间
        pipeline = [
            {"$group": {"_id": None, "avg_time": {"$avg": "$response_time"}}}
        ]
        avg_result = await db["system_logs"].aggregate(pipeline).to_list(length=1)
        avg_response_time = avg_result[0]["avg_time"] if avg_result else 0
        
        # 获取最慢的5个API
        pipeline = [
            {"$group": {
                "_id": {"path": "$path", "method": "$method"},
                "avg_time": {"$avg": "$response_time"},
                "count": {"$sum": 1}
            }},
            {"$sort": {"avg_time": -1}},
            {"$limit": 5}
        ]
        slowest_apis = await db["system_logs"].aggregate(pipeline).to_list(length=5)
        
        # 获取最常访问的5个API
        pipeline = [
            {"$group": {
                "_id": {"path": "$path", "method": "$method"},
                "count": {"$sum": 1}
            }},
            {"$sort": {"count": -1}},
            {"$limit": 5}
        ]
        most_visited_apis = await db["system_logs"].aggregate(pipeline).to_list(length=5)
        
        # 格式化结果
        formatted_slowest_apis = [
            {"path": api["_id"]["path"], "method": api["_id"]["method"], "avg_time": api["avg_time"], "count": api["count"]}
            for api in slowest_apis
        ]
        
        formatted_most_visited_apis = [
            {"path": api["_id"]["path"], "method": api["_id"]["method"], "count": api["count"]}
            for api in most_visited_apis
        ]
        
        return {
            "success": True,
            "data": {
                "total_requests": total_requests,
                "today_requests": today_requests,
                "slow_requests": slow_requests,
                "error_requests": error_requests,
                "today_errors": today_errors,
                "avg_response_time": avg_response_time,
                "slowest_apis": formatted_slowest_apis,
                "most_visited_apis": formatted_most_visited_apis
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计摘要失败: {str(e)}")
