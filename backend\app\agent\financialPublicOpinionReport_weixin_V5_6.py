#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""

import concurrent.futures
import functools
import json
import logging
import os
import platform
import signal
import threading
import traceback
import uuid
import io
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, TypedDict
from io import BytesIO
import markdown
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
import tempfile

import pandas as pd
from elasticsearch import Elasticsearch
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from minio import Minio
from mongoengine import Document, StringField, DateTimeField, ListField, ObjectIdField, connect, disconnect
from bson import ObjectId
import re
import time
import schedule

# 直接定义MediaInsightsReport模型而不是导入
class MediaInsightsReport(Document):
    meta = {
        'collection': 'media_insights_reports'
    }
    _id = ObjectIdField(primary_key=True, default=lambda: ObjectId())
    title = StringField(required=True)  # 报告标题
    content = StringField(required=True)  # Markdown格式内容
    storage_path = StringField(required=True)  # 文件保存路径
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    report_type = StringField(required=True)  # 报告类型
    start_time = DateTimeField(required=True)  # 数据开始时间
    end_time = DateTimeField(required=True)  # 数据结束时间
    media_ids = ListField(StringField(), default=list)  # 媒体ID列表

# 自定义超时异常
class TimeoutException(Exception):
    pass

# 跨平台的超时函数装饰器
def timeout(seconds):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时间
            start_time = datetime.now()
            func_name = func.__name__
            logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")

            # 使用线程池执行函数
            with ThreadPoolExecutor(max_workers=1) as executor:
                # 提交任务到线程池
                future = executor.submit(func, *args, **kwargs)
                try:
                    # 等待任务完成，超时则抛出异常
                    result = future.result(timeout=seconds)
                    # 记录执行时间
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
                    return result
                except concurrent.futures.TimeoutError:
                    # 超时后取消任务（注意：这只是设置取消标志，线程会继续执行）
                    future.cancel()
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
                    raise TimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
        return wrapper
    return decorator

# 检测是否为Unix平台（支持SIGALRM）
IS_UNIX = platform.system() in ('Linux', 'Darwin') and os.name == 'posix'

if IS_UNIX:
    # 在Unix平台上使用signal.SIGALRM实现超时
    @contextmanager
    def timeout_context(seconds):
        def handle_timeout(signum, frame):
            raise TimeoutException(f"操作超时（{seconds}秒）")

        # 设置信号处理器
        original_handler = signal.getsignal(signal.SIGALRM)
        signal.signal(signal.SIGALRM, handle_timeout)

        try:
            # 设置定时器
            signal.alarm(seconds)
            yield
        finally:
            # 取消定时器
            signal.alarm(0)
            # 恢复原始信号处理器
            signal.signal(signal.SIGALRM, original_handler)
else:
    # 在非Unix平台上使用线程池实现超时
    @contextmanager
    def timeout_context(seconds):
        def do_nothing():
            pass  # 占位函数，用于超时测试

        # 使用线程池和Future
        with ThreadPoolExecutor(max_workers=1) as executor:
            # 提交一个简单的任务，仅用于测试超时
            future = executor.submit(do_nothing)

            try:
                # 尝试等待任务完成（但我们实际上只是在测试超时机制）
                future.result(timeout=0.001)  # 确保这会立即返回
                # 现在让主任务运行
                yield
                # 注意：我们不能在这里检查是否超时，因为yield可能会长时间运行
            except concurrent.futures.TimeoutError:
                # 这不应该发生，因为do_nothing应该立即返回
                logger.warning("初始化超时上下文出错")
                yield

        # 超时上下文结束后，原任务应该已经完成或被取消

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_INDEX = "pro_mcp_data_weixin"



# MongoDB配置
MONGODB_USER="wiseAgent001"
MONGODB_PASSWORD="1qaz@WSX"
DATABASE_NAME="wiseagent"
MONGODB_AUTH_SOURCE="wiseagent"
MONGODB_HOST="************"
MONGODB_PORT=37017


# MinIO配置
MINIO_ENDPOINT="**************:9002"
MINIO_ACCESS_KEY="qFHq6y3pNfboA7YBNynE"
MINIO_SECRET_KEY="P5lTULcF2IEtX47mkdmfuDpQVkYJEeL2AKEhvDRr"
MINIO_BUCKET="wiseagentfiles"

# 大模型参数

# LLM_API_BASE = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1"
# OPENAI_API_KEY = "ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ=="
# LLM_MODEL = "Qwen3-32B"





# LLM_MODEL="Qwen/Qwen2.5-72B-Instruct-128K"
# LLM_API_BASE="https://api.siliconflow.cn/v1"
# OPENAI_API_KEY="sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn"




LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
# OPENAI_API_KEY="sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn"

# 公众号ID列表
BIZ_LIST = [
    "MzA4MDY1NTUyMg==",
    "MzA5OTY5MzM4Ng==",
    "Mzg2Mjg1NTg3NA==",
    "Mzg4NTEwMzA5NQ==",
    "Mzg5MjU4MDkyMw==",
    "MzI4NTU0NDE4Mw==",
    "MzI5MzQxOTI0MQ==",
    "MzIwMDI3NjM2Mg==",
    "MzIzMjc3NTYyNQ==",
    "Mzk0NjUwMDIxOQ==",
    "MzkxMDYyNzExMA==",
    "MzkzNTYzMDYxMg==",
    "MzU3MDMwODc2MA==",
    "MzU4NzcwNDcxOA==",
    "MzU4ODM4NzI5Nw==",
    "MzU5MzkzMTY3Mg==",
    "MzUxMDk5NDgwNQ==",
    "MzUxMDkyMzA4Mw==",
    "MzUzMzEyODIyMA==",
    "MzUzNDcxMDgzNg==",
    "MzA3MTIzNDcwMg==",
    "MzA3NjU1NTQwMA==",
    "MzA4MzY2ODYwMw==",
    "MzAwNzMxODYyNg==",
    "Mzg4NDU2MDM3Mw==",
    "MzI1NzAwODc3Nw==",
    "MzU3MDMwODc2MA==",
    "MzU3NTYyNTIyMQ==",
    "MzUzMzYwODI2MA=="
]

# 初始化大模型
llm = ChatOpenAI(
    temperature=0,
    model=LLM_MODEL,
    openai_api_base=LLM_API_BASE,
    api_key=OPENAI_API_KEY,
    request_timeout=120  # 设置120秒的请求超时时间
)

MAX_DAYS = 3

def safe_llm_invoke(messages, timeout_seconds=180, default_response=None, description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
    logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
    try:
        # 使用timeout装饰器包装llm.invoke调用
        @timeout(timeout_seconds)
        def invoke_with_timeout(msgs):
            return llm.invoke(msgs)

        # 调用包装后的函数
        result = invoke_with_timeout(messages)
        logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
        return result
    except TimeoutException as e:
        logger.error(f"{description}超时: {str(e)}")
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        # 如果没有提供默认值，构造一个更有信息量的错误响应
        error_msg = f"{description}超时，请检查您的查询或稍后再试。"
        logger.warning(f"生成默认错误响应: {error_msg}")
        return AIMessage(content=error_msg)
    except Exception as e:
        logger.error(f"{description}失败: {str(e)}")
        traceback.print_exc()
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        # 如果没有提供默认值，构造一个错误响应
        error_msg = f"{description}失败: {str(e)}"
        logger.warning(f"生成默认错误响应: {error_msg}")
        return AIMessage(content=error_msg)

def get_es_scroll_data_batched(index, query_body, batch_size=1000, es_host=None, es_port=None, max_retries=3):
    """滚动查询ES数据，并以批次方式返回"""
    if not es_host:
        es_host = TARGET_ES_HOST
    if not es_port:
        es_port = TARGET_ES_PORT

    retry_count = 0

    while retry_count < max_retries:
        es = None
        sid = None
        try:
            # 创建ES连接
            es = Elasticsearch([f"{es_host}:{es_port}"], timeout=60)
            
            # 检查ES连接
            if not es.ping():
                raise ConnectionError("无法连接到 Elasticsearch")

            # 初始搜索
            result = es.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
            sid = result['_scroll_id']
            scroll_size = result['hits']['total']['value']
            logger.info(f"索引 {index} 总数据量: {scroll_size}")

            # 如果有结果，返回第一批数据
            if len(result['hits']['hits']) > 0:
                yield result['hits']['hits']

            # 继续滚动直到没有更多数据
            scroll_count = len(result['hits']['hits'])
            while scroll_count > 0:
                try:
                    result = es.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
                    batch_data = result['hits']['hits']
                    scroll_count = len(batch_data)
                    if scroll_count == 0:
                        break
                    yield batch_data
                except Exception as scroll_error:
                    logger.error(f"滚动查询时出错: {str(scroll_error)}")
                    break

            # 如果成功完成，跳出重试循环
            break

        except Exception as e:
            retry_count += 1
            logger.error(f"获取索引 {index} 数据时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
            if retry_count < max_retries:
                logger.info(f"等待 5 秒后重试...")
                time.sleep(5)
            else:
                logger.error(f"达到最大重试次数，放弃获取数据")
                raise
        finally:
            if sid and es:
                try:
                    es.clear_scroll(scroll_id=sid)
                except:
                    pass

            if es:
                try:
                    es.close()
                except:
                    pass
        
    logger.info(f"索引 {index} 查询完成")

def build_query_body(start_time, end_time, biz=None):
    """构建ES查询体"""
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "publishtime": {
                                "gte": start_time,
                                "lte": end_time
                             }
                        }
                    }
                ]
            }
        },
        "sort": [{"publishtime": "desc"}]  # 按createTimeES降序排序
    }

    # 如果指定了biz参数，则添加biz过滤条件
    if biz:
        # 如果biz是字符串，则使用term查询
        if isinstance(biz, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "site_id": {
                        "value": biz
                    }
                }
            })
        # 如果biz是列表，则使用terms查询
        elif isinstance(biz, list) and len(biz) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "site_id": biz
                }
            })

    return body

def get_latest_report_by_account_with_timerange(start_time, end_time, es_index=None, es_host=None, es_port=None, max_retries=3):
    """获取每个公众号在指定时间范围内的最新一篇报告"""
    # 如果未提供ES配置，使用默认配置
    if not es_index:
        es_index = TARGET_ES_INDEX
    if not es_host:
        es_host = TARGET_ES_HOST
    if not es_port:
        es_port = TARGET_ES_PORT

    # 格式化为字符串
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")

    latest_reports = {}
    total_count = 0

    for biz in BIZ_LIST:
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 构建查询体
                query_body = build_query_body(start_time_str, end_time_str, biz)
                # query_body["size"] = 5  # 获取多篇，以便有多个选择

                # 获取记录
                es = Elasticsearch([f"{es_host}:{es_port}"], timeout=60)
                result = es.search(index=es_index, body=query_body, request_timeout=60)
                valid_report_found = False

                # 遍历结果，找到最新的报告
                for hit in result['hits']['hits']:
                    total_count += 1
                    doc = hit['_source']
                    publish_time = doc.get('publishtime', '')

                    # 找到有效报告
                    report = {
                        'title': doc.get('title', '无标题'),
                        'content': doc.get('content', '无内容'),
                        'author': doc.get('new_author', '未知作者'),
                        'site_name': doc.get('source', '未知公众号'),
                        'publishtime': publish_time,
                        'authorViewpoint': doc.get('authorViewpoint', ''),
                        'characterEntity': doc.get('characterEntity', []),
                        'institutionalEntities': doc.get('institutionalEntities', []),
                        'locationEntity': doc.get('locationEntity', []),
                        'eventInfo': doc.get('eventInfo', []),
                        'summaryFacts': doc.get('summaryFacts', []),
                        'summary': doc.get('summary', '')
                    }

                    # 应用事件过滤
                    report = filter_events_by_time(report)

                    latest_reports[biz] = report
                    logger.info(f"获取到公众号 {doc.get('source', biz)} 有效文章, 发布时间: {publish_time}, 标题: {doc.get('title', '无标题')}")
                    valid_report_found = True
                    break

                if not valid_report_found:
                    logger.warning(f"未找到公众号 {biz} 的有效报告")

                # 如果成功，跳出重试循环
                break

            except Exception as e:
                retry_count += 1
                logger.error(f"获取公众号 {biz} 最新报告时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
                if retry_count < max_retries:
                    logger.info(f"等待 5 秒后重试...")
                    time.sleep(5)
                else:
                    logger.error(f"达到最大重试次数，跳过公众号 {biz}")
            finally:
                if 'es' in locals():
                    try:
                        es.close()
                    except:
                        pass

    logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告，总共查询了 {total_count} 篇报告")
    return latest_reports

def is_event_in_time_range(publish_time_str, start_time_str, end_time_str):
    """检查文章发布时间是否在指定的时间范围内
    
    Args:
        publish_time_str (str): 文章发布时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
        start_time_str (str): 查询开始时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
        end_time_str (str): 查询结束时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
        
    Returns:
        bool: 如果发布时间在指定范围内返回 True，否则返回 False
    """
    try:
        # 解析时间字符串
        try:
            # 尝试标准格式 "YYYY-MM-DD HH:MM:SS"
            publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试日期格式 "YYYY-MM-DD"
                publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d")
            except ValueError:
                try:
                    # 尝试带毫秒的格式
                    publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    # 如果无法解析，返回False
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
                    return False

        # 解析开始和结束时间
        start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

        # 检查发布时间是否在范围内
        if publish_time < start_time:
            logger.info(f"发布时间 {publish_time_str} 早于查询开始时间 {start_time_str}")
            return False

        if publish_time > end_time:
            logger.info(f"发布时间 {publish_time_str} 晚于查询结束时间 {end_time_str}")
            return False

        return True
    except Exception as e:
        logger.error(f"时间范围验证错误: {str(e)}")
        return False

def filter_events_by_time(report):
    """过滤报告中的事件信息"""
    if not report.get('eventInfo'):
        return report

    filtered_events = []
    for event in report['eventInfo']:
        # 检查事件是否包含时间信息
        event_time = None
        if isinstance(event, dict):
            event_time = event.get('time') or event.get('eventTime') or event.get('timestamp')

        # 如果没有时间信息，保留事件（保守策略）
        if not event_time:
            filtered_events.append(event)
            continue

        # 检查事件时间是否在范围内
        if is_event_in_time_range(event_time, report['start_time'], report['end_time']):
            filtered_events.append(event)

    report['eventInfo'] = filtered_events
    return report

# 在 ANALYSIS_CONFIG 中添加发布时间过滤配置
ANALYSIS_CONFIG = {
    "max_days": 3,  # 最大获取天数
    "min_word_count": 500,  # 最小文章字数
    "max_retries": 3,  # 分析失败重试次数
    "timeout": {
        "single_analysis": 180,  # 单篇分析超时时间
        "overall_analysis": 180,  # 综合分析超时时间
    },
    "event_filter": {
        "max_event_age_days": 7,  # 事件最大年龄（天）
        "min_event_age_days": 0,  # 事件最小年龄（天）
        "exclude_future_events": True  # 是否排除未来事件
    },
    "publish_filter": {
        "max_publish_age_days": 7,  # 报告最大发布年龄（天）
        "min_publish_age_days": 0,  # 报告最小发布年龄（天）
        "exclude_future_publish": True  # 是否排除未来发布日期
    }
}

# 添加发布时间过滤函数
def is_publish_time_valid(publish_time_str):
    """检查报告发布时间是否在配置的有效范围内"""
    try:
        # 尝试解析不同格式的时间字符串
        try:
            # 尝试标准格式 "YYYY-MM-DD HH:MM:SS"
            publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试日期格式 "YYYY-MM-DD"
                publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d")
            except ValueError:
                try:
                    # 尝试带毫秒的格式
                    publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    # 如果无法解析，返回False
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
                    return False

        current_time = datetime.now()

        # 计算发布年龄（天数）
        publish_age_days = (current_time - publish_time).days

        # 检查是否在配置的时间范围内
        if publish_age_days < ANALYSIS_CONFIG["publish_filter"]["min_publish_age_days"]:
            logger.info(f"报告发布时间太新: {publish_time_str}, 年龄: {publish_age_days}天")
            return False

        if publish_age_days > ANALYSIS_CONFIG["publish_filter"]["max_publish_age_days"]:
            logger.info(f"报告发布时间太旧: {publish_time_str}, 年龄: {publish_age_days}天")
            return False

        # 检查是否为未来发布
        if ANALYSIS_CONFIG["publish_filter"]["exclude_future_publish"] and publish_time > current_time:
            logger.info(f"报告发布时间在未来: {publish_time_str}")
            return False

        return True
    except Exception as e:
        logger.error(f"发布时间验证错误: {str(e)}")
        return False

def analyze_report_with_reasoning(title, content, account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
    logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
    # 步骤1: 初始提示
    system_msg = SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")

    # 截断超长内容，但保留足够的内容供分析
    max_content_length = 10000  # 增加允许的内容长度
    content_to_analyze = content[:max_content_length] if content and len(content) > max_content_length else content
    
    human_msg = HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content_to_analyze}
""")

    # 执行初始思考
    logger.info(f"向LLM发送初始分析请求，内容长度: {len(content_to_analyze)}")
    messages = [system_msg, human_msg]

    # 使用安全的LLM调用，增加超时时间
    default_analysis = AIMessage(content="""
{
  "summary": "由于分析时间限制，无法完成详细分析。以下是基于报告标题的初步判断。",
  "core_viewpoints": ["根据报告标题推测可能讨论了当日市场情况和投资机会"],
  "supporting_evidence": ["无法在规定时间内提取完整证据"],
  "key_insights": "报告可能分析了市场趋势和投资机会",
  "market_impact": "可能对投资决策有一定参考价值",
  "reasoning_chain": ["由于分析时间限制，无法提供完整推理过程"]
}
""")
    initial_analysis = safe_llm_invoke(
        messages,
        timeout_seconds=240,  # 增加超时时间到4分钟
        default_response=default_analysis,
        description=f"'{account_name}'报告初始分析"
    )

    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")

    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]

        # 使用安全的LLM调用，提供更好的默认结果
        default_result = AIMessage(content="""
{
  "summary": "这篇报告来自'""" + account_name + """'，标题为'""" + title + """'。由于处理时间限制，无法提供详细分析。根据标题推测，报告可能讨论了市场走势和投资机会。",
  "core_viewpoints": ["基于标题推测，报告可能讨论了市场走势", "可能包含投资建议或市场观点", "可能分析了当前市场机会"],
  "supporting_evidence": ["由于分析时间限制，无法提取详细证据"],
  "key_insights": "报告可能提供了对当前市场环境的分析和投资建议，但需要完整阅读报告以获取准确观点",
  "market_impact": "可能对投资者决策有一定参考价值",
  "reasoning_chain": ["因内容过长或复杂，分析未能在规定时间内完成", "建议相关人员直接查阅原文以获取完整信息"]
}
""")
        result = safe_llm_invoke(
            messages2,
            timeout_seconds=90,  # 增加到90秒
            default_response=default_result,
            description=f"'{account_name}'报告结构化分析"
        )
        content = result.content

        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            try:
                analysis = json.loads(json_str)
                # 保存推理过程
                analysis["reasoning_process"] = initial_analysis.content
                logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints', []))} 条核心观点")
                return analysis
            except json.JSONDecodeError as je:
                logger.error(f"JSON解析错误: {str(je)}")
                # 尝试修复JSON并重新解析
                try:
                    import re
                    # 尝试移除JSON中的非法字符
                    cleaned_json = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', json_str)
                    analysis = json.loads(cleaned_json)
                    analysis["reasoning_process"] = "JSON解析出错，已尝试修复"
                    return analysis
                except:
                    # 如果仍然失败，返回默认分析结果
                    return {
                        "summary": f"这篇来自'{account_name}'的报告标题为'{title}'。分析过程遇到技术问题，无法提供详细分析。",
                        "core_viewpoints": ["无法提取核心观点，请查看原文"],
                        "supporting_evidence": ["无法提取支持证据"],
                        "key_insights": "无法提取关键洞见，建议直接查看原文",
                        "market_impact": f"无法分析市场影响，请参考原文",
                        "reasoning_chain": ["JSON解析错误，无法提供分析"]
                    }
        else:
            logger.error("无法找到JSON内容")
            return {
                "summary": f"这篇来自'{account_name}'的报告标题为'{title}'。由于内容格式问题，无法提供完整分析。",
                "core_viewpoints": ["需要查看原文以了解核心观点"],
                "supporting_evidence": ["无法自动提取支持证据"],
                "key_insights": f"建议查看原文以获取'{account_name}'的完整分析",
                "market_impact": "无法自动分析市场影响",
                "reasoning_chain": ["结果格式问题，无法完成分析"]
            }
    except Exception as e:
        logger.error(f"分析报告内容时出错: {str(e)}")
        return {
            "summary": f"这篇来自'{account_name}'的报告标题为'{title}'。分析过程出现技术错误。",
            "core_viewpoints": [f"建议查看原文以了解'{account_name}'的真实观点"],
            "supporting_evidence": ["技术原因无法提取"],
            "key_insights": f"由于技术原因无法提取，请参考原文",
            "market_impact": "无法评估",
            "reasoning_chain": [f"分析错误: {str(e)}"]
        }

def analyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
    logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
    # 提取所有报告的观点和数据
    all_viewpoints = []
    all_entities = set()
    all_institutions = set()
    all_events = set()
    report_summaries = []

    for biz, report in reports_data.items():
        # 收集观点
        if 'analysis' in report and report['analysis'].get('core_viewpoints'):
            for viewpoint in report['analysis']['core_viewpoints']:
                all_viewpoints.append({
                    'site_name': report['site_name'],
                    'viewpoint': viewpoint
                })
        if report.get('authorViewpoint'):
            all_viewpoints.append({
                'site_name': report['site_name'],
                'viewpoint': report['authorViewpoint']
            })

        # 收集实体和事件
        all_entities.update(report.get('characterEntity', []))
        all_institutions.update(report.get('institutionalEntities', []))
        all_events.update(report.get('eventInfo', []))

        # 收集摘要
        if report.get('summary'):
            report_summaries.append({
                'site_name': report['site_name'],
                'summary': report['summary']
            })

    logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")

    # 构建数据摘要
    data_summary = {
        "total_reports": len(reports_data),
        "unique_entities": list(all_entities)[:10],  # 限制数量
        "unique_institutions": list(all_institutions)[:10],
        "unique_events": list(all_events)[:10],
        "viewpoint_count": len(all_viewpoints)
    }

    # 观点文本
    viewpoints_text = "\n\n".join([f"{item['site_name']}: {item['viewpoint']}" for item in all_viewpoints])
    logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")

    # 步骤1: 初始分析
    logger.info("开始向LLM发送综合分析请求...")
    system_msg = SystemMessage(content="""你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容需要按照以下维度进行详细分析：

1. 整体判断
   - 综合各机构观点的整体市场判断

2. 市场观点共识与分歧
   - 观点共识（哪些机构持有相同观点）
   - 观点分歧（哪些机构观点不同）

3. 主要风险与机会
   - 主要风险点（注明提出风险的机构）
   - 主要机会点（注明提出机会的机构）

4. 宏观经济预期
   - 各机构对宏观经济的预判
   - 主要依据和论据

5. 宏观政策预期
   - 各机构对政策走向的判断
   - 政策预期的主要依据

6. 利率走势预测
   - 各机构对利率走势的预测
   - 预测的主要依据

7. 投资策略建议
   - 各机构的投资策略建议
   - 投资机会分析

请用结构化JSON格式返回，确保每个观点都有对应的来源机构。

【重要】：请严格遵循以下规则：
1. 不要在观点内容中直接写入来源信息，如"（来源：机构A）"或"根据机构A的观点"等
2. 来源信息必须放在单独的字段中
3. 观点内容应该是纯粹的观点，不包含任何来源标注
4. 系统会自动根据来源字段为每个观点添加来源标注""")

    human_msg = HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度必须包括：
1. 整体判断
2. 市场观点共识与分歧
3. 主要风险与机会
4. 宏观经济预期
5. 宏观政策预期
6. 利率走势预测
7. 投资策略建议

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])}
- 主要事件: {', '.join(data_summary['unique_events'][:5])}
- 观点数量: {data_summary['viewpoint_count']}

【严格遵守】：
1. 每个观点必须有对应的来源机构
2. 观点内容中绝对不要包含来源信息，不要写"（来源：机构A）"或"根据机构A的观点"等字样
3. 来源信息必须放在单独的字段中
4. 系统会自动为每个观点添加来源标注，所以不需要在内容中重复添加
5. 如果你在内容中添加了来源信息，会导致最终报告中出现重复的来源标注

以下是各家机构的观点:
{viewpoints_text}
""")

    # 执行初始思考 - 使用安全的LLM调用
    messages = [system_msg, human_msg]
    default_analysis = AIMessage(content="无法在规定时间内完成综合分析。")
    initial_analysis = safe_llm_invoke(
        messages,
        timeout_seconds=180,
        default_response=default_analysis,
        description="多机构报告综合分析"
    )

    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化综合分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构，并按照要求的维度进行详细分析。")

    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，必须包含以下字段:

1. overall_summary: 整体判断（300字以内，提及主要观点来源）
2. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
3. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
4. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
5. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）
6. macro_economic_outlook: 宏观经济预期（数组格式，每项包含观点内容和来源机构）
7. policy_expectations: 宏观政策预期（数组格式，每项包含观点内容和来源机构）
8. interest_rate_forecast: 利率走势预测（数组格式，每项包含观点内容和来源机构）
9. investment_recommendations: 投资策略建议（数组格式，每项包含观点内容和来源机构）

对于每个数组项，请使用以下格式：
{
  "content": "观点内容",
  "sources": ["机构A", "机构B"]
}

【严格遵守】：
1. "content"字段中绝对不要包含来源信息
2. 不要在content中写"（来源：机构A）"或"根据机构A的观点"等字样
3. 来源信息只能放在"sources"字段中
4. 系统会自动根据sources字段添加来源信息
5. 如果你在content中添加了来源信息，会导致最终报告中出现重复的来源标注，如"观点内容（来源：机构A）（来源：机构A）"
6. 确保每个观点的content字段简洁明了，只包含观点本身""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]

        # 使用安全的LLM调用
        default_result = AIMessage(content="""
{
  "macro_economic_outlook": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "policy_expectations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "interest_rate_forecast": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "investment_recommendations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "consensus_points": [{"content": "无法识别共识观点", "sources": []}],
  "divergent_points": [{"content": "无法识别分歧观点", "sources": []}],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": [{"content": "无法识别市场风险", "sources": []}],
  "market_opportunities": [{"content": "无法识别市场机会", "sources": []}]
}
""")
        result = safe_llm_invoke(
            messages2,
            timeout_seconds=120,
            default_response=default_result,
            description="生成结构化综合分析"
        )
        content = result.content

        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            # 保存推理过程
            analysis["reasoning_process"] = initial_analysis.content
            logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points', []))} 条共识观点, {len(analysis.get('divergent_points', []))} 条分歧观点")
            return analysis
        else:
            logger.error("无法找到综合分析的JSON内容")
            return {
                "macro_economic_outlook": [{"content": "无法解析综合观点", "sources": []}],
                "policy_expectations": [{"content": "无法解析综合观点", "sources": []}],
                "interest_rate_forecast": [{"content": "无法解析综合观点", "sources": []}],
                "investment_recommendations": [{"content": "无法解析综合观点", "sources": []}],
                "consensus_points": [],
                "divergent_points": [],
                "overall_summary": "无法生成综合分析",
                "market_risks": [],
                "market_opportunities": []
            }
    except Exception as e:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
        return {
            "macro_economic_outlook": [{"content": "分析过程出错", "sources": []}],
            "policy_expectations": [{"content": "分析过程出错", "sources": []}],
            "interest_rate_forecast": [{"content": "分析过程出错", "sources": []}],
            "investment_recommendations": [{"content": "分析过程出错", "sources": []}],
            "consensus_points": [],
            "divergent_points": [],
            "overall_summary": "分析过程出错",
            "market_risks": [],
            "market_opportunities": []
        }

def generate_executive_summary(overall_analysis, reports_count):
    """生成执行摘要，只保留市场观点共识与分歧、主要风险与机会、整体判断三个部分"""
    logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")

    # 格式化复杂结构为字符串，避免类型错误
    def format_complex_item(items, max_items=3):
        if not items:
            return '无数据'

        if isinstance(items, str):
            return items

        formatted_points = []
        count = 0

        for item in items[:max_items]:
            if isinstance(item, dict):
                content = item.get('content', '')
                sources = item.get('sources', [])
                
                # 检查并移除内容中可能已经包含的来源信息
                source_pattern = r'（来源：.*?）'
                content = re.sub(source_pattern, '', content).strip()
                
                if isinstance(sources, list) and sources:
                    sources_str = '、'.join(sources)
                    formatted_points.append(f"{content}（来源：{sources_str}）")
                else:
                    formatted_points.append(content)
            elif isinstance(item, str):
                # 对于字符串类型，也检查并移除可能包含的来源信息
                source_pattern = r'（来源：.*?）'
                item = re.sub(source_pattern, '', item).strip()
                formatted_points.append(item)

            count += 1
            if count >= max_items:
                break

        if len(items) > max_items:
            formatted_points.append("等")

        return '；'.join(formatted_points)

    system_msg = SystemMessage(content="""你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
摘要应只包含以下三个关键维度的分析：市场观点共识与分歧、主要风险与机会、整体判断。
使用专业但易于理解的语言，避免冗长解释。

【重要提示】：
1. 注意我提供的输入中，每个观点后面已经包含了来源信息，格式为"观点内容（来源：机构A、机构B）"
2. 请不要重复添加来源信息，直接使用我提供的带有来源的观点
3. 如果你再次添加来源，会导致最终报告中出现重复的来源标注，如"观点内容（来源：机构A）（来源：机构A）"
4. 保持原有的来源标注格式，不要修改或重复添加""")

    try:
        # 安全地获取各个字段值
        consensus = format_complex_item(overall_analysis.get('consensus_points', []))
        divergent = format_complex_item(overall_analysis.get('divergent_points', []))
        risks = format_complex_item(overall_analysis.get('market_risks', []))
        opportunities = format_complex_item(overall_analysis.get('market_opportunities', []))

        # 获取整体市场观点摘要
        overall_summary = overall_analysis.get('overall_summary', '无法获取整体市场观点摘要')

        human_msg = HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，只包含以下三个维度：

1. 整体判断：
   请根据所有分析内容，提供一个整体的市场判断。

2. 市场观点共识与分歧：
   - 共识观点：{consensus}
   - 分歧观点：{divergent}

3. 主要风险与机会：
   - 主要风险：{risks}
   - 主要机会：{opportunities}

请生成一份不超过400字的高管层执行摘要。注意：上面提供的观点中已经包含了来源信息，请直接使用，不要重复添加来源标注。
摘要需要结构清晰，重点突出，便于高管快速把握市场关键信息。""")

        logger.info("向LLM发送执行摘要生成请求...")
        messages = [system_msg, human_msg]

        # 使用安全的LLM调用
        default_summary = "无法在规定时间内生成执行摘要。请参考核心观点总结部分了解详细情况。"
        result = safe_llm_invoke(
            messages,
            timeout_seconds=180,
            default_response=default_summary,
            description="生成执行摘要"
        )
        logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
        return result.content
    except Exception as e:
        logger.error(f"生成执行摘要时出错: {str(e)}")
        traceback.print_exc()
        return "无法生成执行摘要。错误原因：" + str(e)

def generate_report_with_storm(fetch_start_time=None, fetch_end_time=None, es_index=None, es_host=None, es_port=None):
    """使用STORM方法生成完整的分析报告"""
    logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
    
    # 解析时间字符串为datetime对象，如果提供了的话
    start_date = None
    end_date = None
    if fetch_start_time and fetch_end_time:
        try:
            if isinstance(fetch_start_time, str):
                start_date = datetime.strptime(fetch_start_time, "%Y-%m-%d %H:%M:%S")
            else:
                start_date = fetch_start_time
                
            if isinstance(fetch_end_time, str):
                end_date = datetime.strptime(fetch_end_time, "%Y-%m-%d %H:%M:%S")
            else:
                end_date = fetch_end_time
                
            # 计算天数差
            days_diff = (end_date - start_date).days
            if days_diff <= 0:
                days_diff = 1  # 确保至少为1天
                
            logger.info(f"使用指定的时间范围: {start_date} 至 {end_date}, 共 {days_diff} 天")
        except Exception as e:
            logger.error(f"解析时间参数出错: {str(e)}，将使用默认配置")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=ANALYSIS_CONFIG.get("max_days", MAX_DAYS))
            days_diff = ANALYSIS_CONFIG.get("max_days", MAX_DAYS)
    else:
        # 使用默认配置
        end_date = datetime.now()
        start_date = end_date - timedelta(days=ANALYSIS_CONFIG.get("max_days", MAX_DAYS))
        days_diff = ANALYSIS_CONFIG.get("max_days", MAX_DAYS)
        logger.info(f"未提供时间参数，使用默认配置: {start_date} 至 {end_date}, 共 {days_diff} 天")
    
    # 1. 获取每个公众号在指定时间范围内的最新报告
    logger.info(f"第1步: 获取每个公众号在指定时间范围内的最新报告...")
    
    latest_reports = get_latest_report_by_account_with_timerange(
        start_time=start_date,
        end_time=end_date,
        es_index=es_index,
        es_host=es_host,
        es_port=es_port
    )
        
    logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
    
    # 检查获取的数据量，如果为0则提前结束流程
    if len(latest_reports) == 0:
        logger.warning(f"在指定时间范围 {start_date} 至 {end_date} 内未获取到任何报告数据，流程结束")
        # 格式化时间范围字符串
        time_range_str = f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"
        
        return {
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "report_title": f"金融媒体洞察分析报告 - {end_date.strftime('%Y-%m-%d')}",
            "executive_summary": f"在指定时间范围（{time_range_str}）内未获取到任何报告数据，无法生成分析报告。",
            "individual_reports": {},
            "overall_analysis": {},
            "metadata": {
                "reports_count": 0,
                "generation_method": "STORM结构化推理",
                "time_range": time_range_str
            },
            "errors": ["在指定时间范围内未获取到任何报告数据"]
        }

    # 2. 并行分析每篇报告
    errors = []
    total = len(latest_reports)
    logger.info(f"第2步: 开始并行分析 {total} 个公众号报告...")

    def analyze_single_report(biz_report_tuple):
        biz, report = biz_report_tuple
        try:
            # 检查报告内容长度，过滤掉过短的内容
            content = report['content']
            if content and len(content) < ANALYSIS_CONFIG.get("min_word_count", 500):
                return biz, None, f"{report['site_name']} 内容过短，不进行分析"

            logger.info(f"开始分析公众号: {report['site_name']}")
            analysis = analyze_report_with_reasoning(report['title'], content, report['site_name'])
            logger.info(f"完成公众号分析: {report['site_name']}")
            return biz, analysis, None
        except Exception as e:
            error_msg = f"{report['site_name']} 分析失败: {str(e)}"
            logger.error(error_msg)
            return biz, None, error_msg

    # 使用线程池并行处理，根据配置设置最大线程数
    max_workers = max(1, min(10, total))  # 确保max_workers至少为1，最大为10
    logger.info(f"使用线程池，工作线程数: {max_workers}")
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(analyze_single_report, latest_reports.items()))

    # 处理结果
    valid_reports_count = 0
    for biz, analysis, error in results:
        if error:
            errors.append(error)
        elif analysis:
            latest_reports[biz]['analysis'] = analysis
            valid_reports_count += 1

    logger.info(f"成功分析 {valid_reports_count}/{total} 个报告")

    # 如果没有有效的报告分析结果，记录错误并返回
    if valid_reports_count == 0:
        error_msg = "未能成功分析任何报告，无法生成综合分析"
        logger.error(error_msg)
        errors.append(error_msg)
        
        # 格式化时间范围字符串
        time_range_str = f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"
            
        return {
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "report_title": f"金融舆情分析报告 - {end_date.strftime('%Y-%m-%d')}",
            "executive_summary": "无法生成报告，未能成功分析任何内容。",
            "individual_reports": {},
            "overall_analysis": {},
            "metadata": {
                "reports_count": 0,
                "generation_method": "STORM结构化推理",
                "time_range": time_range_str
            },
            "errors": errors
        }

    # 3. 综合分析
    logger.info("第3步: 开始综合分析所有报告...")
    try:
        # 直接调用，内部已有超时处理
        overall_analysis = analyze_all_reports_with_storm(latest_reports)
        logger.info("综合分析完成")
    except Exception as e:
        error_msg = f"综合分析失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        overall_analysis = {}

    # 4. 执行摘要
    logger.info("第4步: 开始生成执行摘要...")
    try:
        # 直接调用，内部已有超时处理
        executive_summary = generate_executive_summary(overall_analysis, valid_reports_count)
        logger.info("执行摘要生成完成")
    except Exception as e:
        error_msg = f"执行摘要生成失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        executive_summary = "执行摘要生成失败。"

    # 5. 组装报告
    logger.info("第5步: 组装最终报告...")
    
    # 格式化时间范围字符串
    time_range_str = f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"
    
    report = {
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "report_title": f"金融舆情分析报告 - {end_date.strftime('%Y-%m-%d')}",
        "executive_summary": executive_summary,
        "individual_reports": latest_reports,
        "overall_analysis": overall_analysis,
        "metadata": {
            "reports_count": valid_reports_count,
            "generation_method": "STORM结构化推理",
            "time_range": time_range_str
        },
        "errors": errors
    }

    logger.info("==================== 报告生成过程完成 ====================")
    return report

def format_structured_list(items, content_key='content', source_key='sources'):
    lines = []
    for item in items:
        if isinstance(item, dict):
            # 获取内容
            content = item.get(content_key) or item.get('point') or item.get('risk') or item.get('opportunity') or ''
            sources = item.get(source_key) or item.get('institutions') or item.get('source') or []
            
            # 检查并移除内容中可能已经包含的来源信息
            source_pattern = r'（来源：.*?）'
            content = re.sub(source_pattern, '', content).strip()
            
            # 添加来源信息
            if isinstance(sources, list):
                sources = '、'.join(sources)
            if sources:
                lines.append(f"- {content}（来源：{sources}）")
            else:
                lines.append(f"- {content}")
        elif isinstance(item, str):
            # 对于字符串类型，也检查并移除可能包含的来源信息
            source_pattern = r'（来源：.*?）'
            item = re.sub(source_pattern, '', item).strip()
            lines.append(f"- {item}")
    return '\n'.join(lines)

def add_heading_numbering(md_text):
    lines = md_text.split('\n')
    h1, h2, h3 = 0, 0, 0
    new_lines = []
    for line in lines:
        if line.startswith('# '):
            h1 += 1; h2 = 0; h3 = 0
            new_lines.append(f"# {h1}. {line[2:]}")
        elif line.startswith('## '):
            h2 += 1; h3 = 0
            new_lines.append(f"## {h1}.{h2} {line[3:]}")
        elif line.startswith('### '):
            h3 += 1
            new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
        else:
            new_lines.append(line)
    return '\n'.join(new_lines)

# 添加报告结构常量定义
REPORT_STRUCTURE = {
    "title": "金融舆情分析报告",
    "chapters": {
        "1": {
            "title": "核心观点总结",
            "sections": {
                "1.1": {
                    "title": "整体判断",
                    "required_elements": ["整体判断"],
                    "max_points": 5
                },
                "1.2": {
                    "title": "市场观点共识与分歧",
                    "required_elements": ["观点共识", "观点分歧"],
                    "max_points": 10
                },
                "1.3": {
                    "title": "主要风险与机会",
                    "required_elements": ["市场主要风险", "市场主要机会"],
                    "max_points": 10
                },
                "1.4": {
                    "title": "宏观经济预期",
                    "required_elements": ["宏观经济预期"],
                    "max_points": 10
                },
                "1.5": {
                    "title": "宏观政策预期",
                    "required_elements": ["宏观政策预期"],
                    "max_points": 10
                },
                "1.6": {
                    "title": "利率走势预测",
                    "required_elements": ["利率走势预测"],
                    "max_points": 8
                },
                "1.7": {
                    "title": "投资策略建议",
                    "required_elements": ["投资策略建议"],
                    "max_points": 12
                }
            },
            "required": True
        },
        "2": {
            "title": "各机构观点明细",
            "required": True,
            "per_institution": {
                "required_elements": ["报告信息", "核心观点摘要", "具体观点", "观点依据", "市场影响与洞见"],
                "max_viewpoints": 8,
                "max_evidence": 6
            }
        },
        "3": {
            "title": "附录",
            "required": True,
            "sections": ["分析方法说明", "数据来源说明", "执行情况"]
        }
    }
}

def validate_report_structure(report_text: str) -> tuple[bool, list[str]]:
    """
    验证报告结构是否符合预定义的格式要求
    
    Args:
        report_text: 报告文本内容
        
    Returns:
        tuple[bool, list[str]]: (是否通过验证, 问题列表)
    """
    issues = []
    lines = report_text.split('\n')
    
    # 检查章节结构
    current_chapter = None
    current_section = None
    chapters_found = set()
    sections_found = {}
    
    for line in lines:
        if line.startswith('# '):  # 一级标题
            chapter_num = line.split('.')[0].replace('# ', '').strip()
            if chapter_num.isdigit():
                current_chapter = chapter_num
                chapters_found.add(current_chapter)
                sections_found[current_chapter] = set()
        elif line.startswith('## '):  # 二级标题
            if '.' in line:
                section_num = '.'.join(line.split('.')[0:2]).replace('## ', '').strip()
                current_section = section_num
                if current_chapter:
                    sections_found[current_chapter].add(current_section)
    
    # 验证必需章节
    for chapter_num, chapter_info in REPORT_STRUCTURE['chapters'].items():
        if chapter_info.get('required', False) and chapter_num not in chapters_found:
            issues.append(f"缺少必需的章节 {chapter_num}: {chapter_info['title']}")
        
        # 验证必需小节
        if 'sections' in chapter_info:
            # 检查sections是否为字典类型
            if isinstance(chapter_info['sections'], dict):
                for section_num, section_info in chapter_info['sections'].items():
                    if chapter_num in sections_found:
                        if section_num not in sections_found[chapter_num]:
                            issues.append(f"缺少必需的小节 {section_num}: {section_info['title']}")
            # 如果sections是列表类型，使用不同的处理方式
            elif isinstance(chapter_info['sections'], list):
                # 对于列表类型的sections，我们只检查是否存在必需的小节，但不检查具体的小节编号
                logger.info(f"章节 {chapter_num} 的sections是列表类型，包含 {len(chapter_info['sections'])} 个元素")
            else:
                logger.warning(f"章节 {chapter_num} 的sections既不是字典也不是列表: {type(chapter_info['sections'])}")
    
    # 验证内容长度限制
    for chapter_num, chapter_info in REPORT_STRUCTURE['chapters'].items():
        if 'max_length' in chapter_info:
            chapter_content = extract_chapter_content(report_text, chapter_num)
            if len(chapter_content) > chapter_info['max_length']:
                issues.append(f"章节 {chapter_num} 内容超出长度限制")
    
    return len(issues) == 0, issues

def extract_chapter_content(report_text: str, chapter_num: str) -> str:
    """提取指定章节的内容"""
    lines = report_text.split('\n')
    chapter_content = []
    in_chapter = False
    
    for line in lines:
        if line.startswith(f"# {chapter_num}."):
            in_chapter = True
            continue
        elif line.startswith("# "):
            if in_chapter:
                break
        elif in_chapter:
            chapter_content.append(line)
    
    return '\n'.join(chapter_content)

def format_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
    logger.info("开始格式化STORM报告...")
    start_time = datetime.now()
    
    # 创建纯Markdown输出，不包含HTML/CSS样式
    output = f"# {report['report_title']}\n\n"
    
    # 添加报告元数据
    output += f"**生成时间**: {report['generation_time']}\n"
    output += f"**分析报告数**: {report['metadata']['reports_count']}\n"
    output += f"**时间范围**: {report['metadata']['time_range']}\n\n"
    
    output += "---\n\n"
    
    # 添加目录
    output += "## 目录\n\n"
    output += "1. [核心观点总结](#1-核心观点总结)\n"
    output += "   1.1. [整体判断](#11-整体判断)\n"
    output += "   1.2. [市场观点共识与分歧](#12-市场观点共识与分歧)\n"
    output += "   1.3. [主要风险与机会](#13-主要风险与机会)\n"
    output += "   1.4. [宏观经济预期](#14-宏观经济预期)\n"
    output += "   1.5. [宏观政策预期](#15-宏观政策预期)\n"
    output += "   1.6. [利率走势预测](#16-利率走势预测)\n"
    output += "   1.7. [投资策略建议](#17-投资策略建议)\n"
    output += "2. [各机构观点明细](#2-各机构观点明细)\n"
    output += "3. [附录](#3-附录)\n\n"
    
    output += "---\n\n"
    
    # 获取综合分析数据
    overall = report['overall_analysis']
    
    # 添加第一部分：核心观点总结
    output += "# 1. 核心观点总结\n\n"
    
    # 1.1 整体判断
    output += "## 1.1 整体判断\n\n"
                    
                    # 根据小节类型生成不同内容
                    if section_num == "1.1":  # 整体判断
                        if 'overall_summary' in overall and overall['overall_summary']:
                            # 将整体判断转换为列表格式，并添加"来源：综合分析"
                            summary = overall['overall_summary'].strip()
                            # 如果不是已经是列表格式，则转换为列表格式
                            if not summary.startswith('- '):
                                # 按句号或分号分割成多个观点
                                sentences = re.split(r'[。；;]', summary)
                                # 过滤空字符串并添加列表格式
                                formatted_sentences = []
                                for sentence in sentences:
                                    sentence = sentence.strip()
                                    if sentence:
                                        # 检查是否已经包含来源信息
                                        if not re.search(r'（来源：.*?）', sentence):
                                            formatted_sentences.append(f"- {sentence}（来源：综合分析）")
                                        else:
                                            formatted_sentences.append(f"- {sentence}")
                                output += '\n'.join(formatted_sentences) + "\n\n"
                            else:
                                # 已经是列表格式，检查是否需要添加来源
                                lines = summary.split('\n')
                                formatted_lines = []
                                for line in lines:
                                    line = line.strip()
                                    if line:
                                        # 检查是否已经包含来源信息
                                        if not re.search(r'（来源：.*?）', line):
                                            formatted_lines.append(f"{line}（来源：综合分析）")
                                        else:
                                            formatted_lines.append(line)
                                output += '\n'.join(formatted_lines) + "\n\n"
                        else:
                            # 将执行摘要转换为列表格式，并添加"来源：综合分析"
                            summary = report['executive_summary'].strip()
                            # 如果不是已经是列表格式，则转换为列表格式
                            if not summary.startswith('- '):
                                # 按句号或分号分割成多个观点
                                sentences = re.split(r'[。；;]', summary)
                                # 过滤空字符串并添加列表格式
                                formatted_sentences = []
                                for sentence in sentences:
                                    sentence = sentence.strip()
                                    if sentence:
                                        # 检查是否已经包含来源信息
                                        if not re.search(r'（来源：.*?）', sentence):
                                            formatted_sentences.append(f"- {sentence}（来源：综合分析）")
                                        else:
                                            formatted_sentences.append(f"- {sentence}")
                                output += '\n'.join(formatted_sentences) + "\n\n"
                            else:
                                # 已经是列表格式，检查是否需要添加来源
                                lines = summary.split('\n')
                                formatted_lines = []
                                for line in lines:
                                    line = line.strip()
                                    if line:
                                        # 检查是否已经包含来源信息
                                        if not re.search(r'（来源：.*?）', line):
                                            formatted_lines.append(f"{line}（来源：综合分析）")
                                        else:
                                            formatted_lines.append(line)
                                output += '\n'.join(formatted_lines) + "\n\n"
                            
                    elif section_num == "1.2":  # 市场观点共识与分歧
                        if 'consensus_points' in overall and overall['consensus_points']:
                            output += format_structured_list(overall['consensus_points']) + "\n\n"
                        
                        if 'divergent_points' in overall and overall['divergent_points']:
                            output += format_structured_list(overall['divergent_points']) + "\n\n"
                            
                    elif section_num == "1.3":  # 主要风险与机会
                        if 'market_risks' in overall and overall['market_risks']:
                            output += format_structured_list(overall['market_risks']) + "\n\n"
                            
                        if 'market_opportunities' in overall and overall['market_opportunities']:
                            output += format_structured_list(overall['market_opportunities']) + "\n\n"
                            
                    elif section_num == "1.4":  # 宏观经济预期
                        if 'macro_economic_outlook' in overall and overall['macro_economic_outlook']:
                            output += format_structured_list(overall['macro_economic_outlook']) + "\n\n"
                            
                    elif section_num == "1.5":  # 宏观政策预期
                        if 'policy_expectations' in overall and overall['policy_expectations']:
                            output += format_structured_list(overall['policy_expectations']) + "\n\n"
                            
                    elif section_num == "1.6":  # 利率走势预测
                        if 'interest_rate_forecast' in overall and overall['interest_rate_forecast']:
                            output += format_structured_list(overall['interest_rate_forecast']) + "\n\n"
                            
                    elif section_num == "1.7":  # 投资策略建议
                        if 'investment_recommendations' in overall and overall['investment_recommendations']:
                            output += format_structured_list(overall['investment_recommendations']) + "\n\n"

        elif chapter_num == "2":  # 各机构观点明细
            output += f"""<h1 id="chapter-{chapter_num}">{chapter_num}. {chapter_info['title']}</h1>
<div class="section-divider"></div>
"""
            
            # 添加各机构观点明细（带编号）
            for i, (biz, report_data) in enumerate(report['individual_reports'].items(), 1):
                if 'site_name' in report_data:
                    site_name = report_data['site_name']
                    output += f"""<h2 id="institution-{i}" class="institution-title">2.{i} {site_name}</h2>
<div class="institution-content">
"""
                    
                    # 报告基本信息
                    output += """<h3 class="report-subtitle">报告信息</h3>
<div class="report-info">
"""
                    output += f"- 标题: {report_data.get('title', '无标题')}\n"
                    output += f"- 作者: {report_data.get('author', '未知作者')}\n"
                    output += f"- 发布时间: {report_data.get('publishtime', '未知时间')}\n</div>\n\n"
                    
                    # 核心观点
                    output += """<h3 class="report-subtitle">核心观点摘要</h3>
<div class="core-summary">
"""
                    if 'analysis' in report_data and 'summary' in report_data['analysis']:
                        output += report_data['analysis']['summary'] + "\n</div>\n\n"
                    elif 'summary' in report_data:
                        output += report_data['summary'] + "\n</div>\n\n"
                    else:
                        output += "无核心观点摘要\n</div>\n\n"
                    
                    # 具体观点
                    output += """<h3 class="report-subtitle">具体观点</h3>
<div class="viewpoints">
"""
                    if 'analysis' in report_data and 'core_viewpoints' in report_data['analysis']:
                        viewpoints = report_data['analysis']['core_viewpoints']
                        if isinstance(viewpoints, list):
                            for i, viewpoint in enumerate(viewpoints, 1):
                                output += f"{i}. {viewpoint}\n"
                        else:
                            output += viewpoints + "\n"
                    else:
                        output += "无具体观点\n"
                    output += "</div>\n\n"
                    
                    # 观点依据
                    output += """<h3 class="report-subtitle">观点依据</h3>
<div class="evidence">
"""
                    if 'analysis' in report_data and 'supporting_evidence' in report_data['analysis']:
                        evidence = report_data['analysis']['supporting_evidence']
                        for i, item in enumerate(evidence, 1):
                            output += f"{i}. {item}\n"
                    else:
                        output += "无观点依据\n"
                    output += "</div>\n\n"
                    
                    # 市场影响与洞见
                    output += """<h3 class="report-subtitle">市场影响与洞见</h3>
<div class="market-impact">
"""
                    if 'analysis' in report_data and 'key_insights' in report_data['analysis']:
                        output += report_data['analysis']['key_insights'] + "\n\n"
                        if 'market_impact' in report_data['analysis']:
                            market_impact = report_data['analysis']['market_impact']
                            if isinstance(market_impact, dict):
                                # 你可以只取某个字段，也可以直接转成字符串
                                output += "市场影响: " + json.dumps(market_impact, ensure_ascii=False) + "\n</div>\n\n"
                            else:
                                output += "市场影响: " + str(market_impact) + "\n</div>\n\n"
                    else:
                        output += "无市场影响与洞见\n</div>\n\n"
                    
                    # 关闭机构内容的div标签
                    output += "</div>\n\n"

        elif chapter_num == "3":  # 附录
            output += f"""<h1 id="chapter-{chapter_num}">{chapter_num}. {chapter_info['title']}</h1>
<div class="section-divider"></div>
"""
            
            # 检查sections是否为列表类型
            if 'sections' in chapter_info and isinstance(chapter_info['sections'], list):
                for section in chapter_info['sections']:
                    output += f"""<h2 id="appendix-{section}">{section}</h2>
"""
                    
                    if section == "分析方法说明":
                        output += "本报告有wiseAgent智能体推理生成，对金融机构公众号发布的研究报告进行多维度分析。\n\n"
                        output += "分析流程包括：\n"
                        output += "1. 数据收集：从各大金融机构公众号获取最新研究报告\n"
                        output += "2. 单篇分析：对每篇报告进行结构化分析，提取核心观点和依据\n"
                        output += "3. 综合分析：对所有机构观点进行归纳总结，找出共识与分歧\n"
                        output += "4. 报告生成：基于分析结果生成结构化的金融舆情分析报告\n\n"
                        
                    elif section == "数据来源说明":
                        output += f"本次分析的数据来源于{len(report['individual_reports'])}家金融机构的公众号，时间范围为{report['metadata']['time_range']}。\n\n"
                        output += "数据包括：\n"
                        for biz, report_data in report['individual_reports'].items():
                            if 'site_name' in report_data and 'title' in report_data:
                                output += f"- {report_data['site_name']}: {report_data['title']}\n"
                        output += "\n"
                        
                    elif section == "执行情况":
                        output += f"报告生成时间: {report['generation_time']}\n"
                        output += f"分析报告数量: {report['metadata']['reports_count']}\n"
                        if 'errors' in report and report['errors']:
                            output += "\n执行过程中的错误：\n"
                            for error in report['errors']:
                                output += f"- {error}\n"
                        output += "\n"
            else:
                logger.warning(f"章节 {chapter_num} 的sections不是列表类型或不存在")
                # 添加默认的附录内容
                output += "## 分析方法说明\n\n"
                output += "本报告采用STORM结构化推理方法，对金融机构公众号发布的研究报告进行多维度分析。\n\n"
                
                output += "## 数据来源说明\n\n"
                output += f"本次分析的数据来源于{len(report['individual_reports'])}家金融机构的公众号。\n\n"
                
                output += "## 执行情况\n\n"
                output += f"报告生成时间: {report['generation_time']}\n"
                output += f"分析报告数量: {report['metadata']['reports_count']}\n\n"
    
    # 验证报告结构
    is_valid, issues = validate_report_structure(output)
    if not is_valid:
        logger.warning("报告结构存在以下问题:")
        for issue in issues:
            logger.warning(f"- {issue}")
    
    # 添加页脚
    output += """
<div class="footer">
  <p>本报告由 wiseAgent 智能分析系统生成 &copy; """ + datetime.now().strftime("%Y") + """</p>
</div>
"""
    
    end_time = datetime.now()
    total_elapsed_time = (end_time - start_time).total_seconds()
    logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
    
    return output

# LangGraph智能体定义
class AgentState(TypedDict):
    """智能体状态"""
    report_data: Optional[Dict]
    report_text: Optional[str]
    error: Optional[str]
    es_index: Optional[str]
    es_host: Optional[str]
    es_port: Optional[int]
    fetch_start_time: Optional[str]
    fetch_end_time: Optional[str]
    llm_model: Optional[str]
    llm_api_base: Optional[str]
    llm_api_key: Optional[str]

def fetch_and_analyze_storm(state: AgentState) -> AgentState:
    """使用STORM方法获取数据并分析"""
    try:
        logger.info("====== 智能体工作流: 开始生成金融舆情分析报告 (STORM方法) ======")
        start_time = datetime.now()
        es_index = state.get("es_index")
        es_host = state.get("es_host")
        es_port = state.get("es_port")
        fetch_start_time = state.get("fetch_start_time")
        fetch_end_time = state.get("fetch_end_time")
        llm_model = state.get("llm_model")
        llm_api_base = state.get("llm_api_base")
        llm_api_key = state.get("llm_api_key")
        
        # 将时间参数传递给 generate_report_with_storm 函数
        report_data = generate_report_with_storm(
            fetch_start_time=fetch_start_time,
            fetch_end_time=fetch_end_time,
            es_index=es_index,
            es_host=es_host,
            es_port=es_port
        )
        
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        
        # 检查报告数据中的reports_count字段，如果为0表示没有获取到数据
        if report_data and report_data.get("metadata", {}).get("reports_count", 0) == 0:
            logger.warning("未获取到任何报告数据，流程将提前结束")
            # 仍然保存报告数据，但标记为无数据状态
            state["report_data"] = report_data
            state["no_data"] = True
            state["executive_summary"] = report_data.get("executive_summary", "未获取到任何报告数据")
            return state
            
        logger.info(f"报告生成完成，耗时: {elapsed_time:.2f} 秒")
        state["report_data"] = report_data
        return state
    except Exception as e:
        logger.error(f"生成报告时出错: {str(e)}")
        traceback.print_exc()
        state["error"] = f"生成报告失败: {str(e)}"
        return state

def format_storm_report_node(state: AgentState) -> AgentState:
    """格式化STORM报告"""
    try:
        logger.info("====== 智能体工作流: 开始格式化报告 ======")
        start_time = datetime.now()
        
        # 检查是否标记为无数据状态
        if state.get("no_data", False):
            logger.info("检测到无数据标记，将简化报告处理流程")
            # 简单格式化报告文本
            if "report_data" in state and state["report_data"]:
                report_text = f"# {state['report_data'].get('report_title', '金融舆情分析报告')}\n\n"
                report_text += f"生成时间: {state['report_data'].get('generation_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}\n\n"
                report_text += f"## 执行摘要\n\n{state.get('executive_summary', '未获取到任何报告数据')}\n\n"
                report_text += f"## 数据情况\n\n在指定的时间范围（{state['report_data'].get('metadata', {}).get('time_range', '')}）内未获取到任何有效的报告数据。\n\n"
                report_text += "## 可能的原因\n\n1. 指定时间范围内没有发布新的报告\n2. 数据源连接问题\n3. 数据过滤条件过于严格\n\n"
                report_text += "## 建议\n\n1. 尝试扩大时间范围\n2. 检查数据源连接\n3. 检查数据过滤条件\n"
                
                state["report_text"] = report_text
            return state
        
        if "report_data" in state and state["report_data"]:
            # 1. 格式化报告
            report_text = format_storm_report(state["report_data"])
            state["report_text"] = report_text
            
            # 2. 将报告上传到MinIO
            try:
                # 初始化MinIO客户端
                logger.info("初始化MinIO客户端...")
                minio_client = Minio(
                    endpoint=MINIO_ENDPOINT,
                    access_key=MINIO_ACCESS_KEY,
                    secret_key=MINIO_SECRET_KEY,
                    secure=False  # 根据您的MinIO配置调整
                )
                
                # 确保bucket存在
                bucket_name = MINIO_BUCKET
                if not minio_client.bucket_exists(bucket_name):
                    minio_client.make_bucket(bucket_name)
                    logger.info(f"创建MinIO bucket: {bucket_name}")
                else:
                    logger.info(f"MinIO bucket已存在: {bucket_name}")
                
                # 生成唯一的文件名
                report_id = str(uuid.uuid4())
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                
                # 将Markdown转换为PDF
                logger.info("开始将Markdown转换为PDF...")
                try:
                    # 将Markdown转换为纯文本
                    html = markdown.markdown(report_text, extensions=['tables', 'fenced_code'])
                    
                    # 创建临时PDF文件路径
                    temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                    temp_file.close()
                    pdf_path = temp_file.name
                    
                    # 注册中文字体（如果需要中文支持）
                    try:
                        # 尝试不同系统上可能的中文字体路径
                        font_paths = [
                            # macOS 字体
                            # '/System/Library/Fonts/PingFang.ttc',
                            # '/System/Library/Fonts/STHeiti Light.ttc',
                            # '/System/Library/Fonts/STHeiti Medium.ttc',
                            # '/System/Library/Fonts/Hiragino Sans GB.ttc',
                            # '/Library/Fonts/Arial Unicode.ttf',
                            # '/Library/Fonts/Songti.ttc',
                            
                            # Linux 字体
                            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
                            '/usr/share/fonts/truetype/arphic/uming.ttc',
                            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
                            '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                            '/usr/share/fonts/noto-cjk/NotoSansCJK-Regular.ttc',
                            
                            # # Windows 字体
                            # 'C:\\Windows\\Fonts\\simhei.ttf',
                            # 'C:\\Windows\\Fonts\\simsun.ttc',
                            # 'C:\\Windows\\Fonts\\msyh.ttc',
                            # 'C:\\Windows\\Fonts\\simkai.ttf',
                            # 'C:\\Windows\\Fonts\\simfang.ttf',
                            
                            # # 当前目录下可能的字体
                            # './fonts/simhei.ttf',
                            # './fonts/simsun.ttc',
                            # './fonts/msyh.ttc',
                        ]
                        
                        # 尝试在当前目录创建fonts文件夹并下载一个开源中文字体
                        try:
                            import requests
                            import shutil
                            
                            fonts_dir = './fonts'
                            if not os.path.exists(fonts_dir):
                                os.makedirs(fonts_dir)
                                logger.info(f"创建字体目录: {fonts_dir}")
                            
                            # 尝试下载开源中文字体
                            noto_font_path = f"{fonts_dir}/NotoSansSC-Regular.ttf"
                            if not os.path.exists(noto_font_path):
                                font_url = "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansSC-Regular.otf"
                                try:
                                    logger.info(f"尝试下载开源中文字体: {font_url}")
                                    response = requests.get(font_url, stream=True, timeout=10)
                                    if response.status_code == 200:
                                        with open(noto_font_path, 'wb') as f:
                                            shutil.copyfileobj(response.raw, f)
                                        logger.info(f"成功下载字体到: {noto_font_path}")
                                        # 将下载的字体添加到字体路径列表
                                        font_paths.insert(0, noto_font_path)
                                    else:
                                        logger.warning(f"下载字体失败，HTTP状态码: {response.status_code}")
                                except Exception as e:
                                    logger.warning(f"下载字体时出错: {str(e)}")
                        except Exception as e:
                            logger.warning(f"准备字体目录时出错: {str(e)}")
                        
                        # 尝试使用内置中文字体
                        try:
                            from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                            logger.info("已注册内置中文字体: STSong-Light")
                            
                            # 使用内置字体设置样式
                            styles = getSampleStyleSheet()
                            title_style = ParagraphStyle(
                                'CustomTitle',
                                parent=styles['Heading1'],
                                fontSize=24,
                                spaceAfter=30,
                                textColor=colors.HexColor('#1a5276'),
                                fontName='STSong-Light'
                            )
                            heading1_style = ParagraphStyle(
                                'CustomH1',
                                parent=styles['Heading1'],
                                fontSize=18,
                                spaceAfter=20,
                                textColor=colors.HexColor('#2874a6'),
                                fontName='STSong-Light'
                            )
                            heading2_style = ParagraphStyle(
                                'CustomH2',
                                parent=styles['Heading2'],
                                fontSize=16,
                                spaceAfter=15,
                                textColor=colors.HexColor('#3498db'),
                                fontName='STSong-Light'
                            )
                            normal_style = ParagraphStyle(
                                'CustomNormal',
                                parent=styles['Normal'],
                                fontSize=12,
                                spaceAfter=12,
                                leading=16,
                                fontName='STSong-Light'
                            )
                            
                            font_loaded = True
                            logger.info("已设置内置中文字体样式")
                        except Exception as e:
                            logger.warning(f"加载内置中文字体失败: {str(e)}")
                            font_loaded = False
                        
                        # 如果内置字体加载失败，尝试系统字体
                        if not font_loaded:
                            for font_path in font_paths:
                                if os.path.exists(font_path):
                                    try:
                                        font_name = os.path.basename(font_path).split('.')[0]
                                        pdfmetrics.registerFont(TTFont(font_name, font_path))
                                        logger.info(f"已加载字体: {font_name} 从 {font_path}")
                                        
                                        # 更新样式以使用中文字体
                                        styles = getSampleStyleSheet()
                                        title_style = ParagraphStyle(
                                            'CustomTitle',
                                            parent=styles['Heading1'],
                                            fontSize=24,
                                            spaceAfter=30,
                                            textColor=colors.HexColor('#1a5276'),
                                            fontName=font_name
                                        )
                                        heading1_style = ParagraphStyle(
                                            'CustomH1',
                                            parent=styles['Heading1'],
                                            fontSize=18,
                                            spaceAfter=20,
                                            textColor=colors.HexColor('#2874a6'),
                                            fontName=font_name
                                        )
                                        heading2_style = ParagraphStyle(
                                            'CustomH2',
                                            parent=styles['Heading2'],
                                            fontSize=16,
                                            spaceAfter=15,
                                            textColor=colors.HexColor('#3498db'),
                                            fontName=font_name
                                        )
                                        normal_style = ParagraphStyle(
                                            'CustomNormal',
                                            parent=styles['Normal'],
                                            fontSize=12,
                                            spaceAfter=12,
                                            leading=16,
                                            fontName=font_name
                                        )
                                        
                                        font_loaded = True
                                        break
                                    except Exception as e:
                                        logger.warning(f"加载字体 {font_path} 失败: {str(e)}")
                        
                        if not font_loaded:
                            logger.warning("未找到可用的中文字体，将使用默认字体")
                            # 使用默认样式
                            styles = getSampleStyleSheet()
                            title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], fontSize=24, spaceAfter=30, textColor=colors.HexColor('#1a5276'))
                            heading1_style = ParagraphStyle('CustomH1', parent=styles['Heading1'], fontSize=18, spaceAfter=20, textColor=colors.HexColor('#2874a6'))
                            heading2_style = ParagraphStyle('CustomH2', parent=styles['Heading2'], fontSize=16, spaceAfter=15, textColor=colors.HexColor('#3498db'))
                            normal_style = ParagraphStyle('CustomNormal', parent=styles['Normal'], fontSize=12, spaceAfter=12, leading=16)
                    except Exception as e:
                        logger.warning(f"加载字体时出错: {str(e)}")
                        logger.warning("将使用默认字体")
                        # 使用默认样式
                        styles = getSampleStyleSheet()
                        title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], fontSize=24, spaceAfter=30, textColor=colors.HexColor('#1a5276'))
                        heading1_style = ParagraphStyle('CustomH1', parent=styles['Heading1'], fontSize=18, spaceAfter=20, textColor=colors.HexColor('#2874a6'))
                        heading2_style = ParagraphStyle('CustomH2', parent=styles['Heading2'], fontSize=16, spaceAfter=15, textColor=colors.HexColor('#3498db'))
                        normal_style = ParagraphStyle('CustomNormal', parent=styles['Normal'], fontSize=12, spaceAfter=12, leading=16)
                    
                    # 创建PDF文档
                    doc = SimpleDocTemplate(
                        pdf_path,
                        pagesize=A4,
                        rightMargin=72,
                        leftMargin=72,
                        topMargin=72,
                        bottomMargin=72
                    )
                    
                    # 构建文档内容
                    story = []
                    
                    # 处理文本内容
                    lines = html.split('\n')
                    current_style = normal_style
                    
                    # 简单的HTML标签处理函数
                    def clean_html_tags(text):
                        import re
                        # 保留一些基本的格式化标记（加粗、斜体）
                        text = text.replace('<strong>', '<b>').replace('</strong>', '</b>')
                        text = text.replace('<em>', '<i>').replace('</em>', '</i>')
                        # 移除其他HTML标签
                        text = re.sub(r'<[^>]+>', '', text)
                        return text
                    
                    for line in lines:
                        line = line.strip()
                        if not line:
                            story.append(Spacer(1, 12))
                            continue
                            
                        # 根据HTML标签选择样式
                        if '<h1>' in line and '</h1>' in line:
                            line = re.sub(r'<h1>(.*?)</h1>', r'\1', line)
                            current_style = title_style
                        elif '<h2>' in line and '</h2>' in line:
                            line = re.sub(r'<h2>(.*?)</h2>', r'\1', line)
                            current_style = heading1_style
                        elif '<h3>' in line and '</h3>' in line:
                            line = re.sub(r'<h3>(.*?)</h3>', r'\1', line)
                            current_style = heading2_style
                        else:
                            current_style = normal_style
                        
                        # 清理其他HTML标签
                        line = clean_html_tags(line)
                        
                        # 创建段落
                        if line:
                            try:
                                para = Paragraph(line, current_style)
                                story.append(para)
                                story.append(Spacer(1, 6))
                            except Exception as e:
                                logger.warning(f"处理段落时出错: {str(e)}, 内容: {line[:50]}...")
                                # 尝试移除所有特殊字符后再处理
                                try:
                                    clean_line = re.sub(r'[^\w\s,.;:!?()[\]{}\'"-]', '', line)
                                    para = Paragraph(clean_line, normal_style)
                                    story.append(para)
                                except:
                                    # 如果还是失败，就跳过这一行
                                    pass
                    
                    # 生成PDF
                    try:
                        doc.build(story)
                        logger.info(f"PDF生成成功: {pdf_path}")
                    except Exception as e:
                        logger.error(f"生成PDF时出错: {str(e)}")
                        raise
                    
                    # 读取生成的PDF文件
                    with open(pdf_path, 'rb') as pdf_file:
                        pdf_bytes = pdf_file.read()
                    
                    # 上传PDF文件到MinIO
                    pdf_filename = f"financial_report_storm_{timestamp}_{report_id}.pdf"
                    pdf_object_path = f"financial_reports/{pdf_filename}"
                    
                    minio_client.put_object(
                        bucket_name=bucket_name,
                        object_name=pdf_object_path,
                        data=BytesIO(pdf_bytes),
                        length=len(pdf_bytes),
                        content_type="application/pdf"
                    )
                    
                    # 生成预签名URL（7天有效）
                    pdf_storage_path = minio_client.presigned_get_object(
                        bucket_name=bucket_name,
                        object_name=pdf_object_path,
                        expires=timedelta(days=7)
                    )
                    logger.info(f"PDF报告已上传到MinIO: {pdf_storage_path}")
                    
                    # 同时保存Markdown版本作为备份
                    md_filename = f"financial_report_storm_{timestamp}_{report_id}.md"
                    md_object_path = f"financial_reports/{md_filename}"
                    
                    # 确保使用UTF-8编码
                    md_bytes = report_text.encode('utf-8')
                    minio_client.put_object(
                        bucket_name=bucket_name,
                        object_name=md_object_path,
                        data=BytesIO(md_bytes),
                        length=len(md_bytes),
                        content_type="text/markdown; charset=utf-8"
                    )
                    
                    # 清理临时文件
                    try:
                        if os.path.exists(pdf_path):
                            os.remove(pdf_path)
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")
                    
                except Exception as e:
                    logger.error(f"PDF转换失败: {str(e)}")
                    traceback.print_exc()
                    
                    # 如果PDF转换失败，回退到保存Markdown
                    logger.info("回退到保存Markdown格式...")
                    filename = f"financial_report_storm_{timestamp}_{report_id}.md"
                    object_path = f"financial_reports/{filename}"
                    
                    # 将报告内容转换为字节并上传，确保使用UTF-8编码
                    report_bytes = report_text.encode('utf-8')
                    minio_client.put_object(
                        bucket_name=bucket_name,
                        object_name=object_path,
                        data=BytesIO(report_bytes),
                        length=len(report_bytes),
                        content_type="text/markdown; charset=utf-8"
                    )
                    
                    # 生成预签名URL（7天有效）
                    pdf_storage_path = minio_client.presigned_get_object(
                        bucket_name=bucket_name,
                        object_name=object_path,
                        expires=timedelta(days=7)
                    )
                    logger.info(f"Markdown报告已上传到MinIO: {pdf_storage_path}")
                
                # 3. 初始化MongoDB连接
                logger.info("初始化MongoDB连接...")
                # 断开可能存在的连接
                disconnect()
                # 连接到MongoDB
                connect(
                    db=DATABASE_NAME,
                    username=MONGODB_USER,
                    password=MONGODB_PASSWORD,
                    authentication_source=MONGODB_AUTH_SOURCE,
                    host=MONGODB_HOST,
                    port=MONGODB_PORT
                )
                logger.info("MongoDB连接已建立")
                
                # 4. 将报告信息保存到MongoDB
                report_data = state["report_data"]
                
                # 解析时间范围
                try:
                    # 优先使用传入的时间参数
                    if state.get("fetch_start_time") and state.get("fetch_end_time"):
                        start_time_str = state.get("fetch_start_time")
                        end_time_str = state.get("fetch_end_time")
                        
                        if isinstance(start_time_str, str):
                            start_time_date = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                        else:
                            start_time_date = start_time_str
                            
                        if isinstance(end_time_str, str):
                            end_time_date = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
                        else:
                            end_time_date = end_time_str
                            
                        logger.info(f"使用传入的时间参数: {start_time_date} 至 {end_time_date}")
                    else:
                        # 如果没有传入参数，再尝试从报告元数据解析
                        time_range = report_data['metadata']['time_range']
                        # 提取天数
                        days_str = time_range.split("截至")[0].strip()
                        days = int(''.join(filter(str.isdigit, days_str)))
                        
                        # 提取结束日期并清理字符串
                        end_date_str = time_range.split("截至")[1].strip()
                        # 移除所有括号和其他非日期字符，更彻底地清理
                        end_date_str = re.sub(r'[^\d\-]', '', end_date_str)
                        
                        # 打印清理后的字符串，便于调试
                        logger.info(f"清理后的日期字符串: '{end_date_str}'")
                        
                        # 确保日期格式正确
                        if len(end_date_str) >= 10:  # 确保至少有YYYY-MM-DD长度
                            end_date_str = end_date_str[:10]  # 只取前10个字符(YYYY-MM-DD)
                            logger.info(f"尝试解析日期: '{end_date_str}'")
                            end_time_date = datetime.strptime(end_date_str, "%Y-%m-%d")
                        else:
                            raise ValueError(f"无效的日期格式: {end_date_str}")
                        
                        # 计算开始日期
                        start_time_date = end_time_date - timedelta(days=days)
                        
                        logger.info(f"从报告元数据解析时间范围: {start_time_date} 至 {end_time_date}")
                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"解析时间范围出错: {str(e)}")
                    # 使用默认值
                    end_time_date = datetime.now()
                    start_time_date = end_time_date - timedelta(days=3)
                    logger.info(f"使用默认时间范围: {start_time_date} 至 {end_time_date}")
                
                # 获取媒体ID列表
                media_ids = BIZ_LIST
                
                # 创建并保存报告记录，确保内容使用UTF-8编码
                media_report = MediaInsightsReport(
                    title=report_data['report_title'],
                    content=report_text,
                    storage_path=pdf_storage_path,
                    report_type="financial_weixin_v2",
                    start_time=start_time_date,
                    end_time=end_time_date,
                    media_ids=media_ids
                )
                media_report.save()
                logger.info(f"报告信息已保存到MongoDB，ID: {media_report.id}")
                
                # 更新状态
                state["storage_path"] = pdf_storage_path
                state["mongodb_id"] = str(media_report.id)
                
            except Exception as e:
                logger.error(f"保存报告时出错: {str(e)}")
                traceback.print_exc()
                state["storage_error"] = f"保存报告失败: {str(e)}"
            
            end_time = datetime.now()
            elapsed_time = (end_time - start_time).total_seconds()
            logger.info(f"报告格式化和保存完成，耗时: {elapsed_time:.2f} 秒")
        return state
    except Exception as e:
        logger.error(f"格式化报告时出错: {str(e)}")
        traceback.print_exc()
        state["error"] = f"格式化报告失败: {str(e)}"
        return state

# 创建工作流
workflow = StateGraph(AgentState)
workflow.add_node("fetch_and_analyze_storm", fetch_and_analyze_storm)
workflow.add_node("format_storm_report", format_storm_report_node)
workflow.add_edge("fetch_and_analyze_storm", "format_storm_report")
workflow.add_edge("format_storm_report", END)
workflow.set_entry_point("fetch_and_analyze_storm")
workflow = workflow.compile()

def run_agent(fetch_start_time,fetch_end_time):
    """运行金融舆情分析智能体"""
    logger.info("====================== 开始运行金融舆情分析智能体 ======================")
    start_time = datetime.now()

    # # 使用配置的天数，如果未提供则使用默认值
    # if days is None:
    #     days = ANALYSIS_CONFIG.get("max_days", 3)

    # fetch_end_time = datetime.now()
    # fetch_start_time = fetch_end_time - timedelta(days=days)

        
    logger.info(f"分析时间范围：{fetch_start_time} 至 {fetch_end_time}")
    

    initial_state = {
        "report_data": None,
        "report_text": None,
        "error": None,
        "es_index": TARGET_ES_INDEX,
        "es_host": TARGET_ES_HOST,
        "es_port": TARGET_ES_PORT,
        "fetch_start_time": fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),
        "fetch_end_time": fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),
        "llm_model": LLM_MODEL,
        "llm_api_base": LLM_API_BASE,
        "llm_api_key": OPENAI_API_KEY,
    }

    try:
        logger.info("启动LangGraph工作流...")
        result = workflow.invoke(initial_state)

        if result.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
            return {"status": "error", "message": result["error"]}

        if result.get("report_text"):
            end_time = datetime.now()
            elapsed_time = (end_time - start_time).total_seconds()
            logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
            return {
                "status": "success",
                "report_text": result["report_text"],
                "report_data": result["report_data"],
                "storage_path": result.get("storage_path", ""),
                "mongodb_id": result.get("mongodb_id", "")
            }
        else:
            logger.error("未能生成报告文本")
            return {"status": "error", "message": "未能生成报告文本"}

    except Exception as e:
        logger.error(f"运行智能体时出错: {str(e)}")
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

def safe_str(val):
    if isinstance(val, str):
        return val
    elif isinstance(val, dict):
        return json.dumps(val, ensure_ascii=False)
    elif val is None:
        return ""
    else:
        return str(val)

def run_agent_30days(days=3):
    """循环生成近30天（每天一份）的金融舆情分析报告"""
    logger.info("====================== 开始批量运行金融舆情分析智能体（近30天） ======================")
    all_results = []
    for i in range(3,30):
        # 计算本次循环的时间区间（都是从0点开始）
        fetch_end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
        fetch_start_time = fetch_end_time - timedelta(days=days)
        initial_state = {
            "report_data": None,
            "report_text": None,
            "error": None,
            "es_index": TARGET_ES_INDEX,
            "es_host": TARGET_ES_HOST,
            "es_port": TARGET_ES_PORT,
            "fetch_start_time": fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "fetch_end_time": fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "llm_model": LLM_MODEL,
            "llm_api_base": LLM_API_BASE,
            "llm_api_key": OPENAI_API_KEY,
        }
        try:
            start_time = datetime.now()
            logger.info(f"启动LangGraph工作流... 日期区间: {initial_state['fetch_start_time']} ~ {initial_state['fetch_end_time']}")
            result = workflow.invoke(initial_state)
            if result.get("error"):
                logger.error(f"智能体运行出错: {result['error']}")
                all_results.append({"status": "error", "message": result["error"], "date": fetch_end_time.strftime("%Y-%m-%d")})
                continue
            if result.get("report_text"):
                end_time = datetime.now()
                elapsed_time = (end_time - start_time).total_seconds()
                logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
                all_results.append({
                    "status": "success",
                    "report_text": result["report_text"],
                    "report_data": result["report_data"],
                    "storage_path": result.get("storage_path", ""),
                    "mongodb_id": result.get("mongodb_id", ""),
                    "date": fetch_end_time.strftime("%Y-%m-%d")
                })
            else:
                logger.error("未能生成报告文本")
                all_results.append({"status": "error", "message": "未能生成报告文本", "date": fetch_end_time.strftime("%Y-%m-%d")})
        except Exception as e:
            logger.error(f"运行智能体时出错: {str(e)}")
            traceback.print_exc()
            all_results.append({"status": "error", "message": str(e), "date": fetch_end_time.strftime("%Y-%m-%d")})
    return all_results

if __name__ == "__main__":
    days = 3
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    # 格式化时间字符串（仅用于日志显示）
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    logger.info(f"分析时间范围：{start_time_str} 至 {end_time_str}")
    
    # 调用智能体（传入 datetime 对象）
    result = run_agent(start_time, end_time)
    
    if result.get("status") == "success":
        logger.info("[成功] 金融舆情分析报告生成成功")
        logger.info(f"报告存储路径: {result.get('storage_path', '未知')}")
    else:
        logger.error(f"[失败] 金融舆情分析报告生成失败: {result.get('message', '未知错误')}")
        
