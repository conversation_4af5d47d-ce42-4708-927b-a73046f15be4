# app/routers/users.py
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from typing import List, Optional, Dict, Any
from ..models.user import User, UserCreate, UserUpdate, UserResponse ,MyUserUpdate  # 确保你有一个用户模型
from datetime import datetime
import hashlib
from ..db.mongodb import db
from app.utils.logging_config import setup_logging, get_logger
from ..utils.auth import verify_token

# 设置日志配置
setup_logging()
logger = get_logger(__name__)

router = APIRouter()

# 获取所有用户，支持分页
@router.get("/api/users", response_model=Dict[str, Any])
async def get_users(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,  # 支持按名称检索
    phone: Optional[str] = None,  # 支持按电话检索
    group_name: Optional[int] = None,  # 支持按组名检索
    role_name: Optional[int] = None,  # 支持按角色名检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配，忽略大小写
    if phone:
        query["phone"] = {"$regex": phone, "$options": "i"}  # 支持电话的模糊匹配
    if group_name:
        query["group_id"] =  group_name# 支持组名的模糊匹配
    if role_name:
        query["role_id"] = role_name # 支持角色名的模糊匹配
    print(query)
    users = await db["users"].find(query, {"_id": 0,
                                        "id": 1,
                                        "name": 1, 
                                        "phone": 1,
                                        "group_id": 1,
                                        "group_name": 1, 
                                        "is_active": 1,
                                        "created_at": 1,
                                        "role_name":1}).skip(skip).limit(pageSize).to_list(pageSize)
    
    total = await db["users"].count_documents(query)
    return {
        "data": users,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新用户
@router.post("/api/users", response_model=UserResponse)
async def add_user(user: UserCreate, current_user: dict = Depends(verify_token)):
    last_user = await db["users"].find_one(sort=[("id", -1)])
    new_id = (last_user["id"] + 1) if last_user else 1

    # 查询组名
    group = await db["groups"].find_one({"id": user.group_id})
    group_name = group["name"] if group else "未知组"

    # 查询权限名
    role = await db["roles"].find_one({"id": user.role_id})
    role_name = role["name"] if role else "未知权限"

    # 检查用户是否已存在
    existing_user = await db["users"].find_one({"phone": user.phone})
    if existing_user:
        raise HTTPException(status_code=400, detail="User already exists")

    # 创建新用户
    new_user = {
        "id": new_id,
        "userid": hashlib.md5((str(datetime.now()) + user.phone).encode()).hexdigest(),
        "name": user.name,
        "phone": user.phone,
        "password": hashlib.md5(user.password.encode()).hexdigest(),  # 确保密码已加密
        "is_active": True,  # 默认激活
        "is_superuser": False,  # 默认不是超级用户
        "created_at": datetime.now(),
        "created_by": current_user["id"],
        "group_id": user.group_id,  # 使用传入的 group
        "group_name": group_name,  # 添加组名
        "role_id": user.role_id,  # 使用传入的 role
        "role_name": role_name,  # 添加权限名
        "avatar" : "/avatar/default.jpeg",
    }
    await db["users"].insert_one(new_user)
    return UserResponse(**new_user)

# 更新用户
@router.put("/api/users/{user_id}", response_model=UserResponse)
async def update_user(user_id: int, user: UserUpdate, current_user: dict = Depends(verify_token)):
    result = await db["users"].update_one({"id": user_id}, {"$set": user.dict(exclude_unset=True)})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    updated_user = await db["users"].find_one({"id": user_id})
    return UserResponse(**updated_user)



@router.put("/api/MyUserUpdate",response_model=Dict[str, Any])
async def update_user( user: MyUserUpdate, current_user: dict = Depends(verify_token)):
    # 获取现有用户数据
    print(current_user)
    existing_user = await db["users"].find_one({"id": current_user["id"]})
    if not existing_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # 保留原有的 created_by 值
    update_data = user.dict(exclude_unset=True)
    
    result = await db["users"].update_one(
        {"id": current_user["id"]}, 
        {"$set": update_data}
    )
    updated_user = await db["users"].find_one({"id": current_user["id"]})     
    del updated_user["_id"]    
    return {
        "success": True,
        "message": "用户信息更新成功",
        "user": updated_user
    }


@router.get("/api/user", response_model=UserResponse)
async def update_user( current_user: dict = Depends(verify_token)):

    updated_user = await db["users"].find_one({"id": current_user.id})
    return UserResponse(**updated_user)

# 删除用户
@router.delete("/api/users/{user_id}", response_model=Dict[str, Any])
async def delete_user(user_id: int, current_user: dict = Depends(verify_token)):
    user = await db["users"].find_one({"id": user_id})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    result = await db["users"].delete_one({"id": user_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    return {"id": user_id, "success": True, "message": "User successfully deleted"}

# Change user status
@router.post("/api/users/changeStatus", response_model=Dict[str, Any])
async def change_user_status(data: Dict[str, int]):
    try:
        user_id = data.get("id")
        status = data.get("status")
        
        if user_id is None or status is None:
            raise ValueError("缺少必要的参数 'id' 或 'status'")

        logger.info(f"user_id: {user_id}, status: {status}")
        is_active_bool = True if status == 1 else False
        logger.info(f"user_id: {user_id}, is_active: {is_active_bool}")

        result = await db["users"].update_one({"id": user_id}, {"$set": {"is_active": is_active_bool}})
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="未找到用户")
        
        user = await db["users"].find_one(
            {"id": user_id}, 
            {"_id": 0, "id": 1, "name": 1, "phone": 1, "group_id": 1, "group_name": 1, "created_at": 1, "is_active": 1}
        )
        return {"success": True, "message": "用户状态已成功更新", "user": user}
    except ValueError as ve:
        return {"success": False, "message": str(ve)}
    except Exception as e:
        logger.error(f"更新用户状态时发生错误: {str(e)}")
        return {"success": False, "message": f"更新用户状态时发生错误: {str(e)}"}

@router.put("/api/changePassword", response_model=Dict[str, Any])
async def change_password(
    old_password: str = Body(..., embed=True),
    new_password: str = Body(..., embed=True),
    current_user: dict = Depends(verify_token)
):
    # 获取现有用户数据
    existing_user = await db["users"].find_one({"id": current_user["id"]})
    if not existing_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 验证旧密码
    old_password_hash = hashlib.md5(old_password.encode()).hexdigest()
    if existing_user["password"] != old_password_hash:
        raise HTTPException(status_code=400, detail="旧密码不正确")
    
    # 更新新密码
    new_password_hash = hashlib.md5(new_password.encode()).hexdigest()
    result = await db["users"].update_one(
        {"id": current_user["id"]}, 
        {"$set": {"password": new_password_hash}}
    )
    
    return {
        "success": True,
        "message": "密码修改成功"
    }


# 用户授权流程访问权限
@router.post("/api/authflow", response_model=Dict[str, Any])
async def auth_flow(
    user_ids: List[int] = Body(...),
    flow_id: str = Body(...),
    flow_name: str = Body(...),
    flow_desc: str = Body(...),
    api_key: str = Body(...),
    current_user: dict = Depends(verify_token)
):
    try:
        # 先删除该流程的所有授权记录
        await db["flows"].delete_many({"flow_id": flow_id})
        
        # 为每个用户创建新的授权记录
        flow_auths = []
        for user_id in user_ids:
            flow_auth = {
                "user_id": user_id,
                "id": flow_id,
                "name": flow_name,
                "description": flow_desc,
                "api_key": api_key,
                "created_at": datetime.now(),
                "created_by": current_user["id"]
            }
            flow_auths.append(flow_auth)
        
        if flow_auths:
            await db["flows"].insert_many(flow_auths)
        
        return {
            "success": True,
            "message": "流程授权成功"
        }
    except Exception as e:
        logger.error(f"流程授权失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"流程授权失败: {str(e)}")

# 获取当前用户已授权的流程列表
@router.get("/api/flowlist", response_model=Dict[str, Any])
async def get_flow_list(current_user: dict = Depends(verify_token)):
    try:
        flows = await db["flows"].find(
            {"user_id": current_user["id"]}, 
            {"_id": 0}
        ).to_list(None)
        
        return {
            "success": True,
            "data": flows,
            "total": len(flows)
        }
    except Exception as e:
        logger.error(f"获取流程列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取流程列表失败: {str(e)}")

