"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8258],{58258:function(e,n,t){t.d(n,{Bp:function(){return _},CH:function(){return b},DY:function(){return p},E:function(){return F},E4:function(){return y},IV:function(){return N},L9:function(){return D},Pq:function(){return A},Rj:function(){return ee},SJ:function(){return Q},SS:function(){return H},SZ:function(){return U},Vk:function(){return B},_I:function(){return g},au:function(){return j},bV:function(){return f},dx:function(){return q},e_:function(){return T},ki:function(){return L},l$:function(){return O},ln:function(){return W},pj:function(){return l},vn:function(){return Z},yN:function(){return w}});var r=t(15009),a=t.n(r),u=t(99289),o=t.n(u),c=t(78158),i=t(10981);function p(){return s.apply(this,arguments)}function s(){return(s=o()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/embeddingsList",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,n,t,r,a,u){return d.apply(this,arguments)}function d(){return(d=o()(a()().mark((function e(n,t,r,u,o,i){var p,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p=new FormData,console.log("KnowledgeId",n),p.append("knowledgeId",n),p.append("ocr",t.toString()),p.append("parentId",u),p.append("parserType",o),p.append("files_source_type",i||""),r.forEach((function(e){p.append("files",e)})),e.prev=8,e.next=11,(0,c.N)("/api/updateKnowledgeFile",{method:"POST",data:p,requestType:"form"});case 11:return e.abrupt("return",e.sent);case 14:if(e.prev=14,e.t0=e.catch(8),404!==(null===(s=e.t0.response)||void 0===s?void 0:s.status)){e.next=19;break}throw console.error("上传端点不存在 (404):",e.t0),new Error("上传服务不可用，请联系管理员");case 19:throw console.error("文件上传失败:",e.t0),e.t0;case 21:case"end":return e.stop()}}),e,null,[[8,14]])})))).apply(this,arguments)}function l(e,n,t,r,a,u){return h.apply(this,arguments)}function h(){return(h=o()(a()().mark((function e(n,t,r,u,o,i){var p,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p=new FormData,console.log("KnowledgeId",n),p.append("knowledgeId",n),p.append("ocr",t.toString()),p.append("parentId",u),p.append("parserType",o),p.append("files_source_type",i||""),r.forEach((function(e){p.append("files",e)})),e.prev=8,e.next=11,(0,c.N)("/api/updateKnowledgeFileFromChat",{method:"POST",data:p,requestType:"form"});case 11:return e.abrupt("return",e.sent);case 14:if(e.prev=14,e.t0=e.catch(8),404!==(null===(s=e.t0.response)||void 0===s?void 0:s.status)){e.next=19;break}throw console.error("上传端点不存在 (404):",e.t0),new Error("上传服务不可用，请联系管理员");case 19:throw console.error("文件上传失败:",e.t0),e.t0;case 21:case"end":return e.stop()}}),e,null,[[8,14]])})))).apply(this,arguments)}function w(e){return m.apply(this,arguments)}function m(){return m=o()(a()().mark((function e(n){var t,r,u=arguments;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(u.length>1&&void 0!==u[1])||u[1],r=u.length>2?u[2]:void 0,e.abrupt("return",(0,c.N)("/api/knowledge_file_preview/".concat(n),{method:"GET",params:{download:t},responseType:"blob"}).then((function(e){var n,a=null===(n=e.headers)||void 0===n?void 0:n.get("content-disposition"),u=r||"download";if(!r&&a){var o=a.match(/filename\*=UTF-8''([^;]+)/i);if(o)u=decodeURIComponent(o[1]);else{var c=a.match(/filename="([^"]+)"/i);c&&(u=c[1])}}if(t){var i=new Blob([e],{type:e.type}),p=window.URL.createObjectURL(i),s=document.createElement("a");s.href=p,s.download=u,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(p),document.body.removeChild(s)}else{var f=new Blob([e],{type:e.type}),d=window.URL.createObjectURL(f),l=document.createElement("iframe");l.src=d,l.style.display="none",document.body.appendChild(l),window.URL.revokeObjectURL(d),document.body.removeChild(l)}return e})));case 3:case"end":return e.stop()}}),e)}))),m.apply(this,arguments)}function y(e){return v.apply(this,arguments)}function v(){return(v=o()(a()().mark((function e(n){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="/api/knowledge_file_preview/".concat(n),e.abrupt("return",fetch(t));case 2:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e,n){return k.apply(this,arguments)}function k(){return(k=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base/".concat(n),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return x.apply(this,arguments)}function x(){return(x=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/source_files/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var T=function(){var e=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch("/api/renameFile/".concat(n),{method:"POST",body:JSON.stringify({newName:t}),headers:{"Content-Type":"application/json"}});case 2:if(e.sent.ok){e.next=5;break}throw new Error("重命名失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}();function _(e){return E.apply(this,arguments)}function E(){return(E=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e,n){return S.apply(this,arguments)}function S(){return(S=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base/".concat(n),{method:"PUT",data:{tags:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e,n,t,r,a){return P.apply(this,arguments)}function P(){return(P=o()(a()().mark((function e(n,t,r,u,o){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeFolder",{method:"POST",data:{action:n,folder_name:t,knowledgeId:r,parentId:u,folder_id:o}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e,n){return I.apply(this,arguments)}function I(){return(I=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeTags",{method:"POST",data:{file_id:n,tags:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e,n){return C.apply(this,arguments)}function C(){return(C=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeMove",{method:"POST",data:{file_id:n,parent_id:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e,n){return R.apply(this,arguments)}function R(){return(R=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeForbid",{method:"POST",data:{file_id:n,forbidden:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function F(e,n,t,r,a,u,o){return K.apply(this,arguments)}function K(){return(K=o()(a()().mark((function e(n,t,r,u,o,i,p){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/knowledge_folder_tree/".concat(n),{method:"POST",data:{name:t,tags:r,only_folder:u,forbidden:o,data_type:i,flg:p}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return G.apply(this,arguments)}function G(){return(G=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/source_files/reset/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e,n,t,r,a,u){return z.apply(this,arguments)}function z(){return(z=o()(a()().mark((function e(n,t,r,u,o,i){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/".concat(n),{method:"GET",params:{page:t,pageSize:r,file_name:u,answer:o,question:i}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return V.apply(this,arguments)}function V(){return(V=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function A(e){return J.apply(this,arguments)}function J(){return(J=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/batchDelete",{method:"POST",data:{ids:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function H(e){return M.apply(this,arguments)}function M(){return(M=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/retrieval/search",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e){return Y.apply(this,arguments)}function Y(){return(Y=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/wisegraph/entities/fuzzy-search",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e,n){return $.apply(this,arguments)}function $(){return($=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("http://192.168.0.125:8800/api/wisegraph/knowledge-base/".concat(n,"/entity/subgraph"),{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Q(e){return X.apply(this,arguments)}function X(){return(X=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ee(e){return ne.apply(this,arguments)}function ne(){return(ne=o()(a()().mark((function e(n){var t,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/source_files/".concat(n),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat((0,i.bW)())}});case 3:if((t=e.sent).ok){e.next=9;break}return e.next=7,t.json();case 7:throw r=e.sent,new Error(r.detail||"删除文件失败");case 9:return e.next=11,t.json();case 11:return e.abrupt("return",e.sent);case 14:throw e.prev=14,e.t0=e.catch(0),console.error("删除文件API错误:",e.t0),e.t0;case 18:case"end":return e.stop()}}),e,null,[[0,14]])})))).apply(this,arguments)}}}]);