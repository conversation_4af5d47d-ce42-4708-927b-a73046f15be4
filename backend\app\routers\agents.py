import json
import traceback
from collections.abc import AsyncGenerator
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4
from ..utils.auth import verify_token
from bson.objectid import ObjectId
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command
# from langsmith import Client as LangsmithClient
from pydantic import BaseModel

from agent.agents import CHAT_AGENT, DEFAULT_AGENT, get_agent
from agent.utils import (convert_message_content_to_string,
                            langchain_to_chat_message, remove_tool_calls)
from db.mongodb import db

from app.models.system_app_setting import SystemAppSettingModel
from app.models.message import Message
from app.models.chat import ChatResponse
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter()


async def message_generator(
    kwargs: Dict[str, Any],
) -> AsyncGenerator[str, None]:
    """
    Generate a stream of messages from the agent in OpenAI-compatible format.

    Args:
        kwargs: Dictionary containing input messages and configuration
        agent_id: ID of the agent to use (defaults to DEFAULT_AGENT)

    Returns:
        AsyncGenerator yielding SSE formatted messages
    """
    config = kwargs.get("config", RunnableConfig())
    logger.info(f"config: {config}")


    # 生成一个新的run_id
    run_id = uuid4()

    logger.info(f"kwargs: {kwargs}")
    logger.info(f"run_id: {run_id}")

    # 提取输入消息和配置
    input_messages = kwargs.get("input", {}).get("messages", [])
    stream_tokens = config.get("stream_tokens", True)
    agent_type = config.get("agent_type", DEFAULT_AGENT)
    agent_params = config.get("agent_params", None)
    logger.info(f"========================启用智能体: {config.get('agent_type')}")
    agent: CompiledStateGraph = get_agent(agent_type)


    # 根据role区分消息类型
    processed_messages = []
    for msg in input_messages:
        if isinstance(msg, dict) and "role" in msg and "content" in msg:
            # 处理OpenAI格式的消息
            role = msg["role"]
            content = msg["content"]
            if role == "user":
                processed_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                processed_messages.append(AIMessage(content=content))
            # 可以添加更多角色的处理，如system等
        else:
            # 处理已经是LangChain消息对象的情况
            processed_messages.append(msg)

    messages_list = [{
        'role': msg.type,
        'content': convert_message_content_to_string(msg.content)
    } for msg in processed_messages]
    logger.info("已处理的消息列表:")
    for idx, msg in enumerate(messages_list, 1):
        logger.info(f"  消息 {idx} ：角色: {msg['role']}")
        logger.info(f"  内容: {msg['content']}")
        logger.info(f"----------------------------")
    logger.info(f"===============================================")
    # 创建一个唯一的会话ID
    chat_id = f"chatcmpl-{str(uuid4())[:10]}"
    created_timestamp = int(datetime.now().timestamp())
    model_name = config.get("configurable", {}).get("model", "default-model")

    # 发送角色信息作为第一个块
    first_chunk = {
        "id": chat_id,
        "object": "chat.completion.chunk",
        "created": created_timestamp,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "delta": {"role": "assistant"},
                "logprobs": None,
                "finish_reason": None
            }
        ]
    }
    yield f"data: {json.dumps(first_chunk)}\n\n"

    # 跟踪是否已经发送了任何内容
    has_sent_content = False

    # 准备运行参数

    thread_id = config.get("configurable", {}).get("thread_id", str(uuid4()))
    logger.info(f"使用智能体: {config.get('agent_type')}")

    # 根据不同的智能体类型设置不同的运行参数
    run_id = str(uuid4())
    logger.info(f"agent_type: {agent_type}")

    if agent_type == BASE_RAG_AGENT:
        logger.info(f"=================BASE_RAG_AGENT=========================")
        # 对于RAG智能体，需要添加知识库ID
        knowledge_base_id = agent_params.get("knowledge_base_id", "")
        logger.info(f"知识库ID: {knowledge_base_id}")


        run_kwargs = {
            "input": {
                "messages": processed_messages,
                "agent_params": agent_params  # 也传递整个 agent_params
            },
            "config": RunnableConfig(
                configurable={
                    "model": model_name,
                    "thread_id": thread_id,
                },
                run_id=run_id
            ),
        }
    else:
        logger.info(f"=================DEFAULT_AGENT=========================")
        # 对于默认的聊天机器人，使用标准参数
        run_kwargs = {
            "input": {"messages": processed_messages},
            "config": RunnableConfig(
                configurable={
                    "model": model_name,
                    "thread_id": thread_id  # 确保传递thread_id
                },
                run_id=run_id
            ),
        }
    logger.info(f"===========> run_kwargs: {run_kwargs}")

    # 跟踪已处理的流ID
    processed_stream_ids = set()

    async for event in agent.astream_events(**run_kwargs, version="v2"):
        if not event:
            continue

        # 提取流ID并设置流式处理
        if (event["event"] == "on_chain_end" and
            "stream_id" in event["data"].get("output", {})):

            stream_id = event["data"]["output"]["stream_id"]

            # 跳过已处理的流ID
            if stream_id in processed_stream_ids:
                continue

            processed_stream_ids.add(stream_id)

            if stream_id:
                logger.info(f"处理新的流ID: {stream_id}")
                # 开始流式输出
                async for content_chunk in stream_manager.stream_content(stream_id):
                    if content_chunk:
                        chunk = {
                            "id": chat_id,
                            "object": "chat.completion.chunk",
                            "created": created_timestamp,
                            "model": model_name,
                            "choices": [
                                {
                                    "index": 0,
                                    "delta": {"content": content_chunk},
                                    "logprobs": None,
                                    "finish_reason": None
                                }
                            ]
                        }
                        yield f"data: {json.dumps(chunk)}\n\n"
                        has_sent_content = True
                continue

        # 处理完整的流式令牌 (保持现有代码)
        if (
            event["event"] == "on_chat_model_stream"
            and stream_tokens
        ):
            content = remove_tool_calls(event["data"]["chunk"].content)
            if content:
                has_sent_content = True
                chunk = {
                    "id": chat_id,
                    "object": "chat.completion.chunk",
                    "created": created_timestamp,
                    "model": model_name,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": convert_message_content_to_string(content)},
                            "logprobs": None,
                            "finish_reason": None
                        }
                    ]
                }
                yield f"data: {json.dumps(chunk)}\n\n"
            continue

        # 处理完整消息（可选，如果您想在流结束时发送完整消息）
        new_messages = []
        if (
            event["event"] == "on_chain_end"
            and any(t.startswith("graph:step:") for t in event.get("tags", []))
        ):
            if isinstance(event["data"]["output"], Command):
                new_messages = event["data"]["output"].update.get("messages", [])
            elif "messages" in event["data"]["output"]:
                new_messages = event["data"]["output"]["messages"]

        # 处理自定义事件
        if event["event"] == "on_custom_event" and "custom_data_dispatch" in event.get("tags", []):
            new_messages = [event["data"]]

        # 我们不在这里发送完整消息，因为OpenAI格式是逐个令牌发送的
        # 但我们可以记录它们用于调试或其他目的
        for message in new_messages:
            try:
                chat_message = langchain_to_chat_message(message)
                logger.debug(f"Processed message: {chat_message.type}")
            except Exception as e:
                logger.error(f"Error parsing message: {e}")
                # 不要发送错误消息，因为这不符合OpenAI格式
                continue

    # 发送结束标记
    final_chunk = {
        "id": chat_id,
        "object": "chat.completion.chunk",
        "created": created_timestamp,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "logprobs": None,
                "finish_reason": "stop"
            }
        ]
    }
    yield f"data: {json.dumps(final_chunk)}\n\n"
    yield "data: [DONE]\n\n"

async def robot_message_generator(
    kwargs: Dict[str, Any],
    id: str
) -> AsyncGenerator[str, None]:
    """
    Generate a stream of messages from the agent in OpenAI-compatible format.

    Args:
        kwargs: Dictionary containing input messages and configuration
        id: 会话ID，用于标识此次对话

    Returns:
        AsyncGenerator yielding SSE formatted messages
    """
    config = kwargs.get("config", RunnableConfig())
    logger.info(f"config: {config}")

    # 生成一个新的run_id
    run_id = uuid4()

    logger.info(f"kwargs: {kwargs}")
    logger.info(f"run_id: {run_id}")

    # 提取输入消息和配置
    input_messages = kwargs.get("input", {}).get("messages", [])
    stream_tokens = config.get("stream_tokens", True)
    agent_type = config.get("agent_type", DEFAULT_AGENT)
    agent_params = config.get("agent_params", None)
    logger.info(f"========================启用智能体: {config.get('agent_type')}")
    agent: CompiledStateGraph = get_agent(agent_type)

    # 根据role区分消息类型
    processed_messages = []
    for msg in input_messages:
        if isinstance(msg, dict) and "role" in msg and "content" in msg:
            # 处理OpenAI格式的消息
            role = msg["role"]
            content = msg["content"]
            if role == "user":
                processed_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                processed_messages.append(AIMessage(content=content))
            # 可以添加更多角色的处理，如system等
        else:
            # 处理已经是LangChain消息对象的情况
            processed_messages.append(msg)

    messages_list = [{
        'role': msg.type,
        'content': convert_message_content_to_string(msg.content)
    } for msg in processed_messages]
    logger.info("已处理的消息列表:")
    for idx, msg in enumerate(messages_list, 1):
        logger.info(f"  消息 {idx} ：角色: {msg['role']}")
        logger.info(f"  内容: {msg['content']}")
        logger.info(f"----------------------------")
    logger.info(f"===============================================")

    # 创建一个唯一的会话ID
    chat_id = id
    created_timestamp = int(datetime.now().timestamp())
    model_name = "deepseek" # 默认使用chatMax作为模型名称

    # 消息计数器，用于生成唯一的消息ID
    message_counter = 1

    # 发送角色信息作为第一个块
    first_chunk = {
        "id": chat_id,
        "created": created_timestamp,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "delta": {"role": "assistant", "content": ""},
                "finish_reason": None
            }
        ]
    }
    logger.info(f"first_chunk: {first_chunk}")
    yield f"id: test_id_{message_counter}\ndata: {json.dumps(first_chunk)}\n\n"
    message_counter += 1

    # 跟踪是否已经发送了任何内容
    has_sent_content = False

    # 准备运行参数
    thread_id = config.get("configurable", {}).get("thread_id", str(uuid4()))
    logger.info(f"使用智能体: {config.get('agent_type')}")

    # 根据不同的智能体类型设置不同的运行参数
    run_id = str(uuid4())
    logger.info(f"agent_type: {agent_type}")

    if agent_type == BASE_RAG_AGENT:
        logger.info(f"=================BASE_RAG_AGENT=========================")
        # 对于RAG智能体，需要添加知识库ID
        knowledge_base_id = agent_params.get("knowledge_base_id", "")
        logger.info(f"知识库ID: {knowledge_base_id}")

        run_kwargs = {
            "input": {
                "messages": processed_messages,
                "agent_params": agent_params  # 也传递整个 agent_params
            },
            "config": RunnableConfig(
                configurable={
                    "model": model_name,
                    "thread_id": thread_id,
                },
                run_id=run_id
            ),
        }
    else:
        logger.info(f"=================DEFAULT_AGENT=========================")
        # 对于默认的聊天机器人，使用标准参数
        run_kwargs = {
            "input": {"messages": processed_messages},
            "config": RunnableConfig(
                configurable={
                    "model": model_name,
                    "thread_id": thread_id  # 确保传递thread_id
                },
                run_id=run_id
            ),
        }
    logger.info(f"===========> run_kwargs: {run_kwargs}")

    # 跟踪已处理的流ID
    processed_stream_ids = set()

    async for event in agent.astream_events(**run_kwargs, version="v2"):
        if not event:
            continue

        # 提取流ID并设置流式处理
        if (event["event"] == "on_chain_end" and
            "stream_id" in event["data"].get("output", {})):

            stream_id = event["data"]["output"]["stream_id"]

            # 跳过已处理的流ID
            if stream_id in processed_stream_ids:
                continue

            processed_stream_ids.add(stream_id)

            if stream_id:
                logger.info(f"处理新的流ID: {stream_id}")
                # 开始流式输出
                async for content_chunk in stream_manager.stream_content(stream_id):
                    if content_chunk:
                        chunk = {
                            "id": chat_id,
                            "created": created_timestamp,
                            "model": model_name,
                            "choices": [
                                {
                                    "index": 0,
                                    "delta": {"content": content_chunk},
                                    "finish_reason": None
                                }
                            ]
                        }
                        yield f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
                        message_counter += 1
                        has_sent_content = True
                continue

        # 处理完整的流式令牌 (保持现有代码)
        if (
            event["event"] == "on_chat_model_stream"
            and stream_tokens
        ):
            content = remove_tool_calls(event["data"]["chunk"].content)
            if content:
                has_sent_content = True
                chunk = {
                    "id": chat_id,
                    "created": created_timestamp,
                    "model": model_name,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": convert_message_content_to_string(content)},
                            "finish_reason": None
                        }
                    ]
                }
                yield f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
                message_counter += 1
            continue

        # 处理完整消息（可选，如果您想在流结束时发送完整消息）
        new_messages = []
        if (
            event["event"] == "on_chain_end"
            and any(t.startswith("graph:step:") for t in event.get("tags", []))
        ):
            if isinstance(event["data"]["output"], Command):
                new_messages = event["data"]["output"].update.get("messages", [])
            elif "messages" in event["data"]["output"]:
                new_messages = event["data"]["output"]["messages"]

        # 处理自定义事件
        if event["event"] == "on_custom_event" and "custom_data_dispatch" in event.get("tags", []):
            new_messages = [event["data"]]

        # 我们不在这里发送完整消息，因为OpenAI格式是逐个令牌发送的
        # 但我们可以记录它们用于调试或其他目的
        for message in new_messages:
            try:
                chat_message = langchain_to_chat_message(message)
                logger.debug(f"Processed message: {chat_message.type}")
            except Exception as e:
                logger.error(f"Error parsing message: {e}")
                # 不要发送错误消息，因为这不符合OpenAI格式
                continue

    # 发送结束标记
    final_chunk = {
        "id": chat_id,
        "created": created_timestamp,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }
    yield f"id: test_id_{message_counter}\ndata: {json.dumps(final_chunk)}\n\n"

# 添加ChatCompletionRequest类定义
class ChatCompletionRequest(BaseModel):
    id: Optional[str] = None
    app_info: Optional[str] = None
    conversation_id: Optional[str] = None
    meta: Optional[Dict[str, str]] = None
    messages: Optional[List[Dict[str, str]]] = None
    stream: Optional[bool] = True

@router.post(
    "/v1/agents/completions",
    response_class=StreamingResponse,
)
async def agents_completions(
    request: ChatResponse,
    current_user: dict = Depends(verify_token)):

    try:
        # 基础参数
        logger.info(f"收到聊天请求: {request}")
        print(request)
        extra = request.extra
        app_info = request.app_info
        messages = request.messages
        stream = request.stream or True
        conversation_id = request.conversation_id
        meta = request.meta


        # 获取系统应用信息
        system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": app_info})
        if not system_app_info:
            raise HTTPException(status_code=404, detail="App info not found")

        # 获取agent_id
        agent_id = system_app_info.agent_id
        logger.info(f"agent_id: {agent_id}")

        # 创建新的用户消息
        new_user_message = Message(
            _id=str(ObjectId()),
            conversation_id=conversation_id,
            message_id=messages[-1]['id'],
            meta_data=messages[-1].get('meta', {}),
            extra=extra.get('extra', {}),
            user_id=request.user_id,
            user_name=request.user_name,
            role=messages[-1]['role'],
            content=messages[-1]['content'],
            created_at=datetime.now(),
            app_info=app_info,
            token_count=0,  # 这里可以添加计算token的逻辑
            price=0.0  # 这里可以添加计算价格的逻辑
        )

        # 将 MongoEngine 实例转换为字典
        new_user_message_dict = new_user_message.to_mongo().to_dict()
        await db["messages"].insert_one(new_user_message_dict)

        # 获取系统应用参数
        system_app_params = system_app_info.get('params', {})
        agent_type = system_app_params.get('agent_type', DEFAULT_AGENT)
        agent_params = system_app_params.get('agent_params', {})

        # 从请求头中提取api_key
        api_key = credentials.credentials
        logger.info(f"api_key: {api_key}")
        # 根据api_key获取agent_id
        api_key_doc = await get_agent_by_api_key(api_key)
        agent_type = api_key_doc.get('agent_type')
        


        # 从请求头中提取api_key
        api_key = credentials.credentials
        logger.info(f"api_key: {api_key}")
        # 根据api_key获取agent_id
        api_key_doc = await get_agent_by_api_key(api_key)
        agent_type = api_key_doc.get('agent_type')
        # 获取agent_params并处理ObjectId
        agent_params = api_key_doc.get('agent_params')
        # 如果agent_params中包含ObjectId类型的值，将其转换为字符串
        if agent_params:
            for key, value in agent_params.items():
                if isinstance(value, ObjectId):
                    agent_params[key] = str(value)


        # logger.info(f"agent_type: {agent_type}")
        # logger.info(f"agent_params: {agent_params}")
        # logger.info(f"request: {request}")

        # 提取模型和消息
        # model_name = request.model
        messages = request.messages
        # 格式化打印对话内容
        # formatted_messages = json.dumps(messages, indent=2, ensure_ascii=False)
        # logger.info(f"对话内容:\n{formatted_messages}")
        # logger.info(f"model_name: {model_name}")

        # 确保流式输出标志设置为True
        stream_tokens = request.stream if request.stream is not None else True  # 假设总是需要流式输出

        # 生成一个thread_id
        thread_id = str(uuid4())

        # 构建输入参数
        kwargs = {
            "input": {"messages": messages},  # 直接传递原始消息，让message_generator处理
            "config": RunnableConfig(
                configurable={
                    # "model": model_name,
                    "thread_id": thread_id  # 添加thread_id到configurable字典中
                },
                run_id=uuid4(),
                agent_type=agent_type,
                agent_params=agent_params,
                stream_tokens=stream_tokens
            ),
        }

        return StreamingResponse(
            robot_message_generator(kwargs,request.id),
            media_type="text/event-stream",
            headers={"Content-Type": "text/event-stream"}
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")

