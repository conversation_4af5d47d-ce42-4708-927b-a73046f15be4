"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6638],{27363:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"}},47389:function(e,t,n){var r=n(1413),i=n(67294),a=n(27363),c=n(91146),l=function(e,t){return i.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},o=i.forwardRef(l);t.Z=o},18954:function(e,t,n){n.r(t);var r=n(5574),i=n.n(r),a=n(67294),c=n(97131),l=n(55102),o=n(42075),s=n(83622),d=n(67839),u=n(78818),f=n(47389),h=n(85893),m=l.Z.Search;t.default=function(){var e=(0,a.useState)(""),t=i()(e,2),n=(t[0],t[1]),r=[{title:"标题",dataIndex:"title",key:"title",render:function(e){return(0,h.jsxs)(o.Z,{children:[(0,h.jsx)("span",{style:{color:"#1890ff"},children:e}),(0,h.jsx)(f.Z,{style:{color:"#faad14",fontSize:"12px"}})]})}},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:200,align:"center"},{title:"操作",key:"action",width:150,align:"center",render:function(){return(0,h.jsxs)(o.Z,{children:[(0,h.jsx)(s.ZP,{type:"link",style:{color:"#ff4d4f",padding:0},children:"编辑"}),(0,h.jsx)(s.ZP,{type:"link",style:{color:"#ff4d4f",padding:0},children:"删除"})]})}}];return(0,h.jsx)(c._z,{children:(0,h.jsxs)("div",{style:{padding:"20px",backgroundColor:"#f5f5f5",minHeight:"100vh"},children:[(0,h.jsx)("div",{style:{marginBottom:"20px"},children:(0,h.jsx)(m,{placeholder:"请输入关键字",allowClear:!0,enterButton:(0,h.jsx)(s.ZP,{type:"primary",style:{backgroundColor:"#ff6b35"},children:"搜索"}),size:"large",onSearch:function(e){n(e),console.log("搜索:",e)},onChange:function(e){return n(e.target.value)},style:{maxWidth:400}})}),(0,h.jsx)(d.Z,{dataSource:[{key:"1",title:"【产品发布】AI智能助理正式上线！",createTime:"2024-05-16 18:01:42"},{key:"2",title:"【门户生活的内容之一——智能助理的AI智能平台】",createTime:"2024-05-15 09:55:38"}],columns:r,pagination:!1,style:{backgroundColor:"white"},size:"large"}),(0,h.jsx)("div",{style:{textAlign:"center",marginTop:"20px"},children:(0,h.jsx)(u.Z,{current:1,total:2,pageSize:10,showSizeChanger:!1,showQuickJumper:!1,simple:!0})})]})})}},64019:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(73935);function i(e,t,n,i){var a=r.unstable_batchedUpdates?function(e){r.unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,a,i),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,a,i)}}}},79370:function(e,t,n){n.d(t,{G:function(){return a}});var r=n(98924),i=function(e){if((0,r.Z)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some((function(e){return e in n.style}))}return!1};function a(e,t){return Array.isArray(e)||void 0===t?i(e):function(e,t){if(!i(e))return!1;var n=document.createElement("div"),r=n.style[e];return n.style[e]=t,n.style[e]!==r}(e,t)}}}]);