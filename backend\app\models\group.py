from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class Group(Base):
    __tablename__ = 'groups'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, nullable=False)
    deletable = Column(Boolean, default=True)  # 新增字段
    parent_id = Column(Integer, default=1)  # 增加父节点id，默认为1

    def __repr__(self):
        return f"<Group(name={self.name}, description={self.description})>"

class GroupCreate(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None

    class Config:
        from_attributes = True

class GroupResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    created_at: datetime
    created_by: int
    deletable: bool  # 新增字段

    class Config:
        from_attributes = True